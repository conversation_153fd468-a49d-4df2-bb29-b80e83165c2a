<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Practices.Unity.Interception.Configuration</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.AddInterfaceElement">
            <summary>
            Configuration element that lets you specify additional interfaces
            to add when this type is intercepted.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.AddInterfaceElement.SerializeContent(System.Xml.XmlWriter)">
            <summary>
            Write the contents of this element to the given <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <remarks>The caller of this method has already written the start element tag before
            calling this method, so deriving classes only need to write the element content, not
            the start or end tags.</remarks>
            <param name="writer">Writer to send XML content to.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.AddInterfaceElement.GetInjectionMembers(Microsoft.Practices.Unity.IUnityContainer,System.Type,System.Type,System.String)">
            <summary>
            Return the set of <see cref="T:Microsoft.Practices.Unity.InjectionMember"/>s that are needed
            to configure the container according to this configuration element.
            </summary>
            <param name="container">Container that is being configured.</param>
            <param name="fromType">Type that is being registered.</param>
            <param name="toType">Type that <paramref name="fromType"/> is being mapped to.</param>
            <param name="name">Name this registration is under.</param>
            <returns>One or more <see cref="T:Microsoft.Practices.Unity.InjectionMember"/> objects that should be
            applied to the container registration.</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.AddInterfaceElement.TypeName">
            <summary>
            Type of interface to add.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.AddInterfaceElement.Key">
            <summary>
            Each element must have a unique key, which is generated by the subclasses.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.CallHandlerElement">
            <summary>
            Configuration element representing a call handler.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyChildElement">
            <summary>
            Base class for the two children of the Policy element:
            MatchingRuleElement and CallHandlerElement.
            </summary>
            <remarks>
            <para>
            These configuration elements have a required "name" attribute, an optional "type" attribute, and 
            optional child elements &lt;lifetime&gt; and &lt;injection&gt;
            </para>
            <para>
            Elements without a value for the type attribute can only have a value for the name attribute, and 
            indicate that the represented handler or rule is configured elsewhere and that a reference to 
            the given name must be added to the policy to be resolved, while elements with a value for the type
            attribute indicate how the represented handler or rule should be built and can optionally specify
            lifetime management and injection configuration.
            </para>
            <para>
            This element is similar to the <see cref="T:Microsoft.Practices.Unity.Configuration.RegisterElement"/>, except that it does not provide 
            an extension point for arbitrary configuration.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyChildElement.DeserializeElement(System.Xml.XmlReader,System.Boolean)">
            <summary>
            Reads XML from the configuration file.
            </summary>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> that reads from the configuration file.</param>
            <param name="serializeCollectionKey">true to serialize only the collection key properties; otherwise, false.</param>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">The element to read is locked.
            - or -
            An attribute of the current node is not recognized.
            - or -
            The lock status of the current node cannot be determined.  
            </exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyChildElement.SerializeContent(System.Xml.XmlWriter)">
            <summary>
            Write the contents of this element to the given <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <remarks>The caller of this method has already written the start element tag before
            calling this method, so deriving classes only need to write the element content, not
            the start or end tags.</remarks>
            <param name="writer">Writer to send XML content to.</param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyChildElement.Name">
            <summary>
            Name of this item
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyChildElement.TypeName">
            <summary>
            Type that implements this matching rule or call handler.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyChildElement.Injection">
            <summary>
            Injection members that control how this item is created.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyChildElement.Lifetime">
            <summary>
            Lifetime manager for this item.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.CallHandlerElementCollection">
            <summary>
            A collection of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.CallHandlerElement"/>s for configuration.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.CallHandlerElementCollection.GetElementKey(System.Configuration.ConfigurationElement)">
            <summary>
            Gets the element key for a specified configuration element when overridden in a derived class.
            </summary>
            <returns>
            An <see cref="T:System.Object"/> that acts as the key for the specified <see cref="T:System.Configuration.ConfigurationElement"/>.
            </returns>
            <param name="element">The <see cref="T:System.Configuration.ConfigurationElement"/> to return the key for. </param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.CallHandlerElementCollection.OnDeserializeUnrecognizedElement(System.String,System.Xml.XmlReader)">
            <summary>
            Causes the configuration system to throw an exception.
            </summary>
            <returns>
            true if the unrecognized element was deserialized successfully; otherwise, false. The default is false.
            </returns>
            <param name="elementName">The name of the unrecognized element. </param>
            <param name="reader">An input stream that reads XML from the configuration file. </param>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">The element specified in <paramref name="elementName"/> is the &lt;clear&gt; element.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="elementName"/> starts with the reserved prefix "config" or "lock".</exception>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.CallHandlerElementCollection.Item(System.String)">
            <summary>
            Retrieve a call handler element from the collection by name.
            </summary>
            <param name="name">Name to look up.</param>
            <returns>The rule, or null if not in the collection.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.DefaultElement">
            <summary>
            The &lt;default&gt; element that appears inside an &lt;interceptor&gt; element.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorRegistrationElement">
            <summary>
            Base class for the default and key elements that can occur
            inside the &lt;interceptor&gt; element.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorRegistrationElement.SerializeContent(System.Xml.XmlWriter)">
            <summary>
            Write the contents of this element to the given <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <remarks>The caller of this method has already written the start element tag before
            calling this method, so deriving classes only need to write the element content, not
            the start or end tags.</remarks>
            <param name="writer">Writer to send XML content to.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorRegistrationElement.RegisterInterceptor(Microsoft.Practices.Unity.IUnityContainer,Microsoft.Practices.Unity.InterceptionExtension.IInterceptor)">
            <summary>
            Actually register the interceptor against this type.
            </summary>
            <param name="container">Container to configure.</param>
            <param name="interceptor">interceptor to register.</param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorRegistrationElement.TypeName">
            <summary>
            Type name that this interceptor will be registered for.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorRegistrationElement.ResolvedType">
            <summary>
            Return the type object that is resolved from the <see cref="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorRegistrationElement.TypeName"/> property.
            </summary>
            <returns>The type object.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.DefaultElement.RegisterInterceptor(Microsoft.Practices.Unity.IUnityContainer,Microsoft.Practices.Unity.InterceptionExtension.IInterceptor)">
            <summary>
            Actually register the interceptor against this type.
            </summary>
            <param name="container">Container to configure.</param>
            <param name="interceptor">interceptor to register.</param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptionBehaviorElement">
            <summary>
            Configuration element for specifying interception behaviors for a type.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptionBehaviorElement.DeserializeElement(System.Xml.XmlReader,System.Boolean)">
            <summary>
            Reads XML from the configuration file.
            </summary>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> that reads from the configuration file.</param>
            <param name="serializeCollectionKey">true to serialize only the collection key properties; otherwise, false.</param>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">The element to read is locked.
            - or -
            An attribute of the current node is not recognized.
            - or -
            The lock status of the current node cannot be determined.  
            </exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptionBehaviorElement.SerializeContent(System.Xml.XmlWriter)">
            <summary>
            Write the contents of this element to the given <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <remarks>The caller of this method has already written the start element tag before
            calling this method, so deriving classes only need to write the element content, not
            the start or end tags.</remarks>
            <param name="writer">Writer to send XML content to.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptionBehaviorElement.GetInjectionMembers(Microsoft.Practices.Unity.IUnityContainer,System.Type,System.Type,System.String)">
            <summary>
            Return the set of <see cref="T:Microsoft.Practices.Unity.InjectionMember"/>s that are needed
            to configure the container according to this configuration element.
            </summary>
            <param name="container">Container that is being configured.</param>
            <param name="fromType">Type that is being registered.</param>
            <param name="toType">Type that <paramref name="fromType"/> is being mapped to.</param>
            <param name="name">Name this registration is under.</param>
            <returns>One or more <see cref="T:Microsoft.Practices.Unity.InjectionMember"/> objects that should be
            applied to the container registration.</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptionBehaviorElement.TypeName">
            <summary>
            Type of behavior to add.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptionBehaviorElement.Name">
            <summary>
            Name of behavior to resolve.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptionBehaviorElement.IsDefaultForType">
            <summary>
            Should this behavior be configured as a default behavior for this type, or
            specifically for this type/name pair only?
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptionBehaviorElement.Key">
            <summary>
            Each element must have a unique key, which is generated by the subclasses.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptionConfigurationExtension">
            <summary>
            Section extension class used to add the elements needed to configure
            Unity interception to the configuration schema.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptionConfigurationExtension.AddExtensions(Microsoft.Practices.Unity.Configuration.SectionExtensionContext)">
            <summary>
            Add the extensions to the section via the context.
            </summary>
            <param name="context">Context object that can be used to add elements and aliases.</param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptionElement">
            <summary>
            A configuration element that contains the top-level container configuration
            information for interception - handler policies and global interceptor definitions.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptionElement.OnDeserializeUnrecognizedElement(System.String,System.Xml.XmlReader)">
            <summary>
            Gets a value indicating whether an unknown element is encountered during deserialization.
            </summary>
            <returns>
            true when an unknown element is encountered while deserializing; otherwise, false.
            </returns>
            <param name="elementName">The name of the unknown subelement.</param>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> being used for deserialization.</param>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">The element identified by <paramref name="elementName"/> is locked.
            - or -
            One or more of the element's attributes is locked.
            - or -
            <paramref name="elementName"/> is unrecognized, or the element has an unrecognized attribute.
            - or -
            The element has a Boolean attribute with an invalid value.
            - or -
            An attempt was made to deserialize a property more than once.
            - or -
            An attempt was made to deserialize a property that is not a valid member of the element.
            - or -
            The element cannot contain a CDATA or text element.
            </exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptionElement.SerializeContent(System.Xml.XmlWriter)">
            <summary>
            Write the contents of this element to the given <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <remarks>The caller of this method has already written the start element tag before
            calling this method, so deriving classes only need to write the element content, not
            the start or end tags.</remarks>
            <param name="writer">Writer to send XML content to.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptionElement.ConfigureContainer(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Apply this element's configuration to the given <paramref name="container"/>.
            </summary>
            <param name="container">Container to configure.</param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptionElement.Policies">
            <summary>
            Policies defined for this container.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorElement">
            <summary>
            Configuration element that lets you configure
            what interceptor to use for a type.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorElement.#ctor">
            <summary>
            Initialize a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorElement"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorElement.SerializeContent(System.Xml.XmlWriter)">
            <summary>
            Write the contents of this element to the given <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <remarks>The caller of this method has already written the start element tag before
            calling this method, so deriving classes only need to write the element content, not
            the start or end tags.</remarks>
            <param name="writer">Writer to send XML content to.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorElement.GetInjectionMembers(Microsoft.Practices.Unity.IUnityContainer,System.Type,System.Type,System.String)">
            <summary>
            Return the set of <see cref="T:Microsoft.Practices.Unity.InjectionMember"/>s that are needed
            to configure the container according to this configuration element.
            </summary>
            <param name="container">Container that is being configured.</param>
            <param name="fromType">Type that is being registered.</param>
            <param name="toType">Type that <paramref name="fromType"/> is being mapped to.</param>
            <param name="name">Name this registration is under.</param>
            <returns>One or more <see cref="T:Microsoft.Practices.Unity.InjectionMember"/> objects that should be
            applied to the container registration.</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorElement.TypeName">
            <summary>
            Type name for the interceptor to apply.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorElement.Name">
            <summary>
            Name to use when resolving interceptors from the container.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorElement.IsDefaultForType">
            <summary>
            Should this interceptor be registered as the default for the contained
            type, or only for this particular type/name pair?
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorElement.Key">
            <summary>
            Each element must have a unique key, which is generated by the subclasses.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorRegistrationElementCollection">
            <summary>
            A collection of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorRegistrationElement"/> objects as shown
            in configuration.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorRegistrationElementCollection.CreateNewElement">
            <summary>
            When overridden in a derived class, creates a new <see cref="T:System.Configuration.ConfigurationElement"/>.
            </summary>
            <returns>
            A new <see cref="T:System.Configuration.ConfigurationElement"/>.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorRegistrationElementCollection.GetElementKey(System.Configuration.ConfigurationElement)">
            <summary>
            Gets the element key for a specified configuration element when overridden in a derived class.
            </summary>
            <returns>
            An <see cref="T:System.Object"/> that acts as the key for the specified <see cref="T:System.Configuration.ConfigurationElement"/>.
            </returns>
            <param name="element">The <see cref="T:System.Configuration.ConfigurationElement"/> to return the key for. </param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorsElement">
            <summary>
            Configuration element that provides a top-level element for
            configuration interceptors for types in a container.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorsElement.SerializeContent(System.Xml.XmlWriter)">
            <summary>
            Write the contents of this element to the given <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <remarks>The caller of this method has already written the start element tag before
            calling this method, so deriving classes only need to write the element content, not
            the start or end tags.</remarks>
            <param name="writer">Writer to send XML content to.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorsElement.ConfigureContainer(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Apply this element's configuration to the given <paramref name="container"/>.
            </summary>
            <param name="container">Container to configure.</param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorsElement.Interceptors">
            <summary>
            The various child elements that are contained in this element.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorsInterceptorElement">
            <summary>
            Configuration element that represents the configuration for
            a specific interceptor, as presented in the config file inside
            the &lt;interceptors&gt; element.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorsInterceptorElement.SerializeContent(System.Xml.XmlWriter)">
            <summary>
            Write the contents of this element to the given <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <remarks>The caller of this method has already written the start element tag before
            calling this method, so deriving classes only need to write the element content, not
            the start or end tags.</remarks>
            <param name="writer">Writer to send XML content to.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorsInterceptorElement.OnDeserializeUnrecognizedElement(System.String,System.Xml.XmlReader)">
            <summary>
            Gets a value indicating whether an unknown element is encountered during deserialization.
            </summary>
            <returns>
            true when an unknown element is encountered while deserializing; otherwise, false.
            </returns>
            <param name="elementName">The name of the unknown subelement.</param>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> being used for deserialization.</param>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">The element identified by <paramref name="elementName"/> is locked.
            - or -
            One or more of the element's attributes is locked.
            - or -
            <paramref name="elementName"/> is unrecognized, or the element has an unrecognized attribute.
            - or -
            The element has a Boolean attribute with an invalid value.
            - or -
            An attempt was made to deserialize a property more than once.
            - or -
            An attempt was made to deserialize a property that is not a valid member of the element.
            - or -
            The element cannot contain a CDATA or text element.
            </exception>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorsInterceptorElement.TypeName">
            <summary>
            Type of interceptor to configure.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorsInterceptorElement.Registrations">
            <summary>
            The types that this interceptor will be registered against.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorsInterceptorElement.Value">
            <summary>
            Any value passed to the type converter.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorsInterceptorElement.TypeConverterTypeName">
            <summary>
            Type converter to use to create the interceptor, if any.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorsInterceptorElementCollection">
            <summary>
            A collection of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorsInterceptorElement"/> objects
            as stored in configuration.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.InterceptorsInterceptorElementCollection.GetElementKey(System.Configuration.ConfigurationElement)">
            <summary>
            Gets the element key for a specified configuration element when overridden in a derived class.
            </summary>
            <returns>
            An <see cref="T:System.Object"/> that acts as the key for the specified <see cref="T:System.Configuration.ConfigurationElement"/>.
            </returns>
            <param name="element">The <see cref="T:System.Configuration.ConfigurationElement"/> to return the key for. </param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.KeyElement">
            <summary>
            The &lt;key&gt; element that occurs inside an &lt;interceptor&gt; element
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.KeyElement.SerializeContent(System.Xml.XmlWriter)">
            <summary>
            Write the contents of this element to the given <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <remarks>The caller of this method has already written the start element tag before
            calling this method, so deriving classes only need to write the element content, not
            the start or end tags.</remarks>
            <param name="writer">Writer to send XML content to.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.KeyElement.RegisterInterceptor(Microsoft.Practices.Unity.IUnityContainer,Microsoft.Practices.Unity.InterceptionExtension.IInterceptor)">
            <summary>
            Actually register the interceptor against this type.
            </summary>
            <param name="container">Container to configure.</param>
            <param name="interceptor">interceptor to register.</param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.KeyElement.Name">
            <summary>
            Name registration should be under. To register under the default, leave blank.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.MatchingRuleElement">
            <summary>
            A configuration element representing a matching rule.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.MatchingRuleElementCollection">
            <summary>
            A collection of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.MatchingRuleElement"/>s for configuration.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.MatchingRuleElementCollection.GetElementKey(System.Configuration.ConfigurationElement)">
            <summary>
            Gets the element key for a specified configuration element when overridden in a derived class.
            </summary>
            <returns>
            An <see cref="T:System.Object"/> that acts as the key for the specified <see cref="T:System.Configuration.ConfigurationElement"/>.
            </returns>
            <param name="element">The <see cref="T:System.Configuration.ConfigurationElement"/> to return the key for. </param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.MatchingRuleElementCollection.OnDeserializeUnrecognizedElement(System.String,System.Xml.XmlReader)">
            <summary>
            Causes the configuration system to throw an exception.
            </summary>
            <returns>
            true if the unrecognized element was deserialized successfully; otherwise, false. The default is false.
            </returns>
            <param name="elementName">The name of the unrecognized element. </param>
            <param name="reader">An input stream that reads XML from the configuration file. </param>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">The element specified in <paramref name="elementName"/> is the &lt;clear&gt; element.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="elementName"/> starts with the reserved prefix "config" or "lock".</exception>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.MatchingRuleElementCollection.Item(System.String)">
            <summary>
            Retrieve a matching rule element from the collection by name.
            </summary>
            <param name="name">Name to look up.</param>
            <returns>The rule, or null if not in the collection.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyElement">
            <summary>
            Configuration element for building up an interception policy.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyElement.OnDeserializeUnrecognizedElement(System.String,System.Xml.XmlReader)">
            <summary>
            Gets a value indicating whether an unknown element is encountered during deserialization.
            </summary>
            <returns>
            true when an unknown element is encountered while deserializing; otherwise, false.
            </returns>
            <param name="elementName">The name of the unknown subelement.</param>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> being used for deserialization.</param>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">The element identified by <paramref name="elementName"/> is locked.
            - or -
            One or more of the element's attributes is locked.
            - or -
            <paramref name="elementName"/> is unrecognized, or the element has an unrecognized attribute.
            - or -
            The element has a Boolean attribute with an invalid value.
            - or -
            An attempt was made to deserialize a property more than once.
            - or -
            An attempt was made to deserialize a property that is not a valid member of the element.
            - or -
            The element cannot contain a CDATA or text element.
            </exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyElement.SerializeContent(System.Xml.XmlWriter)">
            <summary>
            Write the contents of this element to the given <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <remarks>The caller of this method has already written the start element tag before
            calling this method, so deriving classes only need to write the element content, not
            the start or end tags.</remarks>
            <param name="writer">Writer to send XML content to.</param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyElement.Name">
            <summary>
            Name of this policy.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyElement.MatchingRules">
            <summary>
            Matching rules for this policy.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyElement.CallHandlers">
            <summary>
            Call handlers for this policy.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyElementCollection">
            <summary>
            A collection of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyElement"/> in the configuration.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyElementCollection.GetElementKey(System.Configuration.ConfigurationElement)">
            <summary>
            Gets the element key for a specified configuration element when overridden in a derived class.
            </summary>
            <returns>
            An <see cref="T:System.Object"/> that acts as the key for the specified <see cref="T:System.Configuration.ConfigurationElement"/>.
            </returns>
            <param name="element">The <see cref="T:System.Configuration.ConfigurationElement"/> to return the key for. </param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyElementCollection.Item(System.String)">
            <summary>
            Indexer to retrieve policy element objects by name.
            </summary>
            <param name="policyName">Name of policy to get.</param>
            <returns>The element.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyInjectionElement">
            <summary>
            A shortcut element to enable the policy injection behavior.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyInjectionElement.GetInjectionMembers(Microsoft.Practices.Unity.IUnityContainer,System.Type,System.Type,System.String)">
            <summary>
            Return the set of <see cref="T:Microsoft.Practices.Unity.InjectionMember"/>s that are needed
            to configure the container according to this configuration element.
            </summary>
            <param name="container">Container that is being configured.</param>
            <param name="fromType">Type that is being registered.</param>
            <param name="toType">Type that <paramref name="fromType"/> is being mapped to.</param>
            <param name="name">Name this registration is under.</param>
            <returns>One or more <see cref="T:Microsoft.Practices.Unity.InjectionMember"/> objects that should be
            applied to the container registration.</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.PolicyInjectionElement.Key">
            <summary>
            Each element must have a unique key, which is generated by the subclasses.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Configuration.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.Properties.Resources.CannotCreateInterceptorRegistrationElement">
            <summary>
              Looks up a localized string similar to The abstract type InterceptorRegistrationElement cannot be created. Please create a concrete instance..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.Properties.Resources.CannotHaveInjectionWithoutTypeName">
            <summary>
              Looks up a localized string similar to The &lt;injection/&gt; element is not allowed on element named &apos;{0}&apos; because it doesn&apos;t have a type attribute..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.Properties.Resources.CannotHaveLifetimeWithoutTypeName">
            <summary>
              Looks up a localized string similar to The &lt;lifetime/&gt; element is not allowed on element named &apos;{0}&apos; because it doesn&apos;t have a type attribute..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.Properties.Resources.CouldNotResolveType">
            <summary>
              Looks up a localized string similar to The type name or alias {0} could not be resolved. Please check your configuration file and verify this type name..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.Properties.Resources.ExceptionCannotCreateInstance">
            <summary>
              Looks up a localized string similar to Cannot create instance of type {0} with a default constructor..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.Properties.Resources.ExceptionResolvedTypeNotCompatible">
            <summary>
              Looks up a localized string similar to The type name {0} resolved to type {1} is not compatible with the required type {2}..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.Properties.Resources.InvalidInterceptorType">
            <summary>
              Looks up a localized string similar to The type {0} could not be resolved to a valid type. Please double check your configuration..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Configuration.Properties.Resources.MustHaveAtLeastOneBehaviorAttribute">
            <summary>
              Looks up a localized string similar to The interception behavior element must have at least one of the &apos;name&apos; or &apos;type&apos; attributes..
            </summary>
        </member>
    </members>
</doc>
