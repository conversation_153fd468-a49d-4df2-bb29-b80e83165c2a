﻿<?xml version="1.0" encoding="utf-8" ?>
<PerformanceCounters>
  <Category CategoryName="Windows Azure Caching:Client" CategoryHelp="Performance counters for caching clients">
    <Counter Name="Requests" Type="NumberOfItems64" HelpString="TotalRequestsHelp"/>
    <Counter Name="Requests / sec" Type="RateOfCountsPerSecond32" HelpString="TotalRequestPerSecHelp"/>
    <Counter Name="Server Responses Dropped / sec" Type="RateOfCountsPerSecond32" HelpString="TotalServerResponsesDroppedHelp"/>
    <Counter Name="Failure Exceptions" Type="NumberOfItems64" HelpString="TotalFailureExceptionsHelp"/>
    <Counter Name="Failure Exceptions / sec" Type="RateOfCountsPerSecond32" HelpString="FailureExceptionRateHelp"/>
    <Counter Name="Total Local Cache Hits" Type="NumberOfItems64" HelpString="TotalLocalCacheHitsHelp"/>
    <Counter Name="Total Local Cache Objects" Type="NumberOfItems64" HelpString="TotalItemsInLocalCacheHelp"/>
    <Counter Name="Average Get Latency (Network) / operation Microsecond" Type="AverageCount64" HelpString="AverageCacheGetNetworkLatencyHelp"/>
    <Counter Name="Average Get Latency (Network) / operation Microsecond Base" Type="AverageBase" HelpString="AverageCacheGetNetworkLatencyBaseHelp"/>
    <Counter Name="Read Requests" Type="NumberOfItems64" HelpString="ReadRequestsHelp"/>
    <Counter Name="Write Requests" Type="NumberOfItems64" HelpString="WriteRequestsHelp"/>
    <Counter Name="Bytes Received / sec" Type="RateOfCountsPerSecond32" HelpString="CacheIncomingDataRateHelp"/>
    <Counter Name="Bytes Sent / sec" Type="RateOfCountsPerSecond32" HelpString="CacheOutgoingDateRateHelp"/>
    <Counter Name="Average Get Latency / operation Microsecond" Type="AverageCount64" HelpString="AverageCacheGetLatencyHelp"/>
    <Counter Name="Average Get Latency / operation Microsecond Base" Type="AverageBase" HelpString="AverageCacheGetLatencyBaseHelp"/>
    <Counter Name="Average Put Latency / operation Microsecond" Type="AverageCount64" HelpString="AverageCachePutLatencyHelp"/>
    <Counter Name="Average Put Latency / operation Microsecond Base" Type="AverageBase" HelpString="AverageCachePutLatencyBaseHelp"/>
    <Counter Name="Local Cache Filled Percentage" Type="RawFraction" HelpString="PercentageLocalCacheFullHelp"/>
    <Counter Name="Local Cache Filled Percentage Base" Type="RawBase" HelpString="PercentageLocalCacheFullBaseHelp"/>
    <Counter Name="Local Cache Hits Percentage" Type="RawFraction" HelpString="PercentageLocalCacheHitsHelp"/>
    <Counter Name="Local Cache Hits Percentage Base" Type="RawBase" HelpString="PercentageLocalCacheHitsBaseHelp"/>
    <Counter Name="Total Notifications Received" Type="NumberOfItems64" HelpString="TotalNotificationsReceivedHelp"/>
    <Counter Name="Retry Exceptions" Type="NumberOfItems64" HelpString="TotalRetryExceptionsHelp"/>
    <Counter Name="Retry Exceptions / sec" Type="RateOfCountsPerSecond32" HelpString="RetryExceptionRateHelp"/>
    <Counter Name="Timeout Exceptions" Type="NumberOfItems64" HelpString="TotalTimeoutExceptionsHelp"/>
    <Counter Name="Timeout Exceptions / sec" Type="RateOfCountsPerSecond32" HelpString="TimeoutExceptionRateHelp"/>
    <Counter Name="Current Server Connections" Type="NumberOfItems64" HelpString="TotalActiveServerConnectionsHelp"/>
    <Counter Name="Total Connection Requests Failed" Type="NumberOfItems64" HelpString="TotalConnectionRequestsFailedHelp"/>
    <Counter Name="Network Exceptions" Type="NumberOfItems64" HelpString="TotalNetworkExceptionsHelp"/>
    <Counter Name="Network Exceptions / sec" Type="RateOfCountsPerSecond32" HelpString="NetworkExceptionRateHelp"/>
    <Counter Name="Current Waiting Requests" Type="NumberOfItems64" HelpString="TotalOutstandingRequestsHelp" />
  </Category>
</PerformanceCounters>