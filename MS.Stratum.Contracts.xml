<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MS.Stratum.Contracts</name>
    </assembly>
    <members>
        <member name="T:MS.Stratum.Authentication.AuthenticationTokenException">
            <summary>
            Custom exception for an authentication token exception
            </summary>
        </member>
        <member name="M:MS.Stratum.Authentication.AuthenticationTokenException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:MS.Stratum.Authentication.AuthenticationTokenException"/> class.
            </summary>
            <param name="serializationInfo">The serialization info.</param>
            <param name="streamingContext">The streaming context.</param>
        </member>
        <member name="M:MS.Stratum.Authentication.AuthenticationTokenException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:MS.Stratum.Authentication.AuthenticationTokenException"/> class.
            </summary>
        </member>
        <member name="M:MS.Stratum.Authentication.AuthenticationTokenException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:MS.Stratum.Authentication.AuthenticationTokenException"/> class.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:MS.Stratum.Authentication.AuthenticationTokenException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:MS.Stratum.Authentication.AuthenticationTokenException"/> class.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="T:MS.Stratum.Authentication.Claim">
            <summary>
            Represents a single claim within a security token.
            </summary>
        </member>
        <member name="P:MS.Stratum.Authentication.Claim.ClaimType">
            <summary>
            Gets or sets a unique identifier for a claim.
            </summary>
        </member>
        <member name="P:MS.Stratum.Authentication.Claim.ClaimValue">
            <summary>
            Gets or sets the claim value.
            </summary>
        </member>
        <member name="T:MS.Stratum.Authentication.ClaimsExtensions">
            <summary>
            Extention methods for the <see cref="T:MS.Stratum.Authentication.Claim"/> entity.
            </summary>
        </member>
        <member name="M:MS.Stratum.Authentication.ClaimsExtensions.ToIdentityModelClaim(MS.Stratum.Authentication.Claim)">
            <summary>
            Gets the identity model claim from stratum claim.
            </summary>
            <param name="claim">The claim.</param>
        </member>
        <member name="M:MS.Stratum.Authentication.ClaimsExtensions.ToIdentityModelClaims(System.Collections.Generic.IEnumerable{MS.Stratum.Authentication.Claim})">
            <summary>
            Gets the identity model claims from stratum claims.
            </summary>
            <param name="claims">The claims.</param>
            <exception cref="T:System.ArgumentNullException">stratumClaims</exception>
        </member>
        <member name="T:MS.Stratum.Authentication.CustomClaimTypes">
            <summary>
            Stratum authentication token custom claim types.
            </summary>
        </member>
        <member name="P:MS.Stratum.Authentication.CustomClaimTypes.X509CertificateThumbprint">
            <summary>
            Gets the X509 certificate thumbprint custom claim type.
            </summary>
        </member>
        <member name="P:MS.Stratum.Authentication.CustomClaimTypes.X509CertificateSerialNumber">
            <summary>
            Gets the X509 certificate serial number custom claim type.
            </summary>
        </member>
        <member name="T:MS.Stratum.Authentication.IAuthenticationTokenValidator">
            <summary>
            Contract for an authentication token validator.
            </summary>
        </member>
        <member name="M:MS.Stratum.Authentication.IAuthenticationTokenValidator.Validate(System.String)">
            <summary>
            Validates the authentication token.
            </summary>
            <param name="token">The authentication token.</param>
            <returns>The collection of claims embedded in the token.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="token" /> is <c>null</c> or empty.</exception>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenException">Thrown if the token cannot be decoded.</exception>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenValidationException">Thrown if any validation error occurs.</exception>
        </member>
        <member name="T:MS.Stratum.Authentication.Interception.IJwtTokenValidationCallHandler">
            <summary>
            Contract for a crosscutting implementation for a JWT validator.
            Throws an <see cref="T:MS.Stratum.Authentication.AuthenticationTokenException"/> if the 
            token cannot be validated or validation fails.
            </summary>
        </member>
        <member name="T:MS.Stratum.Authentication.Interception.JwtTokenValidationAttribute">
            <summary>
            Attribute for a service operation which has JWT in the authorization
            header that needs to be validated.
            A service operation decorated with this attribute will have
            the <see cref="P:MS.Stratum.Services.DataContracts.Request.AuthorizationHeader"/> property set by this validation
            when validation succeeds.  Optionally, an exception
            can be thrown if validation fails.
            </summary>
        </member>
        <member name="M:MS.Stratum.Authentication.Interception.JwtTokenValidationAttribute.CreateHandler(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Derived classes implement this method. When called, it
            creates a new call handler as specified in the attribute
            configuration.
            </summary>
            <param name="container">The <see cref="T:Microsoft.Practices.Unity.IUnityContainer" /> to use when creating handlers,
            if necessary.</param>
            <returns>
            A new call handler object.
            </returns>
        </member>
        <member name="T:MS.Stratum.Authentication.IX509CertificateRepository">
            <summary>
            Contract for a certificate store.
            </summary>
        </member>
        <member name="M:MS.Stratum.Authentication.IX509CertificateRepository.FindByThumbprint(System.String,System.Boolean,System.Security.Cryptography.X509Certificates.StoreLocation,System.Security.Cryptography.X509Certificates.StoreName)">
            <summary>
            Finds a certificate by the thumbprint.
            </summary>
            <param name="thumbprint">The certificate thumbprint.</param>
            <param name="requireTrustedAuthority">If set to <c>true</c> require the certificate to be issued by a trusted certificate authority.</param>
            <param name="storeLocation">The store location.</param>
            <param name="storeName">Name of the store.</param>
            <returns>
            The located certificate or <c>null</c> if the certificate could not be found.
            </returns>
        </member>
        <member name="T:MS.Stratum.Authentication.IJwtTokenValidator">
            <summary>
            Contract for a provider which can 
            perform validation on a JSON Web Token (JWT).
            </summary>
        </member>
        <member name="T:MS.Stratum.AutoLogging.IRequestResponseLogCallHandler">
            <summary>
            Contract for a class which logs the input parameters and output values of a method
            </summary>
        </member>
        <member name="T:MS.Stratum.AutoLogging.IServiceConsumerLogCallHandler">
            <summary>
            Contract for a class which logs the application guid and method name
            </summary>
        </member>
        <member name="T:MS.Stratum.AutoLogging.ITimedCallHandler">
            <summary>
            Contract for a class which logs the timing of a routine
            </summary>
        </member>
        <member name="T:MS.Stratum.AutoLogging.RequestResponseLogAttribute">
            <summary>
            Attribute to call the RequestResponseLogCallHandler
            </summary>
        </member>
        <member name="M:MS.Stratum.AutoLogging.RequestResponseLogAttribute.CreateHandler(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Returns instance of IRequestResponseLogCallHandler
            </summary>
            <param name="container">The unity container.</param>
        </member>
        <member name="T:MS.Stratum.AutoLogging.ServiceConsumerLogAttribute">
            <summary>
            Attribute to call the ServiceConsumerLogCallHandler
            </summary>
        </member>
        <member name="M:MS.Stratum.AutoLogging.ServiceConsumerLogAttribute.CreateHandler(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Returns instance of IServiceConsumerLogCallHandler
            </summary>
            <param name="container">The unity container.</param>
            <returns>
            A new call handler object.
            </returns>
        </member>
        <member name="T:MS.Stratum.AutoLogging.TimedLogAttribute">
            <summary>
            Attribute to call the Timer Log
            </summary>
        </member>
        <member name="M:MS.Stratum.AutoLogging.TimedLogAttribute.CreateHandler(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Returns instance of ITimerCallHandler
            </summary>
            <param name="container">The unity container.</param>
        </member>
        <member name="T:MS.Stratum.Encryption.ICryptographer">
            <summary>
            Contract for a class which performs encryption and decryption
            </summary>
        </member>
        <member name="M:MS.Stratum.Encryption.ICryptographer.Encrypt(System.String)">
            <summary>
            Encrypts the specified input.
            </summary>
            <param name="input">The input.</param>
            <returns>The encrypted value</returns>
        </member>
        <member name="M:MS.Stratum.Encryption.ICryptographer.Decrypt(System.String)">
            <summary>
            Decrypts the specified input.
            </summary>
            <param name="input">The input.</param>
            <returns>The decrypted value</returns>
        </member>
        <member name="T:MS.Stratum.Encryption.ISymmetricCryptographer">
            <summary>
            Contract for a class which performs symmetric encryption and decryption
            </summary>
        </member>
        <member name="P:MS.Stratum.Encryption.ISymmetricCryptographer.Key">
            <summary>
            The secret key for the symmetric algorithm.
            </summary>
        </member>
        <member name="P:MS.Stratum.Encryption.ISymmetricCryptographer.IV">
            <summary>
            The initialization vector (IV) for the symmetric algorithm.
            </summary>
        </member>
        <member name="T:MS.Stratum.Encryption.IAsymmetricCryptographer">
            <summary>
            Contract for a class which performs asymmetric encryption and decryption
            </summary>
        </member>
        <member name="P:MS.Stratum.Encryption.IAsymmetricCryptographer.Certificate">
            <summary>
            Gets the certificate for the asymmetric algorithm.
            </summary>
        </member>
        <member name="M:MS.Stratum.Encryption.IAsymmetricCryptographer.Encrypt(System.String,System.Text.Encoding,System.Boolean)">
            <summary>
            Overloading of Encrypt
            </summary>
            <param name="input">string input to encrypt</param>
            <param name="encoding">Encoding type - ASCII or UTF8</param>
            <param name="fOaep">true = OAEP, false = PKCS#1</param>
            <returns></returns>
        </member>
        <member name="T:MS.Stratum.Encryption.ICryptographerAuthSessionTimeout">
            <summary>
            Symmetric cryptographer for auth session timeout values.
            </summary>
        </member>
        <member name="T:MS.Stratum.Encryption.ICryptographerConfiguration">
            <summary>
            Asymmetric cryptographer for configuration values.
            </summary>
        </member>
        <member name="T:MS.Stratum.Encryption.ICryptographerAuthenticationManager">
            <summary>
            Asymmetric cryptographer for authentication management values.
            </summary>
        </member>
        <member name="T:MS.Stratum.Encryption.ICryptographerOrderProcessingPpmCookieManager">
            <summary>
            Asymmetric cryptographer for order processing ppm cookie values.
            </summary>
        </member>
        <member name="T:MS.Stratum.Encryption.ICryptographerRedirectionURLManager">
            <summary>
            Asymmetric cryptographer for redirection URLs to third party websites using RSA algorithm
            </summary>
        </member>
        <member name="T:MS.Stratum.Encryption.ICryptographerTripleDesQueryString">
            <summary>
            Symmetric cryptographer for Triple DES Query String.
            </summary>
        </member>
        <member name="T:MS.Stratum.StratumModuleNames">
            <summary>
            Names of the modules in this assembly.
            </summary>
        </member>
        <member name="F:MS.Stratum.StratumModuleNames.Logging">
            <summary>
            The logging module name.
            </summary>
        </member>
        <member name="F:MS.Stratum.StratumModuleNames.Configuration">
            <summary>
            The configuration module name.
            </summary>
        </member>
        <member name="F:MS.Stratum.StratumModuleNames.Encryption">
            <summary>
            The encryption module name.
            </summary>
        </member>
        <member name="F:MS.Stratum.StratumModuleNames.Authentication">
            <summary>
            The authentication module name.
            </summary>
        </member>
        <member name="F:MS.Stratum.StratumModuleNames.Caching">
            <summary>
            The caching module name.
            </summary>
        </member>
        <member name="F:MS.Stratum.StratumModuleNames.LocalCaching">
            <summary>
            The local memory caching module name.
            </summary>
        </member>
        <member name="F:MS.Stratum.StratumModuleNames.RequestCaching">
            <summary>
            The request caching module name.
            </summary>
        </member>
        <member name="F:MS.Stratum.StratumModuleNames.DistributedCaching">
            <summary>
            The shared caching module name.
            </summary>
        </member>
        <member name="F:MS.Stratum.StratumModuleNames.SessionCaching">
            <summary>
            The session caching module name.
            </summary>
        </member>
        <member name="F:MS.Stratum.StratumModuleNames.DataAccess">
            <summary>
            The data access module name.
            </summary>
        </member>
        <member name="F:MS.Stratum.StratumModuleNames.Validation">
            <summary>
            The validation module name.
            </summary>
        </member>
        <member name="F:MS.Stratum.StratumModuleNames.Services">
            <summary>
            The services module name.
            </summary>
        </member>
        <member name="F:MS.Stratum.StratumModuleNames.Identity">
            <summary>
            The Identity data source module name.
            </summary>
        </member>
        <member name="F:MS.Stratum.StratumModuleNames.Authorization">
            <summary>
            The Identity data source module name.
            </summary>
        </member>
        <member name="F:MS.Stratum.StratumModuleNames.AutoLogging">
            <summary>
            The autologging module name.
            </summary>
        </member>
        <member name="T:MS.Stratum.Services.DataContracts.AuthorizationHeader">
            <summary>
            Information from the authorization processing pipeline about 
            the incoming request's authorization.
            </summary>
        </member>
        <member name="P:MS.Stratum.Services.DataContracts.AuthorizationHeader.Claims">
            <summary>
            The claims from an <see cref="T:MS.Stratum.Authentication.IAuthenticationTokenValidator"/>.
            </summary>
        </member>
        <member name="T:MS.Stratum.Services.DataContracts.Request">
            <summary>
            Request type to be taken as an input to all service operations.
            If additional parameters need to be taken in, then
            use the <see cref="T:MS.Stratum.Services.DataContracts.Request`1"/> implementation.
            </summary>
        </member>
        <member name="P:MS.Stratum.Services.DataContracts.Request.ApplicationGuid">
            <summary>
            The unique identifier for the calling application.
            </summary>
        </member>
        <member name="P:MS.Stratum.Services.DataContracts.Request.ActivityId">
            <summary>
            The unique id for a tracable activity from
            the calling application through the service.
            Used mainly for logging and troubleshooting purposes.
            </summary>
        </member>
        <member name="P:MS.Stratum.Services.DataContracts.Request.AuthorizationHeader">
            <summary>
            Gets or sets the authorization header.
            Note: This property is not serialized in the request,
            it is set by the authorizaiton processing pipeline.
            </summary>
        </member>
        <member name="P:MS.Stratum.Services.DataContracts.Request.ExtensionData">
            <summary>
            Gets or sets the structure that contains extra data from a SOAP request.
            </summary>
            <returns>
            An <see cref="T:System.Runtime.Serialization.ExtensionDataObject" /> that contains data 
            that is not recognized as belonging to the data contract.
            </returns>
        </member>
        <member name="T:MS.Stratum.Services.DataContracts.Request`1">
            <summary>
            Request type to be taken as an input to all service operations.
            Allows for extended information beyond the standard input
            from a <see cref="T:MS.Stratum.Services.DataContracts.Request"/>.
            </summary>
            <typeparam name="T">
            The type of the extended request information.
            </typeparam>
        </member>
        <member name="P:MS.Stratum.Services.DataContracts.Request`1.OperationInput">
            <summary>
            A service request's operation specific input.
            </summary>
        </member>
        <member name="T:MS.Stratum.Services.DataContracts.Response">
            <summary>
            Response type to returned as the output to all service operations.
            If additional parameters need to be returned, then
            use the <see cref="T:MS.Stratum.Services.DataContracts.Response`1"/> implementation.
            </summary>
        </member>
        <member name="P:MS.Stratum.Services.DataContracts.Response.Error">
            <summary>
            The error details or <c>null</c> if no error occurred.
            </summary>
        </member>
        <member name="P:MS.Stratum.Services.DataContracts.Response.ActivityId">
            <summary>
            The unique id for a tracable activity from
            the calling application through the service.
            Used mainly for logging and troubleshooting purposes.
            </summary>
        </member>
        <member name="P:MS.Stratum.Services.DataContracts.Response.ExtensionData">
            <summary>
            Gets or sets the structure that contains extra data from a SOAP request.
            </summary>
            <returns>
            An <see cref="T:System.Runtime.Serialization.ExtensionDataObject" /> that contains data 
            that is not recognized as belonging to the data contract.
            </returns>
        </member>
        <member name="T:MS.Stratum.Services.DataContracts.Response`1">
            <summary>
            Response type to returned as the output to all service operations.
            Allows for extended information beyond the standard output
            from a <see cref="T:MS.Stratum.Services.DataContracts.Response"/>.
            </summary>
            <typeparam name="T">
            The type of the extended response information.
            </typeparam>
        </member>
        <member name="P:MS.Stratum.Services.DataContracts.Response`1.OperationOutput">
            <summary>
            A service request's operation specific output.
            </summary>
        </member>
        <member name="T:MS.Stratum.Services.DataContracts.ResponseError">
            <summary>
            Details about an error that occurred during a service operation.
            </summary>
        </member>
        <member name="P:MS.Stratum.Services.DataContracts.ResponseError.ErrorCode">
            <summary>
            The unique identifier for an error.
            </summary>
        </member>
        <member name="P:MS.Stratum.Services.DataContracts.ResponseError.ErrorMessage">
            <summary>
            Human readable details about the error.
            </summary>
        </member>
        <member name="P:MS.Stratum.Services.DataContracts.ResponseError.ExtensionData">
            <summary>
            Gets or sets the structure that contains extra data.
            </summary>
            <returns>
            An <see cref="T:System.Runtime.Serialization.ExtensionDataObject" /> that contains data 
            that is not recognized as belonging to the data contract.
            </returns>
        </member>
        <member name="T:MS.Stratum.Services.Interception.HandleExceptionAttribute">
            <summary>
            Attribute which catches all exceptions and allows for a 
            configurable action to take place when an exception was thrown.
            </summary>
        </member>
        <member name="M:MS.Stratum.Services.Interception.HandleExceptionAttribute.CreateHandler(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Creates the handler.
            </summary>
            <param name="container">The container.</param>
        </member>
        <member name="T:MS.Stratum.Services.Interception.IHandleExceptionCallHandler">
            <summary>
            Contract for a crosscutting exception handler.
            </summary>
        </member>
        <member name="T:MS.Stratum.Services.Interception.IHandleExceptionConfiguration">
            <summary>
            Contract for a configuration which defines an 
            action to take place when an exception is handled.
            This implementation will be used if a typed 
            implementation cannot be found.
            </summary>
        </member>
        <member name="M:MS.Stratum.Services.Interception.IHandleExceptionConfiguration.OnException(System.Exception)">
            <summary>
            Called when an exception was thrown and has 
            the option to return a different exception
            or <c>null</c> to let the method return.
            </summary>
            <param name="exception">The exception.</param>
        </member>
        <member name="T:MS.Stratum.Services.Interception.IHandleExceptionConfiguration`1">
            <summary>
            Contract for a configuration which defines an 
            action to take place when an exception is handled.
            </summary>
            <typeparam name="T">
            The source type which is decorated with the <see cref="T:MS.Stratum.Services.Interception.HandleExceptionAttribute"/>.
            </typeparam>
        </member>
        <member name="T:MS.Stratum.Services.Interception.ISetActivityIdCallHandler">
            <summary>
            A call handler which sets the ativity id from a <see cref="T:MS.Stratum.Services.DataContracts.Request"/>
            and sets the activity id in a <see cref="T:MS.Stratum.Services.DataContracts.Response"/>.
            </summary>
        </member>
        <member name="T:MS.Stratum.Services.Interception.SetActivityIdAttribute">
            <summary>
            Attribute for a service operation which has an input of
            <see cref="T:MS.Stratum.Services.DataContracts.Request"/> and / or a response of <see cref="T:MS.Stratum.Services.DataContracts.Response"/>.
            A service operation decorated with this attribute will have
            the ativity id set by the request and set in the response.
            </summary>
        </member>
        <member name="M:MS.Stratum.Services.Interception.SetActivityIdAttribute.CreateHandler(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Derived classes implement this method. When called, it
            creates a new call handler as specified in the attribute
            configuration.
            </summary>
            <param name="container">The <see cref="T:Microsoft.Practices.Unity.IUnityContainer" /> to use when creating handlers,
            if necessary.</param>
            <returns>
            A new call handler object.
            </returns>
        </member>
        <member name="T:MS.Stratum.Validation.Interception.IValidateArgumentsCallHandler">
            <summary>
            Contract for a crosscutting implementation for a generic input argument validator.
            </summary>
        </member>
        <member name="T:MS.Stratum.Validation.Interception.ValidateArgumentsAttribute">
            <summary>
            Attribute for a method to perform validation the input arguments
            to that method using an <see cref="T:MS.Stratum.Validation.IValidator`1"/>.
            Optionally, an exception can be thrown if validation fails.
            </summary>
        </member>
        <member name="M:MS.Stratum.Validation.Interception.ValidateArgumentsAttribute.CreateHandler(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Derived classes implement this method. When called, it
            creates a new call handler as specified in the attribute
            configuration.
            </summary>
            <param name="container">The <see cref="T:Microsoft.Practices.Unity.IUnityContainer" /> to use when creating handlers,
            if necessary.</param>
            <returns>
            A new call handler object.
            </returns>
        </member>
        <member name="T:MS.Stratum.Validation.IValidationBuilder">
            <summary>
            Contract for a stateful builder of validation errors.
            </summary>
        </member>
        <member name="P:MS.Stratum.Validation.IValidationBuilder.Errors">
            <summary>
            Gets the current collection of validation errors.
            Note: If there are no errors were found, an empty collection is returned.
            </summary>
            <returns>The current collection of validation errors.</returns>
        </member>
        <member name="M:MS.Stratum.Validation.IValidationBuilder.AppendNotNull``1(``0,System.String)">
            <summary>
            Appends a validation error if the input object is null.
            </summary>
            <typeparam name="T">The type of the input object.</typeparam>
            <param name="input">The input.</param>
            <param name="message">The message.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="message"/> is <c>null</c>.
            </exception>
        </member>
        <member name="M:MS.Stratum.Validation.IValidationBuilder.Append(MS.Stratum.Validation.ValidationError)">
            <summary>
            Appends the specified validation error to the current collection of validation errors.
            </summary>
            <param name="validationError">The validation error.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="validationError"/> is null.
            </exception>
        </member>
        <member name="M:MS.Stratum.Validation.IValidationBuilder.Append(System.Collections.Generic.IEnumerable{MS.Stratum.Validation.ValidationError})">
            <summary>
            Appends the specified validation errors to the current collection of validation errors.
            </summary>
            <param name="validationErrors">The validation errors.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="validationErrors"/> is null.
            </exception>
        </member>
        <member name="M:MS.Stratum.Validation.IValidationBuilder.Append``3(``0,System.Linq.Expressions.Expression{System.Func{``0,``1}},``2)">
            <summary>
            Peforms validation on the member and appends the validation errors for that
            member to the current collection of validation errors.
            </summary>
            <typeparam name="TParent">The type of the parent object.</typeparam>
            <typeparam name="TMember">The type of the member to validate.</typeparam>
            <typeparam name="TValidator">The type of the validator.</typeparam>
            <param name="input">The input object which contains the member to validate.</param>
            <param name="memberSelector">The expression to get the member to validate.</param>
            <param name="memberValidator">The validator to use to validate the member.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="memberSelector"/> or <paramref name="memberValidator"/> is <c>null</c>.
            </exception>
        </member>
        <member name="M:MS.Stratum.Validation.IValidationBuilder.Append``2(``0,System.Linq.Expressions.Expression{System.Func{``0,``1}},MS.Stratum.Validation.IValidator{``1})">
            <summary>
            Peforms validation on the member and appends the validation errors for that
            member to the current collection of validation errors.
            </summary>
            <typeparam name="TParent">The type of the parent object.</typeparam>
            <typeparam name="TMember">The type of the member to validate.</typeparam>
            <param name="input">The input object which contains the member to validate.</param>
            <param name="memberSelector">The expression to get the member to validate.</param>
            <param name="memberValidator">The validator to use to validate the member.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="memberSelector"/> or <paramref name="memberValidator"/> is <c>null</c>.
            </exception>
        </member>
        <member name="M:MS.Stratum.Validation.IValidationBuilder.Clear">
            <summary>
            Clears the current collection of validation errors.
            </summary>
        </member>
        <member name="T:MS.Stratum.Validation.IValidator`1">
            <summary>
            Contract for class which defines the rules for validating object instances of a single type.
            </summary>
            <typeparam name="T">Type of the object to validate.</typeparam>
        </member>
        <member name="M:MS.Stratum.Validation.IValidator`1.Validate(`0)">
            <summary>
            Validates the specified input object instance.
            </summary>
            <param name="input">The input object instance.</param>
            <returns>
            A collection of validation errors or an empty list if no validation errors were found.
            </returns>
        </member>
        <member name="T:MS.Stratum.Validation.IRequestValidator`1">
            <summary>
            Validator for request objects.
            </summary>
            <typeparam name="T">Request operation input parameter type.</typeparam>
        </member>
        <member name="T:MS.Stratum.Validation.ValidationError">
            <summary>
            Represents a validation error for a single member on an object.
            </summary>
        </member>
        <member name="P:MS.Stratum.Validation.ValidationError.MemberName">
            <summary>
            Gets or sets the name of the member with the validation errors.
            </summary>
        </member>
        <member name="P:MS.Stratum.Validation.ValidationError.Errors">
            <summary>
            Gets or sets the collection of validation errors for this member.
            </summary>
        </member>
        <member name="T:MS.Stratum.Validation.ValidationException">
            <summary>
            Custom exception for a validation error.
            </summary>
        </member>
        <member name="M:MS.Stratum.Validation.ValidationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:MS.Stratum.Validation.ValidationException"/> class.
            </summary>
            <param name="serializationInfo">The serialization info.</param>
            <param name="streamingContext">The streaming context.</param>
        </member>
        <member name="M:MS.Stratum.Validation.ValidationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:MS.Stratum.Validation.ValidationException"/> class.
            </summary>
        </member>
        <member name="M:MS.Stratum.Validation.ValidationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:MS.Stratum.Validation.ValidationException"/> class.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:MS.Stratum.Validation.ValidationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:MS.Stratum.Validation.ValidationException"/> class.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
    </members>
</doc>
