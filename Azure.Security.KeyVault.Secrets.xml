<?xml version="1.0" encoding="utf-8"?>
<doc>
    <assembly>
        <name>Azure.Security.KeyVault.Secrets</name>
    </assembly>
    <members>
        <member name="T:Azure.Security.KeyVault.Secrets.DeletedSecret">
            <summary>
            Represents a Key Vault secret that has been deleted, allowing it to be recovered, if needed.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.DeletedSecret.RecoveryId">
            <summary>
            Gets a <see cref="T:System.Uri" /> of the deleted secret that can be used to recover it.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.DeletedSecret.DeletedOn">
            <summary>
            Gets a <see cref="T:System.DateTimeOffset" /> indicating when the secret was deleted.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.DeletedSecret.ScheduledPurgeDate">
            <summary>
            Gets a <see cref="T:System.DateTimeOffset" /> for when the deleted secret will be purged.
            </summary>
        </member>
        <member name="T:Azure.Security.KeyVault.Secrets.DeleteSecretOperation">
            <summary>
            A long-running operation for <see cref="M:Azure.Security.KeyVault.Secrets.SecretClient.StartDeleteSecret(System.String,System.Threading.CancellationToken)" /> or <see cref="M:Azure.Security.KeyVault.Secrets.SecretClient.StartDeleteSecretAsync(System.String,System.Threading.CancellationToken)" />.
            </summary>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.DeleteSecretOperation.#ctor">
            <summary> Initializes a new instance of <see cref="T:Azure.Security.KeyVault.Secrets.DeleteSecretOperation" /> for mocking. </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.DeleteSecretOperation.Id">
            <summary>
            Gets an ID representing the operation that can be used to poll for
            the status of the long-running operation.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.DeleteSecretOperation.Value">
            <summary>
            Gets the <see cref="T:Azure.Security.KeyVault.Secrets.DeletedSecret" />.
            You should await <see cref="M:Azure.Security.KeyVault.Secrets.DeleteSecretOperation.WaitForCompletionAsync(System.Threading.CancellationToken)" /> before attempting to purge or recover a secret in this pending state.
            </summary>
            <remarks>
            Azure Key Vault will return a <see cref="T:Azure.Security.KeyVault.Secrets.DeletedSecret" /> immediately but may take time to actually delete the secret if soft-delete is enabled.
            </remarks>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.DeleteSecretOperation.HasCompleted">
            <summary>
            Returns true if the long-running operation completed.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.DeleteSecretOperation.HasValue">
            <summary>
            Returns true if the long-running operation completed successfully and has produced final result (accessible by Value property).
            </summary>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.DeleteSecretOperation.GetRawResponse">
            <summary>
            The last HTTP response received from the server.
            </summary><remarks>
            The last response returned from the server during the lifecycle of this instance.
            An instance of <see cref="T:Azure.Operation`1" /> sends requests to a server in UpdateStatusAsync, UpdateStatus, and other methods.
            Responses from these requests can be accessed using GetRawResponse.
            </remarks>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.DeleteSecretOperation.UpdateStatus(System.Threading.CancellationToken)">
            <summary>
            Calls the server to get updated status of the long-running operation.
            </summary><param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the service call.</param><returns>The HTTP response received from the server.</returns><remarks>
            This operation will update the value returned from GetRawResponse and might update HasCompleted, HasValue, and Value.
            </remarks>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.DeleteSecretOperation.UpdateStatusAsync(System.Threading.CancellationToken)">
            <summary>
            Calls the server to get updated status of the long-running operation.
            </summary><param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the service call.</param><returns>The HTTP response received from the server.</returns><remarks>
            This operation will update the value returned from GetRawResponse and might update HasCompleted, HasValue, and Value.
            </remarks>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.DeleteSecretOperation.WaitForCompletionAsync(System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary><param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param><returns>The last HTTP response received from the server.</returns><remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final result of the operation.
            </remarks>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.DeleteSecretOperation.WaitForCompletionAsync(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary><param name="pollingInterval">
            The interval between status requests to the server.
            The interval can change based on information returned from the server.
            For example, the server might communicate to the client that there is not reason to poll for status change sooner than some time.
            </param><param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param><returns>The last HTTP response received from the server.</returns><remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final result of the operation.
            </remarks>
        </member>
        <member name="T:Azure.Security.KeyVault.Secrets.KeyVaultSecret">
            <summary>
            <see cref="T:Azure.Security.KeyVault.Secrets.KeyVaultSecret" /> is the resource consisting of a value and its <see cref="P:Azure.Security.KeyVault.Secrets.KeyVaultSecret.Properties" />.
            </summary>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.KeyVaultSecret.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Security.KeyVault.Secrets.KeyVaultSecret" /> class.
            </summary>
            <param name="name">The name of the secret.</param>
            <param name="value">The value of the secret.</param>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> or <paramref name="value" /> is null.</exception>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.KeyVaultSecret.Id">
            <summary>
            Gets the secret identifier.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.KeyVaultSecret.Name">
            <summary>
            Gets the the name of the secret.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.KeyVaultSecret.Properties">
            <summary>
            Gets additional properties of the <see cref="T:Azure.Security.KeyVault.Secrets.KeyVaultSecret" />.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.KeyVaultSecret.Value">
            <summary>
            Gets the value of the secret.
            </summary>
            <value>This property is always null for <see cref="T:Azure.Security.KeyVault.Secrets.DeletedSecret" />.</value>
        </member>
        <member name="T:Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier">
            <summary>
            Information about a <see cref="T:Azure.Security.KeyVault.Secrets.KeyVaultSecret" /> parsed from a <see cref="T:System.Uri" />.
            You can use this information when calling methods of a <see cref="T:Azure.Security.KeyVault.Secrets.SecretClient" />.
            </summary>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier.#ctor(System.Uri)">
            <summary>
            Creates a new instance of the <see cref="T:Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier" /> class.
            </summary>
            <param name="id">The <see cref="T:System.Uri" /> to a secret or deleted secret.</param>
            <exception cref="T:System.ArgumentException"><paramref name="id" /> is not a valid Key Vault secret ID.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="id" /> is null.</exception>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier.SourceId">
            <summary>
            Gets the source <see cref="T:System.Uri" /> passed to <see cref="M:Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier.#ctor(System.Uri)" />.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier.VaultUri">
            <summary>
            Gets the <see cref="T:System.Uri" /> of the Key Vault.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier.Name">
            <summary>
            Gets the name of the secret.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier.Version">
            <summary>
            Gets the optional version of the secret.
            </summary>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier.Equals(System.Object)">
            <summary>Indicates whether this instance and a specified object are equal.</summary><param name="obj">The object to compare with the current instance.</param><returns><see langword="true" /> if <paramref name="obj" /> and this instance are the same type and represent the same value; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier.Equals(Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary><param name="other">An object to compare with this object.</param><returns><see langword="true" /> if the current object is equal to the <paramref name="other" /> parameter; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier.GetHashCode">
            <summary>Returns the hash code for this instance.</summary><returns>A 32-bit signed integer that is the hash code for this instance.</returns>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier.ToString">
            <summary>Returns the fully qualified type name of this instance.</summary><returns>The fully qualified type name.</returns>
        </member>
        <member name="T:Azure.Security.KeyVault.Secrets.RecoverDeletedSecretOperation">
            <summary>
            A long-running operation for <see cref="M:Azure.Security.KeyVault.Secrets.SecretClient.StartRecoverDeletedSecret(System.String,System.Threading.CancellationToken)" /> or <see cref="M:Azure.Security.KeyVault.Secrets.SecretClient.StartRecoverDeletedSecretAsync(System.String,System.Threading.CancellationToken)" />.
            </summary>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.RecoverDeletedSecretOperation.#ctor">
            <summary> Initializes a new instance of <see cref="T:Azure.Security.KeyVault.Secrets.RecoverDeletedSecretOperation" /> for mocking. </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.RecoverDeletedSecretOperation.Id">
            <summary>
            Gets an ID representing the operation that can be used to poll for
            the status of the long-running operation.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.RecoverDeletedSecretOperation.Value">
            <summary>
            Gets the <see cref="T:Azure.Security.KeyVault.Secrets.SecretProperties" /> of the secret being recovered.
            You should await <see cref="M:Azure.Security.KeyVault.Secrets.RecoverDeletedSecretOperation.WaitForCompletionAsync(System.Threading.CancellationToken)" /> before attempting to use a secret in this pending state.
            </summary>
            <remarks>
            Azure Key Vault will return a <see cref="T:Azure.Security.KeyVault.Secrets.SecretProperties" /> immediately but may take time to actually recover the deleted secret if soft-delete is enabled.
            </remarks>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.RecoverDeletedSecretOperation.HasCompleted">
            <summary>
            Returns true if the long-running operation completed.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.RecoverDeletedSecretOperation.HasValue">
            <summary>
            Returns true if the long-running operation completed successfully and has produced final result (accessible by Value property).
            </summary>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.RecoverDeletedSecretOperation.GetRawResponse">
            <summary>
            The last HTTP response received from the server.
            </summary><remarks>
            The last response returned from the server during the lifecycle of this instance.
            An instance of <see cref="T:Azure.Operation`1" /> sends requests to a server in UpdateStatusAsync, UpdateStatus, and other methods.
            Responses from these requests can be accessed using GetRawResponse.
            </remarks>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.RecoverDeletedSecretOperation.UpdateStatus(System.Threading.CancellationToken)">
            <summary>
            Calls the server to get updated status of the long-running operation.
            </summary><param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the service call.</param><returns>The HTTP response received from the server.</returns><remarks>
            This operation will update the value returned from GetRawResponse and might update HasCompleted, HasValue, and Value.
            </remarks>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.RecoverDeletedSecretOperation.UpdateStatusAsync(System.Threading.CancellationToken)">
            <summary>
            Calls the server to get updated status of the long-running operation.
            </summary><param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the service call.</param><returns>The HTTP response received from the server.</returns><remarks>
            This operation will update the value returned from GetRawResponse and might update HasCompleted, HasValue, and Value.
            </remarks>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.RecoverDeletedSecretOperation.WaitForCompletionAsync(System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary><param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param><returns>The last HTTP response received from the server.</returns><remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final result of the operation.
            </remarks>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.RecoverDeletedSecretOperation.WaitForCompletionAsync(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Periodically calls the server till the long-running operation completes.
            </summary><param name="pollingInterval">
            The interval between status requests to the server.
            The interval can change based on information returned from the server.
            For example, the server might communicate to the client that there is not reason to poll for status change sooner than some time.
            </param><param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> used for the periodical service calls.</param><returns>The last HTTP response received from the server.</returns><remarks>
            This method will periodically call UpdateStatusAsync till HasCompleted is true, then return the final result of the operation.
            </remarks>
        </member>
        <member name="T:Azure.Security.KeyVault.Secrets.SecretClient">
            <summary>
            The SecretClient provides synchronous and asynchronous methods to manage <see cref="T:Azure.Security.KeyVault.Secrets.KeyVaultSecret" /> in the Azure Key Vault. The client
            supports creating, retrieving, updating, deleting, purging, backing up, restoring, and listing <see cref="T:Azure.Security.KeyVault.Secrets.KeyVaultSecret" />.
            The client also supports listing <see cref="T:Azure.Security.KeyVault.Secrets.DeletedSecret" /> for a soft-delete enabled Azure Key Vault.
            </summary>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Security.KeyVault.Secrets.SecretClient" /> class for mocking.
            </summary>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.#ctor(System.Uri,Azure.Core.TokenCredential)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Security.KeyVault.Secrets.SecretClient" /> class for the specified vault.
            </summary>
            <param name="vaultUri">
            A <see cref="T:System.Uri" /> to the vault on which the client operates. Appears as "DNS Name" in the Azure portal.
            If you have a secret <see cref="T:System.Uri" />, use <see cref="T:Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier" /> to parse the <see cref="P:Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier.VaultUri" /> and other information.
            </param>
            <param name="credential">A <see cref="T:Azure.Core.TokenCredential" /> used to authenticate requests to the vault, such as DefaultAzureCredential.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="vaultUri" /> or <paramref name="credential" /> is null.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.#ctor(System.Uri,Azure.Core.TokenCredential,Azure.Security.KeyVault.Secrets.SecretClientOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Security.KeyVault.Secrets.SecretClient" /> class for the specified vault.
            </summary>
            <param name="vaultUri">
            A <see cref="T:System.Uri" /> to the vault on which the client operates. Appears as "DNS Name" in the Azure portal.
            If you have a secret <see cref="T:System.Uri" />, use <see cref="T:Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier" /> to parse the <see cref="P:Azure.Security.KeyVault.Secrets.KeyVaultSecretIdentifier.VaultUri" /> and other information.
            </param>
            <param name="credential">A <see cref="T:Azure.Core.TokenCredential" /> used to authenticate requests to the vault, such as DefaultAzureCredential.</param>
            <param name="options"><see cref="T:Azure.Security.KeyVault.Secrets.SecretClientOptions" /> that allow to configure the management of the request sent to Key Vault.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="vaultUri" /> or <paramref name="credential" /> is null.</exception>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretClient.VaultUri">
            <summary>
            Gets the <see cref="T:System.Uri" /> of the vault used to create this instance of the <see cref="T:Azure.Security.KeyVault.Secrets.SecretClient" />.
            </summary>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.GetSecretAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Get a specified secret from a given key vault.
            </summary>
            <remarks>
            The get operation is applicable to any secret stored in Azure Key Vault.
            This operation requires the secrets/get permission.
            </remarks>
            <param name="name">The name of the secret.</param>
            <param name="version">The version of the secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.GetSecret(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Get a specified secret from a given key vault.
            </summary>
            <remarks>
            The get operation is applicable to any secret stored in Azure Key Vault.
            This operation requires the secrets/get permission.
            </remarks>
            <param name="name">The name of the secret.</param>
            <param name="version">The version of the secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.GetPropertiesOfSecretVersionsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Lists the properties of all versions of the specified secret. You can use the returned <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.Name" /> and <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.Version" /> in subsequent calls to <see cref="M:Azure.Security.KeyVault.Secrets.SecretClient.GetSecretAsync(System.String,System.String,System.Threading.CancellationToken)" />.
            </summary>
            <remarks>
            The full secret identifier and attributes are provided in the response. No
            values are returned for the secrets. This operations requires the
            secrets/list permission.
            </remarks>
            <param name="name">The name of the secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.GetPropertiesOfSecretVersions(System.String,System.Threading.CancellationToken)">
            <summary>
            Lists the properties of all versions of the specified secret. You can use the returned <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.Name" /> and <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.Version" /> in subsequent calls to <see cref="M:Azure.Security.KeyVault.Secrets.SecretClient.GetSecret(System.String,System.String,System.Threading.CancellationToken)" />.
            </summary>
            <remarks>
            The full secret identifier and attributes are provided in the response. No
            values are returned for the secrets. This operations requires the
            secrets/list permission.
            </remarks>
            <param name="name">The name of the secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.GetPropertiesOfSecretsAsync(System.Threading.CancellationToken)">
            <summary>
            Lists the properties of all secrets in the specified vault. You can use the returned <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.Name" /> in subsequent calls to <see cref="M:Azure.Security.KeyVault.Secrets.SecretClient.GetSecretAsync(System.String,System.String,System.Threading.CancellationToken)" />.
            </summary>
            <remarks>
            The Get Secrets operation is applicable to the entire vault. However, only
            the base secret identifier and its attributes are provided in the response.
            Individual secret versions are not listed in the response. This operation
            requires the secrets/list permission.
            </remarks>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.GetPropertiesOfSecrets(System.Threading.CancellationToken)">
            <summary>
            Lists the properties of all secrets in the specified vault. You can use the returned <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.Name" /> in subsequent calls to <see cref="M:Azure.Security.KeyVault.Secrets.SecretClient.GetSecret(System.String,System.String,System.Threading.CancellationToken)" />.
            </summary>
            <remarks>
            The Get Secrets operation is applicable to the entire vault. However, only
            the base secret identifier and its attributes are provided in the response.
            Individual secret versions are not listed in the response. This operation
            requires the secrets/list permission.
            </remarks>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.UpdateSecretPropertiesAsync(Azure.Security.KeyVault.Secrets.SecretProperties,System.Threading.CancellationToken)">
            <summary>
            Updates the attributes associated with a specified secret.
            </summary>
            <remarks>
            The update operation changes specified attributes of an existing stored
            secret. Attributes that are not specified in the request are left
            unchanged. The value of a secret itself cannot be changed. This operation
            requires the secrets/set permission.
            </remarks>
            <param name="properties">The secret object with updated properties.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="properties" /> or <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.Version" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.UpdateSecretProperties(Azure.Security.KeyVault.Secrets.SecretProperties,System.Threading.CancellationToken)">
            <summary>
            Updates the attributes associated with a specified secret.
            </summary>
            <remarks>
            The update operation changes specified attributes of an existing stored
            secret. Attributes that are not specified in the request are left
            unchanged. The value of a secret itself cannot be changed. This operation
            requires the secrets/set permission.
            </remarks>
            <param name="properties">The secret object with updated properties.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="properties" /> or <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.Version" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.SetSecretAsync(Azure.Security.KeyVault.Secrets.KeyVaultSecret,System.Threading.CancellationToken)">
            <summary>
            Sets a secret in a specified key vault.
            </summary>
            <remarks>
            The set operation adds a secret to the Azure Key Vault. If the named secret
            already exists, Azure Key Vault creates a new version of that secret. This
            operation requires the secrets/set permission.
            </remarks>
            <param name="secret">The Secret object containing information about the secret and its properties. The properties secret.Name and secret.Value must be non null.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="secret" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.SetSecret(Azure.Security.KeyVault.Secrets.KeyVaultSecret,System.Threading.CancellationToken)">
            <summary>
            Sets a secret in a specified key vault.
            </summary>
            <remarks>
            The set operation adds a secret to the Azure Key Vault. If the named secret
            already exists, Azure Key Vault creates a new version of that secret. This
            operation requires the secrets/set permission.
            </remarks>
            <param name="secret">The Secret object containing information about the secret and its properties. The properties secret.Name and secret.Value must be non null.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="secret" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.SetSecretAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets a secret in a specified key vault.
            </summary>
            <remarks>
            The set operation adds a secret to the Azure Key Vault. If the named secret
            already exists, Azure Key Vault creates a new version of that secret. This
            operation requires the secrets/set permission.
            </remarks>
            <param name="name">The name of the secret. It must not be null.</param>
            <param name="value">The value of the secret. It must not be null.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.SetSecret(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets a secret in a specified key vault.
            </summary>
            <remarks>
            The set operation adds a secret to the Azure Key Vault. If the named secret
            already exists, Azure Key Vault creates a new version of that secret. This
            operation requires the secrets/set permission.
            </remarks>
            <param name="name">The name of the secret.</param>
            <param name="value">The value of the secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.StartDeleteSecretAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Deletes a secret from a specified key vault.
            </summary>
            <remarks>
            The delete operation applies to any secret stored in Azure Key Vault.
            Delete cannot be applied to an individual version of a secret. This
            operation requires the secrets/delete permission.
            </remarks>
            <param name="name">The name of the secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>
            A <see cref="T:Azure.Security.KeyVault.Secrets.DeleteSecretOperation" /> to wait on this long-running operation.
            If the Key Vault is soft delete-enabled, you only need to wait for the operation to complete if you need to recover or purge the secret;
            otherwise, the secret is deleted automatically on the <see cref="P:Azure.Security.KeyVault.Secrets.DeletedSecret.ScheduledPurgeDate" />.
            </returns>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.StartDeleteSecret(System.String,System.Threading.CancellationToken)">
            <summary>
            Deletes a secret from a specified key vault.
            </summary>
            <remarks>
            The delete operation applies to any secret stored in Azure Key Vault.
            Delete cannot be applied to an individual version of a secret. This
            operation requires the secrets/delete permission.
            </remarks>
            <param name="name">The name of the secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>
            A <see cref="T:Azure.Security.KeyVault.Secrets.DeleteSecretOperation" /> to wait on this long-running operation.
            If the Key Vault is soft delete-enabled, you only need to wait for the operation to complete if you need to recover or purge the secret;
            otherwise, the secret is deleted automatically on the <see cref="P:Azure.Security.KeyVault.Secrets.DeletedSecret.ScheduledPurgeDate" />.
            </returns>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.GetDeletedSecretAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets the specified deleted secret.
            </summary>
            <remarks>
            The Get Deleted Secret operation returns the specified deleted secret along
            with its attributes. This operation requires the secrets/get permission.
            </remarks>
            <param name="name">The name of the secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.GetDeletedSecret(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets the specified deleted secret.
            </summary>
            <remarks>
            The Get Deleted Secret operation returns the specified deleted secret along
            with its attributes. This operation requires the secrets/get permission.
            </remarks>
            <param name="name">The name of the secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.GetDeletedSecretsAsync(System.Threading.CancellationToken)">
            <summary>
            Lists deleted secrets for the specified vault.
            </summary>
            <remarks>
            The Get Deleted Secrets operation returns the secrets that have been
            deleted for a vault enabled for soft-delete. This operation requires the
            secrets/list permission.
            </remarks>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.GetDeletedSecrets(System.Threading.CancellationToken)">
            <summary>
            Lists deleted secrets for the specified vault.
            </summary>
            <remarks>
            The Get Deleted Secrets operation returns the secrets that have been
            deleted for a vault enabled for soft-delete. This operation requires the
            secrets/list permission.
            </remarks>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.StartRecoverDeletedSecretAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Recovers the deleted secret to the latest version.
            </summary>
            <remarks>
            Recovers the deleted secret in the specified vault. This operation can only
            be performed on a soft-delete enabled vault. This operation requires the
            secrets/recover permission.
            </remarks>
            <param name="name">The name of the secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>A <see cref="T:Azure.Security.KeyVault.Secrets.RecoverDeletedSecretOperation" /> to wait on this long-running operation.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.StartRecoverDeletedSecret(System.String,System.Threading.CancellationToken)">
            <summary>
            Recovers the deleted secret to the latest version.
            </summary>
            <remarks>
            Recovers the deleted secret in the specified vault. This operation can only
            be performed on a soft-delete enabled vault. This operation requires the
            secrets/recover permission.
            </remarks>
            <param name="name">The name of the secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <returns>A <see cref="T:Azure.Security.KeyVault.Secrets.RecoverDeletedSecretOperation" /> to wait on this long-running operation.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.PurgeDeletedSecretAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Permanently deletes the specified secret.
            </summary>
            <remarks>
            The purge deleted secret operation removes the secret permanently, without
            the possibility of recovery. This operation can only be enabled on a
            soft-delete enabled vault. This operation requires the secrets/purge
            permission.
            </remarks>
            <param name="name">The name of the secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.PurgeDeletedSecret(System.String,System.Threading.CancellationToken)">
            <summary>
            Permanently deletes the specified secret.
            </summary>
            <remarks>
            The purge deleted secret operation removes the secret permanently, without
            the possibility of recovery. This operation can only be enabled on a
            soft-delete enabled vault. This operation requires the secrets/purge
            permission.
            </remarks>
            <param name="name">The name of the secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.BackupSecretAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Backs up the specified secret.
            </summary>
            <remarks>
            Requests that a backup of the specified secret be downloaded to the client.
            All versions of the secret will be downloaded. This operation requires the
            secrets/backup permission.
            </remarks>
            <param name="name">The name of the secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.BackupSecret(System.String,System.Threading.CancellationToken)">
            <summary>
            Backs up the specified secret.
            </summary>
            <remarks>
            Requests that a backup of the specified secret be downloaded to the client.
            All versions of the secret will be downloaded. This operation requires the
            secrets/backup permission.
            </remarks>
            <param name="name">The name of the secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.RestoreSecretBackupAsync(System.Byte[],System.Threading.CancellationToken)">
            <summary>
            Restores a backed up secret to a vault.
            </summary>
            <remarks>
            Restores a backed up secret, and all its versions, to a vault. This
            operation requires the secrets/restore permission.
            </remarks>
            <param name="backup">The backup blob associated with a secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="backup" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClient.RestoreSecretBackup(System.Byte[],System.Threading.CancellationToken)">
            <summary>
            Restores a backed up secret to a vault.
            </summary>
            <remarks>
            Restores a backed up secret, and all its versions, to a vault. This
            operation requires the secrets/restore permission.
            </remarks>
            <param name="backup">The backup blob associated with a secret.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> controlling the request lifetime.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="backup" /> is null.</exception>
            <exception cref="T:Azure.RequestFailedException">The server returned an error. See <see cref="P:System.Exception.Message" /> for details returned from the server.</exception>
        </member>
        <member name="T:Azure.Security.KeyVault.Secrets.SecretClientOptions">
            <summary>
            Options that allow you to configure the requests sent to Key Vault.
            </summary>
        </member>
        <member name="F:Azure.Security.KeyVault.Secrets.SecretClientOptions.LatestVersion">
            <summary>
            The latest service version supported by this client library.
            For more information, see
            <see href="https://docs.microsoft.com/rest/api/keyvault/key-vault-versions">Key Vault versions</see>.
            </summary>
        </member>
        <member name="T:Azure.Security.KeyVault.Secrets.SecretClientOptions.ServiceVersion">
            <summary>
            The versions of Azure Key Vault supported by this client
            library.
            </summary>
        </member>
        <member name="F:Azure.Security.KeyVault.Secrets.SecretClientOptions.ServiceVersion.V7_0">
            <summary>
            The Key Vault API version 7.0.
            </summary>
        </member>
        <member name="F:Azure.Security.KeyVault.Secrets.SecretClientOptions.ServiceVersion.V7_1">
            <summary>
            The Key Vault API version 7.1.
            </summary>
        </member>
        <member name="F:Azure.Security.KeyVault.Secrets.SecretClientOptions.ServiceVersion.V7_2">
            <summary>
            The Key Vault API version 7.2.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretClientOptions.Version">
            <summary>
            Gets the <see cref="T:Azure.Security.KeyVault.Secrets.SecretClientOptions.ServiceVersion" /> of the service API used when
            making requests. For more information, see
            <see href="https://docs.microsoft.com/rest/api/keyvault/key-vault-versions">Key Vault versions</see>.
            </summary>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretClientOptions.#ctor(Azure.Security.KeyVault.Secrets.SecretClientOptions.ServiceVersion)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Security.KeyVault.Secrets.SecretClientOptions" /> class.
            class.
            </summary>
            <param name="version">
            The <see cref="T:Azure.Security.KeyVault.Secrets.SecretClientOptions.ServiceVersion" /> of the service API used when
            making requests.
            </param>
        </member>
        <member name="T:Azure.Security.KeyVault.Secrets.SecretModelFactory">
            <summary>
            Model factory that enables mocking for the Key Vault Secrets library.
            </summary>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretModelFactory.SecretProperties(System.Uri,System.Uri,System.String,System.String,System.Boolean,System.Uri,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Security.KeyVault.Secrets.SecretProperties" /> for mocking purposes.
            </summary>
            <param name="id">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.Id" /> property.</param>
            <param name="vaultUri">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.VaultUri" /> property.</param>
            <param name="name">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.Name" /> property.</param>
            <param name="version">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.Version" /> property.</param>
            <param name="managed">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.Managed" /> property.</param>
            <param name="keyId">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.KeyId" /> property.</param>
            <param name="createdOn">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.CreatedOn" /> property.</param>
            <param name="updatedOn">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.UpdatedOn" /> property.</param>
            <param name="recoveryLevel">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.RecoveryLevel" /> property.</param>
            <returns>A new instance of the <see cref="T:Azure.Security.KeyVault.Secrets.SecretProperties" /> for mocking purposes.</returns>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretModelFactory.SecretProperties(System.Uri,System.Uri,System.String,System.String,System.Boolean,System.Uri,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset},System.String,System.Nullable{System.Int32})">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Security.KeyVault.Secrets.SecretProperties" /> for mocking purposes.
            </summary>
            <param name="id">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.Id" /> property.</param>
            <param name="vaultUri">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.VaultUri" /> property.</param>
            <param name="name">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.Name" /> property.</param>
            <param name="version">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.Version" /> property.</param>
            <param name="managed">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.Managed" /> property.</param>
            <param name="keyId">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.KeyId" /> property.</param>
            <param name="createdOn">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.CreatedOn" /> property.</param>
            <param name="updatedOn">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.UpdatedOn" /> property.</param>
            <param name="recoveryLevel">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.RecoveryLevel" /> property.</param>
            <param name="recoverableDays">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.SecretProperties.RecoverableDays" /> property.</param>
            <returns>A new instance of the <see cref="T:Azure.Security.KeyVault.Secrets.SecretProperties" /> for mocking purposes.</returns>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretModelFactory.KeyVaultSecret(Azure.Security.KeyVault.Secrets.SecretProperties,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Security.KeyVault.Secrets.KeyVaultSecret" /> for mocking purposes.
            </summary>
            <param name="properties">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.KeyVaultSecret.Properties" /> property, which provides the <see cref="P:Azure.Security.KeyVault.Secrets.KeyVaultSecret.Id" /> and <see cref="P:Azure.Security.KeyVault.Secrets.KeyVaultSecret.Name" /> properties.</param>
            <param name="value">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.KeyVaultSecret.Value" /> property.</param>
            <returns>A new instance of the <see cref="T:Azure.Security.KeyVault.Secrets.KeyVaultSecret" /> for mocking purposes.</returns>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretModelFactory.DeletedSecret(Azure.Security.KeyVault.Secrets.SecretProperties,System.String,System.Uri,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Security.KeyVault.Secrets.DeletedSecret" /> for mocking purposes.
            </summary>
            <param name="properties">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.KeyVaultSecret.Properties" /> property, which provides the <see cref="P:Azure.Security.KeyVault.Secrets.KeyVaultSecret.Id" /> and <see cref="P:Azure.Security.KeyVault.Secrets.KeyVaultSecret.Name" /> properties.</param>
            <param name="value">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.KeyVaultSecret.Value" /> property.</param>
            <param name="recoveryId">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.DeletedSecret.RecoveryId" /> property.</param>
            <param name="deletedOn">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.DeletedSecret.DeletedOn" /> property.</param>
            <param name="scheduledPurgeDate">Sets the <see cref="P:Azure.Security.KeyVault.Secrets.DeletedSecret.ScheduledPurgeDate" /> property.</param>
            <returns>A new instance of the <see cref="T:Azure.Security.KeyVault.Secrets.DeletedSecret" /> for mocking purposes.</returns>
        </member>
        <member name="T:Azure.Security.KeyVault.Secrets.SecretProperties">
            <summary>
            <see cref="T:Azure.Security.KeyVault.Secrets.SecretProperties" /> is the resource containing all the properties of the secret except its value.
            </summary>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretProperties.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Security.KeyVault.Secrets.SecretProperties" /> class.
            </summary>
            <param name="name">The name of the secret.</param>
            <exception cref="T:System.ArgumentException"><paramref name="name" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="name" /> is null.</exception>
        </member>
        <member name="M:Azure.Security.KeyVault.Secrets.SecretProperties.#ctor(System.Uri)">
            <summary>
            Initializes a new instance of the <see cref="T:Azure.Security.KeyVault.Secrets.SecretProperties" /> class.
            </summary>
            <param name="id">The identifier of the secret.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="id" /> is null.</exception>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretProperties.Id">
            <summary>
            Gets the secret identifier.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretProperties.VaultUri">
            <summary>
            Gets the Key Vault base <see cref="T:System.Uri" />.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretProperties.Name">
            <summary>
            Gets the name of the secret.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretProperties.Version">
            <summary>
            Gets the version of the secret.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretProperties.ContentType">
            <summary>
            Gets or sets the content type of the secret value such as "text/plain" for a password.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretProperties.Managed">
            <summary>
            Gets a value indicating whether the secret's lifetime is managed by Key Vault.
            If this secret is backing a Key Vault certificate, the value will be true.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretProperties.KeyId">
            <summary>
            Gets the key identifier of a key backing a Key Vault certificate if this secret is backing a Key Vault certifcate.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretProperties.Enabled">
            <summary>
            Gets or sets a value indicating whether the secret is enabled and useable.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretProperties.NotBefore">
            <summary>
            Gets or sets a <see cref="T:System.DateTimeOffset" /> indicating when the secret will be valid and can be used.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretProperties.ExpiresOn">
            <summary>
            Gets or sets a <see cref="T:System.DateTimeOffset" /> indicating when the secret will expire and cannot be used.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretProperties.CreatedOn">
            <summary>
            Gets a <see cref="T:System.DateTimeOffset" /> indicating when the secret was created.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretProperties.UpdatedOn">
            <summary>
            Gets a <see cref="T:System.DateTimeOffset" /> indicating when the secret was updated.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretProperties.RecoverableDays">
            <summary>
            Gets the number of days a secret is retained before being deleted for a soft delete-enabled Key Vault.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretProperties.RecoveryLevel">
            <summary>
            Gets the recovery level currently in effect for secrets in the Key Vault.
            If <c>Purgeable</c>, the secret can be permanently deleted by an authorized user;
            otherwise, only the service can purge the secrets at the end of the retention interval.
            </summary>
            <value>Possible values include <c>Purgeable</c>, <c>Recoverable+Purgeable</c>, <c>Recoverable</c>, and <c>Recoverable+ProtectedSubscription</c>.</value>
        </member>
        <member name="P:Azure.Security.KeyVault.Secrets.SecretProperties.Tags">
            <summary>
            Gets a dictionary of tags with specific metadata about the secret.
            </summary>
        </member>
        <member name="M:Azure.Security.KeyVault.ChallengeBasedAuthenticationPolicy.AuthorizeRequestAsync(Azure.Core.HttpMessage)">
            <summary>
            Executes before <see cref="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.ProcessAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> or
            <see cref="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.Process(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> is called.
            Implementers of this method are expected to call <see cref="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.AuthenticateAndAuthorizeRequest(Azure.Core.HttpMessage,Azure.Core.TokenRequestContext)" /> or <see cref="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.AuthenticateAndAuthorizeRequestAsync(Azure.Core.HttpMessage,Azure.Core.TokenRequestContext)" />
            if authorization is required for requests not related to handling a challenge response.
            </summary><param name="message">The <see cref="T:Azure.Core.HttpMessage" /> this policy would be applied to.</param><returns>The <see cref="T:System.Threading.Tasks.ValueTask" /> representing the asynchronous operation.</returns>
        </member>
        <member name="M:Azure.Security.KeyVault.ChallengeBasedAuthenticationPolicy.AuthorizeRequest(Azure.Core.HttpMessage)">
            <summary>
            Executes before <see cref="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.ProcessAsync(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> or
            <see cref="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.Process(Azure.Core.HttpMessage,System.ReadOnlyMemory{Azure.Core.Pipeline.HttpPipelinePolicy})" /> is called.
            Implementers of this method are expected to call <see cref="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.AuthenticateAndAuthorizeRequest(Azure.Core.HttpMessage,Azure.Core.TokenRequestContext)" /> or <see cref="M:Azure.Core.Pipeline.BearerTokenAuthenticationPolicy.AuthenticateAndAuthorizeRequestAsync(Azure.Core.HttpMessage,Azure.Core.TokenRequestContext)" />
            if authorization is required for requests not related to handling a challenge response.
            </summary><param name="message">The <see cref="T:Azure.Core.HttpMessage" /> this policy would be applied to.</param>
        </member>
        <member name="M:Azure.Security.KeyVault.ChallengeBasedAuthenticationPolicy.AuthorizeRequestOnChallengeAsync(Azure.Core.HttpMessage)">
            <summary>
            Executed in the event a 401 response with a WWW-Authenticate authentication challenge header is received after the initial request.
            </summary><remarks>Service client libraries may override this to handle service specific authentication challenges.</remarks><param name="message">The <see cref="T:Azure.Core.HttpMessage" /> to be authenticated.</param><returns>A boolean indicating whether the request was successfully authenticated and should be sent to the transport.</returns>
        </member>
        <member name="T:Azure.Security.KeyVault.KeyVaultPage`1">
            <summary>
            Defines a page in Azure responses.
            </summary>
            <typeparam name="T">Type of the page content items</typeparam>
        </member>
        <member name="P:Azure.Security.KeyVault.KeyVaultPage`1.Items">
            <summary>
            Gets the content items.
            </summary>
        </member>
        <member name="P:Azure.Security.KeyVault.KeyVaultPage`1.NextLink">
            <summary>
            Gets the link to the next page.
            </summary>
        </member>
        <member name="T:Azure.Core.HashCodeBuilder">
            <summary>
            Copied from https://github.com/dotnet/corefx/blob/master/src/Common/src/CoreLib/System/HashCode.cs.
            </summary>
        </member>
        <member name="T:Azure.Core.AuthorizationChallengeParser">
            <summary>
            A helper class for parsing Authorization challenge headers.
            </summary>
        </member>
        <member name="M:Azure.Core.AuthorizationChallengeParser.GetChallengeParameterFromResponse(Azure.Response,System.String,System.String)">
            <summary>
            Parses the specified parameter from a challenge hearder found in the specified <see cref="T:Azure.Response" />.
            </summary>
            <param name="response">The <see cref="T:Azure.Response" /> to parse.</param>
            <param name="challengeScheme">The challenge scheme containing the <paramref name="challengeParameter" />. For example: "Bearer"</param>
            <param name="challengeParameter">The parameter key name containing the value to return.</param>
            <returns>The value of the parameter name specified in <paramref name="challengeParameter" /> if it is found in the specified <paramref name="challengeScheme" />.</returns>
        </member>
        <member name="M:Azure.Core.AuthorizationChallengeParser.TryGetNextChallenge(System.ReadOnlySpan{System.Char}@,System.ReadOnlySpan{System.Char}@)">
            <summary>
            Iterates through the challenge schemes present in a challenge header.
            </summary>
            <param name="headerValue">
            The header value which will be sliced to remove the first parsed <paramref name="challengeKey" />.
            </param>
            <param name="challengeKey">The parsed challenge scheme.</param>
            <returns>
            <c>true</c> if a challenge scheme was successfully parsed.
            The value of <paramref name="headerValue" /> should be passed to <see cref="M:Azure.Core.AuthorizationChallengeParser.TryGetNextParameter(System.ReadOnlySpan{System.Char}@,System.ReadOnlySpan{System.Char}@,System.ReadOnlySpan{System.Char}@,System.Char)" /> to parse the challenge parameters if <c>true</c>.
            </returns>
        </member>
        <member name="M:Azure.Core.AuthorizationChallengeParser.TryGetNextParameter(System.ReadOnlySpan{System.Char}@,System.ReadOnlySpan{System.Char}@,System.ReadOnlySpan{System.Char}@,System.Char)">
            <summary>
            Iterates through a challenge header value after being parsed by <see cref="M:Azure.Core.AuthorizationChallengeParser.TryGetNextChallenge(System.ReadOnlySpan{System.Char}@,System.ReadOnlySpan{System.Char}@)" />.
            </summary>
            <param name="headerValue">The header value after being parsed by <see cref="M:Azure.Core.AuthorizationChallengeParser.TryGetNextChallenge(System.ReadOnlySpan{System.Char}@,System.ReadOnlySpan{System.Char}@)" />.</param>
            <param name="paramKey">The parsed challenge parameter key.</param>
            <param name="paramValue">The parsed challenge parameter value.</param>
            <param name="separator">The challenge parameter key / value pair separator. The default is '='.</param>
            <returns>
            <c>true</c> if the next available challenge parameter was successfully parsed.
            <c>false</c> if there are no more parameters for the current challenge scheme or an additional challenge scheme was encountered in the <paramref name="headerValue" />.
            The value of <paramref name="headerValue" /> should be passed again to <see cref="M:Azure.Core.AuthorizationChallengeParser.TryGetNextChallenge(System.ReadOnlySpan{System.Char}@,System.ReadOnlySpan{System.Char}@)" /> to attempt to parse any additional challenge schemes if <c>false</c>.
            </returns>
        </member>
        <member name="M:Azure.Core.Base64Url.Decode(System.String)">
            <summary> Converts a Base64URL encoded string to a string.</summary>
            <param name="encoded">The Base64Url encoded string containing UTF8 bytes for a string.</param>
            <returns>The string represented by the Base64URL encoded string.</returns>
        </member>
        <member name="M:Azure.Core.Base64Url.Encode(System.Byte[])">
            <summary>Encode a byte array as a Base64URL encoded string.</summary>
            <param name="bytes">Raw byte input buffer.</param>
            <returns>The bytes, encoded as a Base64URL string.</returns>
        </member>
        <member name="M:Azure.Core.Base64Url.DecodeString(System.String)">
            <summary> Converts a Base64URL encoded string to a string.</summary>
            <param name="encoded">The Base64Url encoded string containing UTF8 bytes for a string.</param>
            <returns>The string represented by the Base64URL encoded string.</returns>
        </member>
        <member name="M:Azure.Core.Base64Url.EncodeString(System.String)">
            <summary>Encode a string as a Base64URL encoded string.</summary>
            <param name="value">String input buffer.</param>
            <returns>The UTF8 bytes for the string, encoded as a Base64URL string.</returns>
        </member>
        <member name="T:Azure.Core.Argument">
            <summary>
            Argument validation.
            </summary>
            <remarks>
              <para>This class should be shared via source using Azure.Core.props and contain only common argument validation.
                It is declared partial so that you can use the same familiar class name but extend it with project-specific validation.
                To extend the functionality of this class, just declare your own partial <see cref="T:Azure.Core.Argument" /> class with project-specific methods.
              </para>
              <para>
                Be sure to document exceptions thrown by these methods on your public methods.
              </para>
            </remarks>
        </member>
        <member name="M:Azure.Core.Argument.AssertNotNull``1(``0,System.String)">
            <summary>
            Throws if <paramref name="value" /> is null.
            </summary>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="value" /> is null.</exception>
        </member>
        <member name="M:Azure.Core.Argument.AssertNotNull``1(System.Nullable{``0},System.String)">
            <summary>
            Throws if <paramref name="value" /> has not been initialized.
            </summary>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="value" /> has not been initialized.</exception>
        </member>
        <member name="M:Azure.Core.Argument.AssertNotNullOrEmpty``1(System.Collections.Generic.IEnumerable{``0},System.String)">
            <summary>
            Throws if <paramref name="value" /> is null or an empty collection.
            </summary>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentException"><paramref name="value" /> is an empty collection.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="value" /> is null.</exception>
        </member>
        <member name="M:Azure.Core.Argument.AssertNotNullOrEmpty(System.String,System.String)">
            <summary>
            Throws if <paramref name="value" /> is null or an empty string.
            </summary>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentException"><paramref name="value" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="value" /> is null.</exception>
        </member>
        <member name="M:Azure.Core.Argument.AssertNotNullOrWhiteSpace(System.String,System.String)">
            <summary>
            Throws if <paramref name="value" /> is null, an empty string, or consists only of white-space characters.
            </summary>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentException"><paramref name="value" /> is an empty string or consists only of white-space characters.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="value" /> is null.</exception>
        </member>
        <member name="M:Azure.Core.Argument.AssertNotDefault``1(``0@,System.String)">
            <summary>
            Throws if <paramref name="value" /> is the default value for type <typeparamref name="T" />.
            </summary>
            <typeparam name="T">The type of structure to validate which implements <see cref="T:System.IEquatable`1" />.</typeparam>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentException"><paramref name="value" /> is the default value for type <typeparamref name="T" />.</exception>
        </member>
        <member name="M:Azure.Core.Argument.AssertInRange``1(``0,``0,``0,System.String)">
            <summary>
            Throws if <paramref name="value" /> is less than the <paramref name="minimum" /> or greater than the <paramref name="maximum" />.
            </summary>
            <typeparam name="T">The type of to validate which implements <see cref="T:System.IComparable`1" />.</typeparam>
            <param name="value">The value to validate.</param>
            <param name="minimum">The minimum value to compare.</param>
            <param name="maximum">The maximum value to compare.</param>
            <param name="name">The name of the parameter.</param>
        </member>
        <member name="M:Azure.Core.Argument.CheckNotNull``1(``0,System.String)">
            <summary>
            Throws if <paramref name="value" /> has not been initialized; otherwise, returns <paramref name="value" />.
            </summary>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="value" /> has not been initialized.</exception>
        </member>
        <member name="M:Azure.Core.Argument.CheckNotNullOrEmpty(System.String,System.String)">
            <summary>
            Throws if <paramref name="value" /> is null or an empty string; otherwise, returns <paramref name="value" />.
            </summary>
            <param name="value">The value to validate.</param>
            <param name="name">The name of the parameter.</param>
            <exception cref="T:System.ArgumentException"><paramref name="value" /> is an empty string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="value" /> is null.</exception>
        </member>
        <member name="T:Azure.Core.AzureResourceProviderNamespaceAttribute">
            <summary>
            This attribute should be set on all client assemblies with value of one of the resource providers
            from the https://docs.microsoft.com/en-us/azure/azure-resource-manager/management/azure-services-resource-providers list.
            </summary>
        </member>
        <member name="T:Azure.Core.ArrayBufferWriter`1">
            <summary>
            Represents a heap-based, array-backed output sink into which <typeparam name="T" /> data can be written.
            </summary>
        </member>
        <member name="M:Azure.Core.ArrayBufferWriter`1.#ctor">
            <summary>
            Creates an instance of an <see cref="T:Azure.Core.ArrayBufferWriter`1" />, in which data can be written to,
            with the default initial capacity.
            </summary>
        </member>
        <member name="M:Azure.Core.ArrayBufferWriter`1.#ctor(System.Int32)">
            <summary>
            Creates an instance of an <see cref="T:Azure.Core.ArrayBufferWriter`1" />, in which data can be written to,
            with an initial capacity specified.
            </summary>
            <param name="initialCapacity">The minimum capacity with which to initialize the underlying buffer.</param>
            <exception cref="T:System.ArgumentException">
            Thrown when <paramref name="initialCapacity" /> is not positive (i.e. less than or equal to 0).
            </exception>
        </member>
        <member name="P:Azure.Core.ArrayBufferWriter`1.WrittenMemory">
            <summary>
            Returns the data written to the underlying buffer so far, as a <see cref="T:System.ReadOnlyMemory`1" />.
            </summary>
        </member>
        <member name="P:Azure.Core.ArrayBufferWriter`1.WrittenSpan">
            <summary>
            Returns the data written to the underlying buffer so far, as a <see cref="T:System.ReadOnlySpan`1" />.
            </summary>
        </member>
        <member name="P:Azure.Core.ArrayBufferWriter`1.WrittenCount">
            <summary>
            Returns the amount of data written to the underlying buffer so far.
            </summary>
        </member>
        <member name="P:Azure.Core.ArrayBufferWriter`1.Capacity">
            <summary>
            Returns the total amount of space within the underlying buffer.
            </summary>
        </member>
        <member name="P:Azure.Core.ArrayBufferWriter`1.FreeCapacity">
            <summary>
            Returns the amount of space available that can still be written into without forcing the underlying buffer to grow.
            </summary>
        </member>
        <member name="M:Azure.Core.ArrayBufferWriter`1.Clear">
            <summary>
            Clears the data written to the underlying buffer.
            </summary>
            <remarks>
            You must clear the <see cref="T:Azure.Core.ArrayBufferWriter`1" /> before trying to re-use it.
            </remarks>
        </member>
        <member name="M:Azure.Core.ArrayBufferWriter`1.Advance(System.Int32)">
            <summary>
            Notifies <see cref="T:System.Buffers.IBufferWriter`1" /> that <paramref name="count" /> amount of data was written to the output <see cref="T:System.Span`1" />/<see cref="T:System.Memory`1" />.
            </summary>
            <exception cref="T:System.ArgumentException">
            Thrown when <paramref name="count" /> is negative.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown when attempting to advance past the end of the underlying buffer.
            </exception>
            <remarks>
            You must request a new buffer after calling Advance to continue writing more data and cannot write to a previously acquired buffer.
            </remarks>
        </member>
        <member name="M:Azure.Core.ArrayBufferWriter`1.GetMemory(System.Int32)">
            <summary>
            Returns a <see cref="T:System.Memory`1" /> to write to that is at least the requested length (specified by <paramref name="sizeHint" />).
            If no <paramref name="sizeHint" /> is provided (or it's equal to <code>0</code>), some non-empty buffer is returned.
            </summary>
            <exception cref="T:System.ArgumentException">
            Thrown when <paramref name="sizeHint" /> is negative.
            </exception>
            <remarks>
            This will never return an empty <see cref="T:System.Memory`1" />.
            </remarks>
            <remarks>
            There is no guarantee that successive calls will return the same buffer or the same-sized buffer.
            </remarks>
            <remarks>
            You must request a new buffer after calling Advance to continue writing more data and cannot write to a previously acquired buffer.
            </remarks>
        </member>
        <member name="M:Azure.Core.ArrayBufferWriter`1.GetSpan(System.Int32)">
            <summary>
            Returns a <see cref="T:System.Span`1" /> to write to that is at least the requested length (specified by <paramref name="sizeHint" />).
            If no <paramref name="sizeHint" /> is provided (or it's equal to <code>0</code>), some non-empty buffer is returned.
            </summary>
            <exception cref="T:System.ArgumentException">
            Thrown when <paramref name="sizeHint" /> is negative.
            </exception>
            <remarks>
            This will never return an empty <see cref="T:System.Span`1" />.
            </remarks>
            <remarks>
            There is no guarantee that successive calls will return the same buffer or the same-sized buffer.
            </remarks>
            <remarks>
            You must request a new buffer after calling Advance to continue writing more data and cannot write to a previously acquired buffer.
            </remarks>
        </member>
        <member name="M:Azure.Core.Pipeline.ClientDiagnostics.ExtractFailureContent(System.String,Azure.Core.ResponseHeaders,System.String@,System.String@,System.Collections.Generic.IDictionary{System.String,System.String}@)">
            <summary>
            Partial method that can optionally be defined to extract the error
            message, code, and details in a service specific manner.
            </summary>
            <param name="content">The error content.</param>
            <param name="responseHeaders">The response headers.</param>
            <param name="message">The error message.</param>
            <param name="errorCode">The error code.</param>
            <param name="additionalInfo">Additional error details.</param>
        </member>
        <member name="T:Azure.Core.Pipeline.ActivityExtensions">
            <summary>
            HACK HACK HACK. Some runtime environments like Azure.Functions downgrade System.Diagnostic.DiagnosticSource package version causing method not found exceptions in customer apps
            This type is a temporary workaround to avoid the issue.
            </summary>
        </member>
        <member name="T:Azure.Core.Pipeline.TaskExtensions.Enumerable`1">
            <summary>
            Both <see cref="T:Azure.Core.Pipeline.TaskExtensions.Enumerable`1" /> and <see cref="T:Azure.Core.Pipeline.TaskExtensions.Enumerator`1" /> are defined as public structs so that foreach can use duck typing
            to call <see cref="M:Azure.Core.Pipeline.TaskExtensions.Enumerable`1.GetEnumerator" /> and avoid heap memory allocation.
            Please don't delete this method and don't make these types private.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="T:Microsoft.Extensions.Azure.SecretClientBuilderExtensions">
            <summary>
            Extension methods to add <see cref="T:Azure.Security.KeyVault.Secrets.SecretClient" /> to clients builder.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Azure.SecretClientBuilderExtensions.AddSecretClient``1(``0,System.Uri)">
            <summary>
            Registers a <see cref="T:Azure.Security.KeyVault.Secrets.SecretClient" /> instance with the provided <paramref name="vaultUri" />
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Azure.SecretClientBuilderExtensions.AddSecretClient``2(``0,``1)">
            <summary>
            Registers a <see cref="T:Azure.Security.KeyVault.Secrets.SecretClient" /> instance with connection options loaded from the provided <paramref name="configuration" /> instance.
            </summary>
        </member>
        <member name="M:Azure.Core.Pipeline.TaskExtensions.Enumerable`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary><returns>An enumerator that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Azure.Core.Pipeline.TaskExtensions.Enumerable`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="P:Azure.Core.Pipeline.TaskExtensions.Enumerator`1.System#Collections#IEnumerator#Current">
            <summary>Gets the element in the collection at the current position of the enumerator.</summary><returns>The element in the collection at the current position of the enumerator.</returns>
        </member>
    </members>
</doc>
