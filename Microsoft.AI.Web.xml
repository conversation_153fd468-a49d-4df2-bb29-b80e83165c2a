<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AI.Web</name>
    </assembly>
    <members>
        <member name="T:Microsoft.ApplicationInsights.Web.AspNetDiagnosticTelemetryModule">
            <summary>
            Listens to ASP.NET DiagnosticSource and enables instrumentation with Activity: let ASP.NET create root Activity for the request.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.AspNetDiagnosticTelemetryModule.isEnabled">
            <summary>
            Indicates if module initialized successfully.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.AspNetDiagnosticTelemetryModule.Initialize(Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration)">
            <summary>
            Initializes the telemetry module.
            </summary>
            <param name="configuration">Telemetry configuration to use for initialization.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.AspNetDiagnosticTelemetryModule.OnNext(System.Diagnostics.DiagnosticListener)">
            <summary>
            Implements IObserver OnNext callback, subscribes to AspNet DiagnosticSource.
            </summary>
            <param name="value">DiagnosticListener value.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.AspNetDiagnosticTelemetryModule.Dispose">
            <summary>
            Disposes all subscriptions to DiagnosticSources.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.AspNetDiagnosticTelemetryModule.OnError(System.Exception)">
            <summary>
            IObserver OnError callback.
            </summary>
            <param name="error">Exception instance.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.AspNetDiagnosticTelemetryModule.OnCompleted">
            <summary>
            IObserver OnCompleted callback.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.AspNetDiagnosticTelemetryModule.Dispose(System.Boolean)">
            <summary>
            Implements IDisposable pattern. Dispose() should call Dispose(true), and the finalizer should call Dispose(false).
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.Implementation.HttpRequestExtensions">
            <summary>
            HttpRequest Extensions.
            </summary>
            <summary>
            HttpRequest Extensions.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.Inject(Microsoft.ApplicationInsights.TelemetryClient)">
            <summary>
            Forces injection of MVC5 exception filter and WebAPI2 exception logger into the global configurations.
            </summary>
            <remarks>
            <para>Injection is attempted each time method is called. However if the filter/logger was injected already, injection is skipped.</para>
            <para>Use this method only when you can guarantee it's called once per AppDomain.</para>
            </remarks>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.AddMvcFilter(Microsoft.ApplicationInsights.TelemetryClient,System.Reflection.Emit.ModuleBuilder,System.Reflection.ConstructorInfo,System.Reflection.MethodInfo)">
            <summary>
            Generates new MVC5 filter class implementing HandleErrorAttribute and adds instance of it to the GlobalFilterCollection.
            </summary>
            <param name="telemetryClient"><see cref="T:Microsoft.ApplicationInsights.TelemetryClient"/> instance.</param>
            <param name="moduleBuilder"><see cref="T:System.Reflection.Emit.ModuleBuilder"/> to define type in.</param>
            <param name="exceptionTelemetryCtor"><see cref="T:System.Reflection.ConstructorInfo"/> of default constructor of <see cref="T:Microsoft.ApplicationInsights.DataContracts.ExceptionTelemetry"/>.</param>
            <param name="trackExceptionMethod"><see cref="T:System.Reflection.MethodInfo"/> of <see cref="M:Microsoft.ApplicationInsights.TelemetryClient.TrackException(Microsoft.ApplicationInsights.DataContracts.ExceptionTelemetry)"/>.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.EmitMvcOnException(System.Reflection.Emit.TypeBuilder,System.Type,System.Reflection.FieldInfo,System.Reflection.MethodInfo,System.Reflection.MethodInfo,System.Reflection.ConstructorInfo,System.Type,System.Reflection.MethodInfo,System.Reflection.MethodInfo)">
            <summary>
            Emits OnException method.
            </summary>
            <param name="typeBuilder">MVCExceptionFilter type builder.</param>
            <param name="exceptionContextType">Type of ExceptionContext.</param>
            <param name="telemetryClientField">FieldInfo of MVCExceptionFilter.telemetryClient.</param>
            <param name="exceptionGetter">MethodInfo to get ExceptionContext.Exception.</param>
            <param name="trackException">MethodInfo of TelemetryClient.TrackException(ExceptionTelemetry).</param>
            <param name="exceptionTelemetryCtor">ConstructorInfo of ExceptionTelemetry.</param>
            <param name="httpContextBaseType">Type of HttpContextBase.</param>
            <param name="httpContextGetter">MethodInfo to get ExceptionContext.HttpContext.</param>
            <param name="isCustomErrorEnabled">MethodInfo to get ExceptionContext.HttpContextBase.IsCustomErrorEnabled.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.GetMvcGlobalFiltersOrFail(System.Object@,System.Type@)">
            <summary>
            Gets GlobalFilters.Filters property value. If not resolved, or is null, fails with <see cref="T:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.ResolutionException"/>.
            </summary>
            <param name="globalFilters">Resolved GlobalFilters.Filters instance.</param>
            <param name="globalFilterCollectionType">Resolved GlobalFilterCollection type.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.NeedToInjectMvc(System.Object)">
            <summary>
            Checks if another auto injected filter was already added into the filter collection.
            </summary>
            <param name="globalFilters">GlobalFilters.Filters value of GlobalFilterCollection type.</param>
            <returns>True if injection needs to be done, false when collection already contains another auto injected filter.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.AddWebApiExceptionLogger(Microsoft.ApplicationInsights.TelemetryClient,System.Reflection.Emit.ModuleBuilder,System.Reflection.ConstructorInfo,System.Reflection.MethodInfo)">
            <summary>
            Generates new WebAPI2 exception logger class implementing ExceptionLogger and adds instance of it to the GlobalConfiguration.Configuration.Services of IExceptionLogger type.
            </summary>
            <param name="telemetryClient"><see cref="T:Microsoft.ApplicationInsights.TelemetryClient"/> instance.</param>
            <param name="moduleBuilder"><see cref="T:System.Reflection.Emit.ModuleBuilder"/> to define type in.</param>
            <param name="exceptionTelemetryCtor"><see cref="T:System.Reflection.ConstructorInfo"/> of default constructor of <see cref="T:Microsoft.ApplicationInsights.DataContracts.ExceptionTelemetry"/>.</param>
            <param name="trackExceptionMethod"><see cref="T:System.Reflection.MethodInfo"/> of <see cref="M:Microsoft.ApplicationInsights.TelemetryClient.TrackException(Microsoft.ApplicationInsights.DataContracts.ExceptionTelemetry)"/>.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.EmitWebApiLog(System.Reflection.Emit.TypeBuilder,System.Type,System.Reflection.MethodInfo,System.Reflection.FieldInfo,System.Reflection.ConstructorInfo,System.Reflection.MethodInfo)">
            <summary>
            Emits OnLog method.
            </summary>
            <param name="typeBuilder">MVCExceptionFilter type builder.</param>
            <param name="exceptionContextType">Type of ExceptionContext.</param>
            <param name="exceptionGetter">MethodInfo to get ExceptionLoggerContext.Exception.</param>
            <param name="telemetryClientField">FieldInfo of WebAPIExceptionFilter.telemetryClient.</param>
            <param name="exceptionTelemetryCtor">ConstructorInfo of ExceptionTelemetry.</param>
            <param name="trackException">MethodInfo of TelemetryClient.TrackException(ExceptionTelemetry).</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.GetServicesContainerWebApiOrFail(System.Object@,System.Type@)">
            <summary>
            Gets GlobalConfiguration.Configuration.Services value and type.
            </summary>
            <param name="serviceContaner">Services collection.</param>
            <param name="servicesContainerType">ServicesContainer type of Services.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.NeedToInjectWebApi(System.Object,System.Type,System.Type)">
            <summary>
            Checks if another auto injected logger was already added into the Services collection.
            </summary>
            <param name="servicesContainer">GlobalConfiguration.Configuration.Services value.</param>
            <param name="servicesContainerType">ServicesContainer type.</param>
            <param name="iexceptionLoggerType">IExceptionLogger type.</param>
            <returns>True if injection needs to be done, false when collection already contains another auto injected logger.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.EmitConstructor(System.Reflection.Emit.TypeBuilder,System.Type,System.Reflection.FieldInfo,System.Reflection.ConstructorInfo)">
            <summary>
            Emits constructor for MVC Filter and WebAPI logger.
            </summary>
            <param name="typeBuilder">TypeBuilder of MVC filter or WebAPI logger.</param>
            <param name="telemetryClientType">Type of TelemetryClient.</param>
            <param name="field">FieldInfo to assign TelemetryClient instance to.</param>
            <param name="baseCtorInfo">ConstructorInfo of the base class.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.DefineAutoInjectedField(System.Reflection.Emit.TypeBuilder)">
            <summary>
            Emits IsAutoInjected field. The field is used to mark injected filter/logger and prevent double-injection.
            </summary>
            <param name="exceptionHandlerType">Type of the exception handler.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.GetUsageAttributeOrFail">
            <summary>
            Gets attribute builder for AttributeUsageAttribute with AllowMultiple set to true.
            </summary>
            <returns>CustomAttributeBuilder for the AttributeUsageAttribute.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.GetTypeOrFail(System.String)">
            <summary>
            Gets type by it's name and throws <see cref="T:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.ResolutionException"/> if type is not found.
            </summary>
            <param name="typeName">Name of the type to be found. It could be a short namespace qualified name or assembly qualified name, as appropriate.</param>
            <returns>Resolved <see cref="T:System.Type"/>.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.GetMethodOrFail(System.Type,System.String,System.Type[])">
            <summary>
            Gets public instance method info from the given type with the given of parameters. Throws <see cref="T:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.ResolutionException"/> if method is not found.
            </summary>
            <param name="type">Type to get method from.</param>
            <param name="methodName">Method name.</param>
            <param name="paramTypes">Array of method parameters. Optional (empty array by default).</param>
            <returns>Resolved <see cref="T:System.Reflection.MethodInfo"/>.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.GetConstructorOrFail(System.Type,System.Type[])">
            <summary>
            Gets public instance constructor info from the given type with the given of parameters. Throws <see cref="T:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.ResolutionException"/> if constructor is not found.
            </summary>
            <param name="type">Type to get constructor from.</param>
            <param name="paramTypes">Array of constructor parameters.</param>
            <returns>Resolved <see cref="T:System.Reflection.ConstructorInfo"/>.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.GetPropertyOrFail(System.Type,System.String)">
            <summary>
            Gets public instance property info from the given type. Throws <see cref="T:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.ResolutionException"/> if property is not found.
            </summary>
            <param name="type">Type to get property from.</param>
            <param name="propertyName">Name or the property to get.</param>
            <returns>Resolved <see cref="T:System.Reflection.PropertyInfo"/>.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.GetPropertyValueOrFail(System.Type,System.Object,System.String)">
            <summary>
            Gets public instance property value from the given type. Throws <see cref="T:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.ResolutionException"/> if property is not found.
            </summary>
            <param name="type">Type to get property from.</param>
            <param name="instance">Instance of type to get property value from.</param>
            <param name="propertyName">Name or the property to get.</param>
            <returns>Value of the property.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.GetStaticPropertyValueOrFail(System.Type,System.String)">
            <summary>
            Gets public static property value from the given type. Throws <see cref="T:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.ResolutionException"/> if property is not found.
            </summary>
            <param name="type">Type to get property from.</param>
            <param name="propertyName">Name of the property to get.</param>
            <returns>Value of the property.</returns>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.Implementation.ExceptionHandlersInjector.ResolutionException">
            <summary>
            Represents specific resolution exception.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.Implementation.RequestTrackingConstants">
            <summary>
            Request tracking constants and keys.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.Implementation.RequestTrackingConstants.RequestTelemetryItemName">
            <summary>
            Name of the HttpContext item containing RequestTelemetry object.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.Implementation.RequestTrackingConstants.TransferHandlerType">
            <summary>
            Type name for the transfer handler. This handler is used to enable extension(less) URI 
            and it produces extra request, which should not be counted.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.Implementation.RequestTrackingConstants.WebAuthenticatedUserCookieName">
            <summary>
            The name of the cookie which holds authenticated user context information.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.RequestTrackingExtensions.CreateRequestNamePrivate(System.Web.HttpContext)">
            <summary>
            Creates request name on the base of HttpContext.
            </summary>
            <returns>Controller/Action for MVC or path for other cases.</returns>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.Implementation.SyntheticUserAgentFilter">
            <summary>
            Allows configuration of patterns for synthetic traffic filters.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.Implementation.SyntheticUserAgentFilter.Pattern">
            <summary>
            Gets or sets the regular expression pattern applied to the user agent string to determine whether traffic is synthetic.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.Implementation.SyntheticUserAgentFilter.SourceName">
            <summary>
            Gets or sets the readable name for the synthetic traffic source. If not provided, defaults to the pattern match.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.Implementation.WebEventSource">
            <summary>
            ETW EventSource tracing class.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.Implementation.WebEventSource.Log">
            <summary>
            Instance of the PlatformEventSource class.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.Implementation.WebEventSource.Keywords">
            <summary>
            Keywords for the PlatformEventSource. Those keywords should match keywords in Core.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.Implementation.WebEventSource.Keywords.UserActionable">
            <summary>
            Key word for user actionable events.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.Implementation.WebEventSource.Keywords.Diagnostics">
            <summary>
            Diagnostics tracing keyword.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.Implementation.WebEventSource.Keywords.VerboseFailure">
            <summary>
            Keyword for errors that trace at Verbose level.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryInitializerBase">
            <summary>
            Base class for WebOperationTelemetryInitializers.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryInitializerBase.Initialize(Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Base implementation of the initialization method.
            </summary>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryInitializerBase.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryInitializerBase.ResolvePlatformContext">
            <summary>
            Resolved web platform specific context.
            </summary>
            <returns>An instance of the context.</returns>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryModuleBase">
            <summary>
            Base web telemetry module.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryModuleBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryModuleBase"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryModuleBase.ModuleName">
            <summary>
            Gets the module name which is added to be used for internal tracing instead of GetType on each request to improve performance.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryModuleBase.OnBeginRequest(Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,System.Web.HttpContext)">
            <summary>
            Post initialization Web Telemetry Module callback.
            </summary>
            <param name="requestTelemetry">An instance of request telemetry context.</param>
            <param name="platformContext">Platform specific context.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryModuleBase.OnEndRequest(Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,System.Web.HttpContext)">
            <summary>
            Request telemetry finalization - sending callback Web Telemetry Module callback.
            </summary>
            <param name="requestTelemetry">An instance of request telemetry context.</param>
            <param name="platformContext">Platform specific context.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryModuleBase.OnError(Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,System.Web.HttpContext)">
            <summary>
            Http Error reporting Web Telemetry Module callback.
            </summary>
            <param name="requestTelemetry">An instance of request telemetry context.</param>
            <param name="platformContext">Platform specific context.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.AccountIdTelemetryInitializer">
            <summary>
            A telemetry initializer that will set the User properties of Context corresponding to a RequestTelemetry object.
            User.AccountId is updated with properties derived from the RequestTelemetry.RequestTelemetry.Context.User.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.AccountIdTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule">
            <summary>
            Platform agnostic module for web application instrumentation.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule.isEnabled">
            <summary>
            Indicates if module initialized successfully.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule.Init(System.Web.HttpApplication)">
            <summary>
            Initializes module for a given application.
            </summary>
            <param name="context">HttpApplication instance.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule.Dispose">
            <summary>
            Required IDisposable implementation.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule.CreateOpenDelegate(System.Reflection.MethodInfo)">
            <summary>
            Creates open delegate for faster invocation than regular Invoke.        
            </summary>
            <param name="mi">MethodInfo for which open delegate is to be created.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule.AddCorrelationHeaderOnSendRequestHeaders(System.Web.HttpApplication)">
            <summary>
            When sending the response headers, allow request module to add the IKey's target hash.
            </summary>
            <param name="httpApplication">HttpApplication instance.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.AuthenticatedUserIdTelemetryInitializer">
            <summary>
            A telemetry initializer that will set the User properties of Context corresponding to a RequestTelemetry object.
            User.AuthenticatedUserId is updated with properties derived from the RequestTelemetry.RequestTelemetry.Context.User.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.AuthenticatedUserIdTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.OperationCorrelationTelemetryInitializer">
            <summary>
            A telemetry initializer that will set the correlation context for all telemetry items in web application.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.OperationCorrelationTelemetryInitializer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.ApplicationInsights.Web.OperationCorrelationTelemetryInitializer"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.OperationCorrelationTelemetryInitializer.ParentOperationIdHeaderName">
            <summary>
            Gets or sets the name of the header to get parent operation Id from.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.OperationCorrelationTelemetryInitializer.RootOperationIdHeaderName">
            <summary>
            Gets or sets the name of the header to get root operation Id from.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.OperationCorrelationTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.ClientIpHeaderTelemetryInitializer">
            <summary>
            Telemetry initializer populates client IP address for the current request.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ClientIpHeaderTelemetryInitializer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.ApplicationInsights.Web.ClientIpHeaderTelemetryInitializer"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.ClientIpHeaderTelemetryInitializer.HeaderNames">
            <summary>
            Gets a list of request header names that is used to check client id.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.ClientIpHeaderTelemetryInitializer.HeaderValueSeparators">
            <summary>
            Gets or sets a header values separator.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.ClientIpHeaderTelemetryInitializer.UseFirstIp">
            <summary>
            Gets or sets a value indicating whether the first or the last IP should be used from the lists of IPs in the header.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ClientIpHeaderTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.ExceptionTrackingTelemetryModule">
            <summary>
            Telemetry module to collect unhandled exceptions caught by http module.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.ExceptionTrackingTelemetryModule.EnableMvcAndWebApiExceptionAutoTracking">
            <summary>
            Gets or sets a value indicating whether automatic MVC 5 and WebAPI 2 exceptions tracking should be done.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ExceptionTrackingTelemetryModule.OnError(System.Web.HttpContext)">
            <summary>
            Implements on error callback of http module.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ExceptionTrackingTelemetryModule.Initialize(Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration)">
            <summary>
            Initializes the telemetry module.
            </summary>
            <param name="configuration">Telemetry configuration to use for initialization.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ExceptionTrackingTelemetryModule.Dispose">
            <summary>
            IDisposable implementation.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ExceptionTrackingTelemetryModule.Dispose(System.Boolean)">
            <summary>
            IDisposable implementation.
            </summary>
            <param name="disposing">The method has been called directly or indirectly by a user's code.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.OperationNameTelemetryInitializer">
            <summary>
            A telemetry initializer that will set the NAME property of OperationContext corresponding to a TraceTelemetry object.
            If the telemetry object is of type RequestTelemetry, then the Name of the RequestTelemetry is updated. For all other cases,
            Operation.Name is updated with the name derived from the HttpContext.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.OperationNameTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="rootRequestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule">
            <summary>
            Telemetry module tracking requests using http module.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.EnableChildRequestTrackingSuppression">
            <summary>
            Gets or sets a value indicating whether child request suppression is enabled or disabled. 
            True by default.
            This value is evaluated in Initialize().
            </summary>
            <remarks>
            See also <see cref="T:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.ChildRequestTrackingSuppressionModule" />.
            Child requests caused by <see cref="T:System.Web.Handlers.TransferRequestHandler" />.
            Unit tests should disable this.
            </remarks>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.EnableAccessControlExposeHeader">
            <summary>
            Gets or sets a value indicating whether Request-Id header is added to Access-Control-Expose-Headers or not. 
            True by default.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.ChildRequestTrackingInternalDictionarySize">
            <summary>
            Gets or sets a value indicating the size of internal tracking dictionary.
            Must be a positive integer.
            </summary>
            <remarks>
            See also <see cref="T:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.ChildRequestTrackingSuppressionModule" />.
            </remarks>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.Handlers">
            <summary>
            Gets the list of handler types for which requests telemetry will not be collected
            if request was successful.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.SetComponentCorrelationHttpHeaders">
            <summary>
            Gets or sets a value indicating whether the component correlation headers would be set on http responses.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.EnableW3CHeadersExtraction">
            <summary>
            Gets or sets a value indicating whether to enable W3C distributed tracing headers support.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.ProfileQueryEndpoint">
            <summary>
            Gets or sets the endpoint that is to be used to get the application insights resource's profile (appId etc.).
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.OnBeginRequest(System.Web.HttpContext)">
            <summary>
            Implements on begin callback of http module.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.OnEndRequest(System.Web.HttpContext)">
            <summary>
            Implements on end callback of http module.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.AddTargetHashForResponseHeader(System.Web.HttpContext)">
            <summary>
            Adds target response header response object.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.Initialize(Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration)">
            <summary>
            Initializes the telemetry module.
            </summary>
            <param name="configuration">Telemetry configuration to use for initialization.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.TrackIntermediateRequest(System.Web.HttpContext,System.Diagnostics.Activity)">
            <summary>
            Tracks intermediate request if Activity was lost on the way to ASP.NET.
            This request ensures we can correlate high-level request and dependency call.
            This method should be removed with DiagnosticSource released along with .NET Core 2.2.
            </summary>
            <param name="context">Current HttpContext.</param>
            <param name="activity">Restored activity.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.NeedProcessRequest(System.Web.HttpContext)">
            <summary>
            Verifies context to detect whether or not request needs to be processed.
            </summary>
            <param name="httpContext">Current http context.</param>
            <returns>True if request needs to be processed, otherwise - False.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.IsHandlerToFilter(System.Web.IHttpHandler)">
            <summary>
            Checks whether or not handler is a transfer handler.
            </summary>
            <param name="handler">An instance of handler to validate.</param>
            <returns>True if handler is a transfer handler, otherwise - False.</returns>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.ChildRequestTrackingSuppressionModule">
            <summary>
            <see cref="T:System.Web.Handlers.TransferRequestHandler"/> can create a Child request to route extension-less requests to a controller.
            (ex: site/home -> site/HomeController.cs)
            We do not want duplicate telemetry logged for both the Parent and Child requests, so the activeRequests will be created OnBeginRequest.
            When the child request OnEndRequest, the id will be removed from this dictionary and telemetry will not be logged for the parent.
            </summary>
            <remarks>
            Unit tests should disable the ChildRequestTrackingSuppressionModule.
            Unit test projects cannot create an [internal] IIS7WorkerRequest object.
            Without this object, we cannot modify the Request.Headers without throwing a PlatformNotSupportedException.
            Unit tests will have to initialize the RequestIdHeader.
            The second IF will ensure the id is added to the activeRequests.
            </remarks>
            <remarks>
            IIS Classic Pipeline should disable the ChildRequestTrackingSuppressionModule.
            Classic does not create IIS7WorkerRequest object and Headers will be read-only.
            (Exception System.PlatformNotSupportedException: This operation requires IIS integrated pipeline mode.)
            </remarks>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.ChildRequestTrackingSuppressionModule.activeRequestsA">
            <summary>
            Using this as a hash-set of current active requests. The value of the Dictionary is not used.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.ChildRequestTrackingSuppressionModule.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.ChildRequestTrackingSuppressionModule" /> class.
            </summary>
            <param name="maxRequestsTracked">The maximum number of active requests to be tracked before resetting the dictionary.</param>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.ChildRequestTrackingSuppressionModule.MAXSIZE">
            <summary>
            Gets the Max number of request ids to cache.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.ChildRequestTrackingSuppressionModule.OnBeginRequest_IdRequest(System.Web.HttpContext)">
            <summary>
            Request will be tagged with an id to identify if it should be logged later.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.ChildRequestTrackingSuppressionModule.OnEndRequest_ShouldLog(System.Web.HttpContext)">
            <summary>
            OnEndRequest - Should this request be logged?
            Will compare a request id against a hash-set of known requests.
            If this request is not known, add it to hash-set and return true (safe to log).
            If this request is known, return false (do not log twice).
            Additional requests with the same id will return false.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.ChildRequestTrackingSuppressionModule.TagRequest(System.Web.HttpContext)">
            <summary>
            Tag new requests.
            Transfer Ids to parent requests.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.ChildRequestTrackingSuppressionModule.IsRequestKnown(System.String)">
            <summary>
            Has this request been tracked.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.ChildRequestTrackingSuppressionModule.AddRequestToDictionary(System.String)">
            <summary>
            Track this requestId.
            </summary>
            <remarks>
            Dictionary A will be read/write.
            When dictionary A is full, move to B and create new A.
            Dictionary B will be read-only.
            </remarks>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.SessionTelemetryInitializer">
            <summary>
            A telemetry initializer that will set the Session properties of Context corresponding to a RequestTelemetry object.
            Session is updated with properties derived from the RequestTelemetry.RequestTelemetry.Context.Session.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.SessionTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.WebTestTelemetryInitializer">
            <summary>
            A telemetry initializer that will update the User, Session and Operation contexts if request originates from a web test.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.WebTestTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.SyntheticUserAgentTelemetryInitializer">
            <summary>
            A telemetry initializer that determines if the request came from a synthetic source based on the user agent string.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.SyntheticUserAgentTelemetryInitializer.Filters">
            <summary>
            Gets or sets the configured patterns for matching synthetic traffic filters through user agent string.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.SyntheticUserAgentTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.UserTelemetryInitializer">
            <summary>
            A telemetry initializer that will set the User properties of Context corresponding to a RequestTelemetry object.
            User.Id are updated with properties derived from the RequestTelemetry.RequestTelemetry.Context.User.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.UserTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.ActivityHelpers.IsHierarchicalRequestId(System.String)">
            <summary> 
            Checks if given RequestId is hierarchical.
            </summary>
            <param name="requestId">Request id.</param>
            <returns>True if requestId is hierarchical false otherwise.</returns>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.AppMapCorrelationEventSource">
            <summary>
            ETW EventSource tracing class.
            </summary>
            <summary>
            ETW EventSource tracing class.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.AppMapCorrelationEventSource.Keywords">
            <summary>
            Keywords for the <see cref="T:Microsoft.ApplicationInsights.Common.AppMapCorrelationEventSource"/>.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.AppMapCorrelationEventSource.Keywords.UserActionable">
            <summary>
            Key word for user actionable events.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.AppMapCorrelationEventSource.Keywords.Diagnostics">
            <summary>
            Key word for diagnostics events.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.ArrayExtensions.Empty``1">
            <summary>
            Returns an empty array.
            </summary>
            <remarks>
            Array.Empty() was added to Net Framework in 4.6
            This adds support for Net45.
            </remarks>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.ArrayExtensions.EmptyArray`1">
            <summary>
            [Net45 Only] Copied from Net Framework (https://referencesource.microsoft.com/#mscorlib/system/array.cs,bc9fd1be0e4f4e70,references).
            </summary>
            <remarks>
            Array.Empty() was added to Net Framework in 4.6
            This adds support for Net45.
            </remarks>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.ConditionalWeakTableExtensions">
            <summary>
            Extension methods for the ConditionalWeakTable class.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.ConditionalWeakTableExtensions.AddIfNotExists``2(System.Runtime.CompilerServices.ConditionalWeakTable{``0,``1},``0,``1)">
            <summary>
            Check if a key exists before adding the key/value pair.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.ExceptionUtilities">
            <summary>
            Utility functions for dealing with exceptions.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.ExceptionUtilities.GetExceptionDetailString(System.Exception)">
            <summary>
            Get the string representation of this Exception with special handling for AggregateExceptions.
            </summary>
            <param name="ex">The exception to convert to a string.</param>
            <returns>The detailed string version of the provided exception.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.GuidExtensions.ToStringInvariant(System.Guid,System.String)">
            <summary>
            Overload for Guid.ToString(). 
            </summary>
            <remarks>
            This method encapsulates the language switch for NetStandard and NetFramework and resolves the error "The behavior of guid.ToStrinc() could vary based on the current user's locale settings".
            </remarks>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.HeadersUtilities">
            <summary>
            Generic functions that can be used to get and set Http headers.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.HeadersUtilities.GetHeaderKeyValue(System.Collections.Generic.IEnumerable{System.String},System.String)">
            <summary>
            Get the key value from the provided HttpHeader value that is set up as a comma-separated list of key value pairs. Each key value pair is formatted like (key)=(value).
            </summary>
            <param name="headerValues">The header values that may contain key name/value pairs.</param>
            <param name="keyName">The name of the key value to find in the provided header values.</param>
            <returns>The first key value, if it is found. If it is not found, then null.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.HeadersUtilities.UpdateHeaderWithKeyValue(System.Collections.Generic.IEnumerable{System.String},System.String,System.String)">
            <summary>
            Given the provided list of header value strings, return a list of key name/value pairs
            with the provided keyName and keyValue. If the initial header value strings contains
            the key name, then the original key value should be replaced with the provided key
            value. If the initial header value strings don't contain the key name, then the key
            name/value pair should be added to the list and returned.
            </summary>
            <param name="headerValues">The existing header values that the key/value pair should be added to.</param>
            <param name="keyName">The name of the key to add.</param>
            <param name="keyValue">The value of the key to add.</param>
            <returns>The result of setting the provided key name/value pair into the provided headerValues.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.HeadersUtilities.SanitizeString(System.String)">
            <summary>
            Http Headers only allow Printable US-ASCII characters.
            Remove all other characters.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants">
            <summary>
            These values are listed to guard against malicious injections by limiting the max size allowed in an HTTP Response.
            These max limits are intentionally exaggerated to allow for unexpected responses, while still guarding against unreasonably large responses.
            Example: While a 32 character response may be expected, 50 characters may be permitted while a 10,000 character response would be unreasonable and malicious.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants.AppIdMaxLength">
            <summary>
            Max length of AppId allowed in response from Breeze.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants.RequestHeaderMaxLength">
            <summary>
            Max length of incoming Request Header value allowed.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants.ContextHeaderKeyMaxLength">
            <summary>
            Max length of context header key.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants.ContextHeaderValueMaxLength">
            <summary>
            Max length of context header value.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants.TraceParentHeaderMaxLength">
            <summary>
            Max length of traceparent header value.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants.TraceStateHeaderMaxLength">
            <summary>
            Max length of tracestate header value string.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.Internal.InjectionGuardConstants.TraceStateMaxPairs">
            <summary>
            Max number of key value pairs in the tracestate header.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.Internal.StringUtilities">
            <summary>
            Generic functions to perform common operations on a string.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.Internal.StringUtilities.EnforceMaxLength(System.String,System.Int32)">
            <summary>
            Check a strings length and trim to a max length if needed.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.Internal.StringUtilities.GenerateTraceId">
            <summary>
            Generates random trace Id as per W3C Distributed tracing specification.
            https://github.com/w3c/distributed-tracing/blob/master/trace_context/HTTP_HEADER_FORMAT.md#trace-id
            </summary>
            <returns>Random 16 bytes array encoded as hex string</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.Internal.StringUtilities.GenerateSpanId">
            <summary>
            Generates random span Id as per W3C Distributed tracing specification.
            https://github.com/w3c/distributed-tracing/blob/master/trace_context/HTTP_HEADER_FORMAT.md#span-id
            </summary>
            <returns>Random 8 bytes array encoded as hex string</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.Internal.StringUtilities.FormatRequestId(System.String,System.String)">
            <summary>
            Formats trace Id and span Id into valid Request-Id: |trace.span.
            </summary>
            <param name="traceId">Trace Id.</param>
            <param name="spanId">Span id.</param>
            <returns>valid Request-Id.</returns>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.PropertyFetcher">
            <summary>
            Efficient implementation of fetching properties of anonymous types with reflection.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.PropertyFetcher.PropertyFetch.FetcherForProperty(System.Reflection.PropertyInfo)">
            <summary>
            Create a property fetcher from a .NET Reflection PropertyInfo class that
            represents a property of a particular type.  
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.PropertyFetcher.PropertyFetch.Fetch(System.Object)">
            <summary>
            Given an object, fetch the property that this propertyFetch represents. 
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.RequestResponseHeaders">
            <summary>
            Header names for requests / responses.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.RequestResponseHeaders.RequestContextHeader">
            <summary>
            Request-Context header.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.RequestResponseHeaders.RequestContextCorrelationSourceKey">
            <summary>
            Source key in the request context header that is added by an application while making http requests and retrieved by the other application when processing incoming requests.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.RequestResponseHeaders.RequestContextCorrelationTargetKey">
            <summary>
            Target key in the request context header that is added to the response and retrieved by the calling application when processing incoming responses.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.RequestResponseHeaders.StandardParentIdHeader">
            <summary>
            Legacy parent Id header.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.RequestResponseHeaders.StandardRootIdHeader">
            <summary>
            Legacy root id header.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.RequestResponseHeaders.RequestIdHeader">
            <summary>
            Standard Request-Id Id header.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.RequestResponseHeaders.CorrelationContextHeader">
            <summary>
            Standard Correlation-Context header.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Common.RequestResponseHeaders.AccessControlExposeHeadersHeader">
            <summary>
            Access-Control-Expose-Headers header indicates which headers can be exposed as part of the response by listing their names.
            Should contain Request-Context value that will allow reading Request-Context in JavaScript SDK on Browser side.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Common.WebHeaderCollectionExtensions">
            <summary>
            WebHeaderCollection extension methods.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.WebHeaderCollectionExtensions.GetNameValueHeaderValue(System.Collections.Specialized.NameValueCollection,System.String,System.String)">
            <summary>
            For the given header collection, for a given header of name-value type, find the value of a particular key.
            </summary>
            <param name="headers">Header collection.</param>
            <param name="headerName">Name of the header in the collection.</param>
            <param name="keyName">Desired key of the key-value list.</param>
            <returns>Value against the given parameters.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.WebHeaderCollectionExtensions.GetNameValueCollectionFromHeader(System.Collections.Specialized.NameValueCollection,System.String)">
            <summary>
            For the given header collection, for a given header of name-value type, return list of KeyValuePairs.
            </summary>
            <param name="headers">Header collection.</param>
            <param name="headerName">Name of the header in the collection.</param>
            <returns>List of KeyValuePairs in the given header.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.WebHeaderCollectionExtensions.SetNameValueHeaderValue(System.Collections.Specialized.NameValueCollection,System.String,System.String,System.String)">
            <summary>
            For the given header collection, adds KeyValuePair to header.
            </summary>
            <param name="headers">Header collection.</param>
            <param name="headerName">Name of the header that is to contain the name-value pair.</param>
            <param name="keyName">Name in the name value pair.</param>
            <param name="value">Value in the name value pair.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.WebHeaderCollectionExtensions.SetHeaderFromNameValueCollection(System.Collections.Specialized.NameValueCollection,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            For the given header collection, sets the header value based on the name value format.
            </summary>
            <param name="headers">Header collection.</param>
            <param name="headerName">Name of the header that is to contain the name-value pair.</param>
            <param name="keyValuePairs">List of KeyValuePairs to format into header.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Common.WebHeaderCollectionExtensions.GetHeaderValue(System.Collections.Specialized.NameValueCollection,System.String,System.Int32,System.Int32)">
            <summary>
            For the given header collection, for a given header name, returns collection of header values.
            </summary>
            <param name="headers">Header collection.</param>
            <param name="headerName">Name of the header in the collection.</param>
            <param name="maxStringLength">Maximum allowed header length</param>
            <param name="maxItems">Maximum allowed number comma separated values in the header</param>
            <returns>List of comma separated values in the given header.</returns>
        </member>
        <member name="T:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants">
            <summary>
            W3C constants.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.TraceParentHeader">
            <summary>
            W3C traceparent header name.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.TraceStateHeader">
            <summary>
            W3C tracestate header name.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.ApplicationIdTraceStateField">
            <summary>
            Name of the field that carry ApplicationInsights application Id in the tracestate header under az key.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.AzureTracestateNamespace">
            <summary>
            Name of the field that carry Azure-specific states in the tracestate header.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.TracestateAzureSeparator">
            <summary>
            Separator between Azure namespace values.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.TraceIdTag">
            <summary>
            Trace-Id tag name.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.SpanIdTag">
            <summary>
            Span-Id tag name.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.ParentSpanIdTag">
            <summary>
            Parent span-Id tag name.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.VersionTag">
            <summary>
            Version tag name.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.SampledTag">
            <summary>
            Sampled tag name.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.TracestateTag">
            <summary>
            Tracestate tag name.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.DefaultVersion">
            <summary>
            Default version value.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.TraceFlagRecordedAndNotRequested">
            <summary>
            Default sampled flag value: may be recorded, not requested
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.TraceFlagRecordedAndRequested">
            <summary>
            Recorded and requested sampled flag value
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.RequestedTraceFlag">
            <summary>
            Requested trace flag
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.LegacyRootIdProperty">
            <summary>
            Legacy root Id tag name.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.W3C.Internal.W3CConstants.LegacyRequestIdProperty">
            <summary>
            Legacy root Id tag name.
            </summary>
        </member>
        <member name="T:System.Web.HttpContextBaseExtension">
            <summary>
            HttpContextBaseExtension class provides extensions methods for accessing Web Application Insights objects.
            </summary>
        </member>
        <member name="M:System.Web.HttpContextBaseExtension.GetRequestTelemetry(System.Web.HttpContextBase)">
            <summary>
            Provide access to request generated by Web Application Insights SDK.
            </summary>
            <param name="context">HttpContextBase instance.</param>
            <returns>Request telemetry instance or null.</returns>
        </member>
        <member name="T:System.Web.HttpContextExtension">
            <summary>
            HttpContextExtension class provides extensions methods for accessing Web Application Insights objects.
            </summary>
        </member>
        <member name="M:System.Web.HttpContextExtension.GetRequestTelemetry(System.Web.HttpContext)">
            <summary>
            Provide access to request generated by Web Application Insights SDK.
            </summary>
            <param name="context">HttpContext instance.</param>
            <returns>Request telemetry instance or null.</returns>
        </member>
    </members>
</doc>
