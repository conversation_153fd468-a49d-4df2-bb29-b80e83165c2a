<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.IT.Commerce.Diagnostics.Listeners.ETW</name>
    </assembly>
    <members>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener">
            <summary>
            A class used to route log records to the ETW (Event Tracing for Windows) pipeline.
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener._applicationName">
            <summary>
            The _application name to be used for all events being passed to ETW
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener._environmentName">
            <summary>
            The _environment name to be used for all events being passed to ETW
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener.MaxLengthMessage">
            <summary>
            The maximum message length to send to ETW.
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener.MaxXmlLength">
            <summary>
            The maximum XML message length.
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener.MaxKeyLength">
            <summary>
            The maximum Key length in XML.
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener.MaxLengthThreatAuditMessage">
            <summary>
            The maximum message length to send threat audit messages to ETW.
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener.MaxLengthMilestoneMessage">
            <summary>
            The maximum message length to send milestone messages to ETW.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener" /> class.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener.#ctor(Microsoft.IT.Commerce.Diagnostics.Listeners.Common.Settings.DiagnosticsEtwSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener" /> class.
            </summary>
            <param name="configEntity">The configuration entity.</param>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener.IsThreadSafe">
            <summary>
            Gets a value indicating whether the trace listener is thread safe.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener.Write(System.String)">
            <summary>
            Writes the specified message to the listener.
            </summary>
            <param name="message">A message to write.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener.WriteLine(System.String)">
            <summary>
            Writes a message to the listener followed by a line terminator.
            </summary>
            <param name="message">A message to write.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener.TraceData(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.Object)">
            <summary>
            Writes trace information, a data object and event information to the listener specific output.
            </summary>
            <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
            <param name="source">A name used to identify the output, typically the name of the application that generated the trace event.</param>
            <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values specifying the type of event that has caused the trace.</param>
            <param name="id">A numeric identifier for the event.</param>
            <param name="data">The trace data to emit.</param>
            <PermissionSet>
              <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
              <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
            </PermissionSet>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.EtwTraceListener.SendLogEntryToEtw(Microsoft.Practices.EnterpriseLibrary.Logging.LogEntry)">
            <summary>
            Sends the log entry to ETW
            </summary>
            <param name="logEntry">The log.</param>
        </member>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.Repository.TelemetryEventSource">
            <summary>
            A class defining the methods used to deliver Diagnostics telemetry call data to ETW as ETW events
            </summary>
        </member>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.Repository.TelemetryEventSource.Keywords">
            <summary>
            A class used to define the values that will decorate specific ETW events to allow consumers to use them to drive search results filtering
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.Repository.TelemetryEventSource.Keywords.Event">
            <summary>
            Indicates that the etw event is generic in nature and does not fit any other subclassification
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.Repository.TelemetryEventSource.Keywords.Message">
            <summary>
            Indicates that the etw event contains an application generated message
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.Repository.TelemetryEventSource.Keywords.Exception">
            <summary>
            Indicates that the etw event contains exception details for an exception that was detected by the application
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.Repository.TelemetryEventSource.Keywords.Milestone">
            <summary>
            Indicates that the etw event contains business process details for workflow milestone that was created by the application
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.Repository.TelemetryEventSource.Keywords.Timing">
            <summary>
            Indicates that the etw event contains execution timings for a specific method that was instrumented by the application
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.Repository.TelemetryEventSource.Keywords.ThreatAudit">
            <summary>
            Indicates that the etw event contains a threat audit message generated by ThreatMetrix
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.Repository.TelemetryEventSource.Log">
            <summary>
            The Log property exposes a static object that provides access the logging methods that push application data into the ETW pipeline
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.Repository.TelemetryEventSource.WriteMilestone(System.String,System.String,System.String,System.Guid,System.String,System.DateTime,System.Guid,System.Guid,System.String,System.Int32,System.Guid,System.String,System.String,System.Guid,System.Guid,System.String)">
            <summary>
            Writes a milestone log entry to the ETW pipeline
            </summary>
            <param name="applicationName">Name of the application.</param>
            <param name="environmentName">Name of the environment.</param>
            <param name="alternateReferenceId">The alternate reference identifier.</param>
            <param name="milestoneApplicationId">The milestone application identifier.</param>
            <param name="milestoneApplicationName">Name of the milestone application.</param>
            <param name="dateTimeStamp">The date time stamp of when the milestone occurred.</param>
            <param name="milestoneId">The milestone identifier.</param>
            <param name="milestoneInstanceId">The milestone instance identifier.</param>
            <param name="milestoneName">Name of the milestone.</param>
            <param name="milestoneState">State of the milestone. (Internal use only. Default to 0)</param>
            <param name="parentWorkflowInstanceId">The parent workflow instance identifier.</param>
            <param name="payloadXml">The payload XML.</param>
            <param name="referenceId">The reference identifier.</param>
            <param name="workflowId">The workflow identifier.</param>
            <param name="workflowInstanceId">The workflow instance identifier.</param>
            <param name="workflowName">Name of the workflow.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.Repository.TelemetryEventSource.WriteTiming(System.String,System.String,System.String,System.String,System.DateTime,System.String,System.Int32,System.String)">
            <summary>
            Writes the timing log to the ETW pipeline
            </summary>
            <param name="applicationName">Name of the application.</param>
            <param name="environmentName">Name of the environment.</param>
            <param name="activityId">The correlation identifier.</param>
            <param name="transactionName">Name of the transaction.</param>
            <param name="timeStamp">The time stamp of when the timing was collected</param>
            <param name="machineName">Name of the machine.</param>
            <param name="elapsedMilliseconds">The elapsed milliseconds.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.Repository.TelemetryEventSource.WriteEvent(System.String,System.String,System.String,System.Int32,System.String,System.String,System.DateTime,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Writes an event to the ETW pipeline
            </summary>
            <param name="applicationName">Name of the application.</param>
            <param name="environmentName">Name of the environment.</param>
            <param name="activityId">The correlation identifier for the event</param>
            <param name="eventId">The event identifier. (must be between 1 and 65000)</param>
            <param name="severity">The severity of the event</param>
            <param name="title">The title of the event</param>
            <param name="timestamp">The timestamp.</param>
            <param name="machineName">Name of the machine the event occurred on</param>
            <param name="method">The method that was executing when the event occurred</param>
            <param name="message">The message describing the event</param>
            <param name="className">The class name containing additional information related to the event</param>
            <param name="logCreatorName">The application user creating the logging event</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.Repository.TelemetryEventSource.WriteEventWithAttributes(System.String,System.String,System.String,System.Int32,System.String,System.String,System.DateTime,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Writes the event to the ETW pipeline
            </summary>
            <param name="applicationName">Name of the application.</param>
            <param name="environmentName">Name of the environment.</param>
            <param name="activityId">The correlation identifier for the event</param>
            <param name="eventId">The event identifier. (must be between 1 and 65000)</param>
            <param name="severity">The severity of the event</param>
            <param name="title">The title of the event</param>
            <param name="timestamp">The timestamp.</param>
            <param name="machineName">Name of the machine the event occurred on</param>
            <param name="method">The method that was executing when the event occurred</param>
            <param name="message">The message describing the event</param>
            <param name="className">The className where the event occured</param>
            <param name="logAttributes">The serialized xml object contains attributes of the logging event</param>
            <param name="logCreatorName">The application user creating the logging event</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.ETW.Repository.TelemetryEventSource.WriteThreatAuditMessage(System.String,System.String,System.Guid,System.Guid,System.Guid,System.String,System.String,System.DateTime)">
            <summary>
            Writes a threat audit message to the ETW pipeline.
            </summary>
            <param name="applicationName">Name of the application.</param>
            <param name="environmentName">Name of the environment.</param>
            <param name="auditApplicationId">The audit application identifier.</param>
            <param name="auditPrimaryIdentifier">The audit primary identifier.</param>
            <param name="auditSecondaryIdentifier">The audit secondary identifier.</param>
            <param name="auditSessionId">The audit session identifier.</param>
            <param name="auditMessage">The audit message.</param>
            <param name="auditDateCreated">The date the audit was executed.</param>
        </member>
    </members>
</doc>
