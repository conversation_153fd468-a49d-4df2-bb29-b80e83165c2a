﻿# Variables:
$RootDir = split-path $MyInvocation.MyCommand.Path
$local:ErrorActionPreference = "SilentlyContinue"
$SystemRoot = ($env:SystemRoot).Substring(0,1)
$ProdEnvironmentString = "Prod"
$fileNameToRemove = "robots.txt"

# Functions:


# Main:

Write-Host "Start of $fileNameToRemove Processing Script:"

Write-Host "Current Environment is:  $env:EnvironmentName"

#Check if current environment is a production environment:
if ($env:EnvironmentName.Contains($ProdEnvironmentString))
{
    Write-Host "Environment is found to be production... continuing to search for and remove any '$fileNameToRemove' files..."

    # Search through all drives except for OS drive and locate "Robots.txt" files:
    $fileList = Get-PSDrive -PSProvider FileSystem | Where-Object { $_.Name -NotMatch $SystemRoot } | ForEach-Object { Get-ChildItem -Path $_.Root -Filter $fileNameToRemove -Recurse -File }

    #List any findings:
    $fileList.FullName

    #Remove any Robots.txt files:
    $fileList | Remove-Item
}

Write-Host "End of $fileNameToRemove Processing Script:"
