@ECHO OFF
@REM A file to flag that this script has already run
@REM because if we run it twice, it errors out and prevents the Azure role from starting properly
@REM %~n0 expands to the name of the currently executing file, without the extension
SET FLAGFILE=%windir%\Temp\%~n0-Flag.txt

IF EXIST "%FLAGFILE%" (
  ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1
  ECHO %FLAGFILE% exists, exiting startup script @ [%DATE%  - %TIME%] >> "%windir%\Temp\StartupLog.txt" 2>&1
  DATE /T >> "%windir%\Temp\StartupLog.txt" 2>&1
  TIME /T >> "%windir%\Temp\StartupLog.txt" 2>&1
  ECHO EXIT... >> "%windir%\Temp\StartupLog.txt" 2>&1
  ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1
  exit /B
) ELSE (
  DATE /T >> %FLAGFILE%
  TIME /T >> %FLAGFILE%
)


ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO Starting @ >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1
DATE /T >> "%windir%\Temp\StartupLog.txt" 2>&1
TIME /T >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO Current Environment = [%EnvironmentName%] >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1


ECHO Calling: powershell.exe -command "Set-ExecutionPolicy Unrestricted" >> "%windir%\Temp\StartupLog.txt" 2>&1
powershell.exe -command "Set-ExecutionPolicy Unrestricted" >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1


ECHO Calling: powershell.exe .\Startup\CreateWindowsEventSource.ps1 >> "%windir%\Temp\StartupLog.txt" 2>&1
powershell.exe .\Startup\CreateWindowsEventSource.ps1 >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1


REM   Set IIS to automatically start AppPools
ECHO Calling:  appcmd.exe set config -section:applicationPools -applicationPoolDefaults.startMode:AlwaysRunning /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1
%windir%\system32\inetsrv\appcmd.exe set config -section:applicationPools -applicationPoolDefaults.startMode:AlwaysRunning /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1


REM   IIS:  Set Application Pool Timeout to 00:00:00
REM   This should keep application pool from stopping after 20 minutes of idle time.
ECHO Calling:  appcmd.exe set config -section:applicationPools -applicationPoolDefaults.processModel.idleTimeout:00:00:00 >> "%windir%\Temp\StartupLog.txt" 2>&1
%windir%\system32\inetsrv\appcmd.exe set config -section:applicationPools -applicationPoolDefaults.processModel.idleTimeout:00:00:00 >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1


REM   But don't automatically start the AppPools that we don't use, and do shut them down when idle
ECHO Calling:  appcmd.exe set config  -section:system.applicationHost/applicationPools "/[name='Classic .NET AppPool'].startMode:OnDemand" "/[name='Classic .NET AppPool'].autoStart:False" "/[name='Classic .NET AppPool'].processModel.idleTimeout:00:01:00" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1
%windir%\system32\inetsrv\appcmd.exe set config  -section:system.applicationHost/applicationPools "/[name='Classic .NET AppPool'].startMode:OnDemand" "/[name='Classic .NET AppPool'].autoStart:False" "/[name='Classic .NET AppPool'].processModel.idleTimeout:00:01:00" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1

ECHO Calling:  appcmd.exe set config  -section:system.applicationHost/applicationPools "/[name='ASP.NET v4.0'].startMode:OnDemand" "/[name='ASP.NET v4.0'].autoStart:False" "/[name='ASP.NET v4.0'].processModel.idleTimeout:00:01:00" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1
%windir%\system32\inetsrv\appcmd.exe set config  -section:system.applicationHost/applicationPools "/[name='ASP.NET v4.0'].startMode:OnDemand" "/[name='ASP.NET v4.0'].autoStart:False" "/[name='ASP.NET v4.0'].processModel.idleTimeout:00:01:00" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1

ECHO Calling:  appcmd.exe set config  -section:system.applicationHost/applicationPools "/[name='ASP.NET v4.0 Classic'].startMode:OnDemand" "/[name='ASP.NET v4.0 Classic'].autoStart:False" "/[name='ASP.NET v4.0 Classic'].processModel.idleTimeout:00:01:00" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1
%windir%\system32\inetsrv\appcmd.exe set config  -section:system.applicationHost/applicationPools "/[name='ASP.NET v4.0 Classic'].startMode:OnDemand" "/[name='ASP.NET v4.0 Classic'].autoStart:False" "/[name='ASP.NET v4.0 Classic'].processModel.idleTimeout:00:01:00" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1


REM   IIS: Add content (MIME) types to be enabled for dynamic compression
ECHO Calling:  appcmd.exe set config -section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/json',enabled='True']" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1
%windir%\system32\inetsrv\appcmd.exe set config -section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/json',enabled='True']" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1

ECHO Calling:  appcmd.exe set config -section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/json; charset=utf-8',enabled='True']" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1
%windir%\system32\inetsrv\appcmd.exe set config -section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/json; charset=utf-8',enabled='True']" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1

ECHO Calling:  appcmd.exe set config -section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/json; odata=fullmetadata; charset=utf-8',enabled='True']" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1
%windir%\system32\inetsrv\appcmd.exe set config -section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/json; odata=fullmetadata; charset=utf-8',enabled='True']" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1

ECHO Calling:  appcmd.exe set config -section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/atom%u002bxml',enabled='True']" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1
%windir%\system32\inetsrv\appcmd.exe set config -section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/atom%u002bxml',enabled='True']" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1

ECHO Calling:  appcmd.exe set config -section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/atom%u002bxml; charset=utf-8',enabled='True']" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1
%windir%\system32\inetsrv\appcmd.exe set config -section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/atom%u002bxml; charset=utf-8',enabled='True']" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1

ECHO Calling:  appcmd.exe set config -section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/xml',enabled='True']" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1
%windir%\system32\inetsrv\appcmd.exe set config -section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/xml',enabled='True']" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1

ECHO Calling:  appcmd.exe set config -section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/xml; charset=utf-8',enabled='True']" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1
%windir%\system32\inetsrv\appcmd.exe set config -section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/xml; charset=utf-8',enabled='True']" /commit:apphost >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1


REM   Remove IIS response headers
ECHO Calling:  appcmd.exe set config /section:httpProtocol /-customHeaders.[name='X-Powered-By'] >> "%windir%\Temp\StartupLog.txt" 2>&1
%windir%\system32\inetsrv\appcmd.exe set config /section:httpProtocol /-customHeaders.[name='X-Powered-By'] >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1


REM  Cleanup Robots.txt file if Environment == Production
ECHO Calling: powershell.exe .\Startup\Cleanup-RobotsText.ps1 >> "%windir%\Temp\StartupLog.txt" 2>&1
powershell.exe .\Startup\Cleanup-RobotsText.ps1 >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1

REM  Disable SSLv3 - Shellshock vuln
ECHO Calling: powershell.exe .\Startup\DisableSSLv3.ps1 >> "%windir%\Temp\StartupLog.txt" 2>&1
powershell.exe .\Startup\DisableSSLv3.ps1 >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1

REM   *** Exit batch file. ***
REM Log date and time to log file:
ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO Log date and time to log file: >> "%windir%\Temp\StartupLog.txt" 2>&1
DATE /T >> "%windir%\Temp\StartupLog.txt" 2>&1
TIME /T >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO --------------------------------------------------------- >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO. >> "%windir%\Temp\StartupLog.txt" 2>&1
EXIT /b 0

REM   *** Log error and exit ***
:ErrorExit
REM   Report the date, time, and ERRORLEVEL of the error.
DATE /T >> "%windir%\Temp\StartupLog.txt" 2>&1
TIME /T >> "%windir%\Temp\StartupLog.txt" 2>&1
ECHO An error occurred during startup. ERRORLEVEL = %ERRORLEVEL% >> "%windir%\Temp\StartupLog.txt" 2>&1
EXIT %ERRORLEVEL%
