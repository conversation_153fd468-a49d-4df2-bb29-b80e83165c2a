﻿[Reflection.Assembly]::LoadWithPartialName("Microsoft.WindowsAzure.ServiceRuntime")
$eventLogName = [Microsoft.WindowsAzure.ServiceRuntime.RoleEnvironment]::GetConfigurationSettingValue("StratumLogging.EventLogName")
$eventSourceName = [Microsoft.WindowsAzure.ServiceRuntime.RoleEnvironment]::GetConfigurationSettingValue("StratumLogging.EventSourceName")
if (![System.Diagnostics.EventLog]::SourceExists($eventSourceName)) { 
    Write-Host "Source '$eventSourceName' does not exists.  Creating..."
    [System.Diagnostics.EventLog]::CreateEventSource($eventSourceName, $eventLogName)
    [System.Diagnostics.EventLog]::WriteEntry($eventSourceName, "Created event log source.")
    Write-Host "Source '$eventSourceName' created."
}
else {
    Write-Host "Source '$eventSourceName' already exists."
}
