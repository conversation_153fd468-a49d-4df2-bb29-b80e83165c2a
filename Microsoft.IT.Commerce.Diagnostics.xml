<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.IT.Commerce.Diagnostics</name>
    </assembly>
    <members>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Configuration.DiagnosticsCategorySettings.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.Configuration.DiagnosticsCategorySettings"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.Configuration.DiagnosticsCategorySettings.CategoryName">
            <summary>
            Gets or sets the name of the category.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.Configuration.DiagnosticsCategorySettings.FilterLevel">
            <summary>
            Gets or sets the switch value which filters
            log events based on their severity.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.Configuration.DiagnosticsCategorySettings.ListenerSettings">
            <summary>
            Gets or sets the settings for each diagnostics listener.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Configuration.DiagnosticsSettings.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.Configuration.DiagnosticsSettings"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.Configuration.DiagnosticsSettings.Categories">
            <summary>
            Gets or sets the logging trace categories.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.Configuration.DiagnosticsSettings.ApplicationName">
            <summary>
            Gets or sets the name of the application.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.Configuration.DiagnosticsSettings.EnvironmentName">
            <summary>
            Gets or sets the name of the environment.
            </summary>
        </member>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.Correlation.CallContextCorrelationIdProvider">
            <summary>
            Gets or sets the correlation id from the current <see cref="T:System.Runtime.Remoting.Messaging.CallContext"/>.
            NOTE: There are specific scenarios where the <see cref="T:System.Runtime.Remoting.Messaging.CallContext"/> does not flow the value from one context to another.
            (Such as from an Owin middleware to a Web API message handler)
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Correlation.CallContextCorrelationIdProvider.GetCorrelationId">
            <summary>
            Gets the correlation id for the current activity or <c>null</c> if none defined.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Correlation.CallContextCorrelationIdProvider.SetCorrelationId(System.Guid)">
            <summary>
            Sets the correlation id for the current activity.
            </summary>
            <param name="correlationId">The correlation id.</param>
        </member>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.Correlation.HttpContextCorrelationIdProvider">
            <summary>
            Gets or sets the correlation id from the current <see cref="T:System.Web.HttpContext"/>.
            NOTE: This provider only functions in a web context, but is silently ignored in a non-web scenario.
            NOTE: This provider fails if the synchronization context is lost 
            (such as after an await continuation on a task where ConfigureAwait is set to false).
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Correlation.HttpContextCorrelationIdProvider.GetCorrelationId">
            <summary>
            Gets the correlation id for the current activity or <c>null</c> if none defined.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Correlation.HttpContextCorrelationIdProvider.SetCorrelationId(System.Guid)">
            <summary>
            Sets the correlation id for the current activity.
            </summary>
            <param name="correlationId">The correlation id.</param>
        </member>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.Correlation.HttpRequestHeaderCorrelationIdProvider">
            <summary>
            Gets the correlation id from the headers of the <see cref="T:System.Web.HttpRequest"/> and
            sets the correlation id in the headers of the <see cref="T:System.Web.HttpResponse"/>.
            NOTE: This provider only functions in a web context, but is silently ignored in a non-web scenario.
            NOTE: This provider fails if the synchronization context is lost 
            (such as after an await continuation on a task where ConfigureAwait is set to false).
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Correlation.HttpRequestHeaderCorrelationIdProvider.GetCorrelationId">
            <summary>
            Gets the correlation id for the current activity or <c>null</c> if none defined.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Correlation.HttpRequestHeaderCorrelationIdProvider.SetCorrelationId(System.Guid)">
            <summary>
            Sets the correlation identifier for the current activity.
            </summary>
            <param name="correlationId">The correlation id.</param>
        </member>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.Correlation.TraceActivityCorrelationIdProvider">
            <summary>
            Gets or sets the correlation id from the current activity id of <see cref="P:System.Diagnostics.Trace.CorrelationManager"/>.
            NOTE: There are specific scenarios where the <see cref="P:System.Diagnostics.Trace.CorrelationManager"/> resets back to a default value.
            (Such as in when attempting to override that activity id in Web API and then after an awaited task continuation)
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Correlation.TraceActivityCorrelationIdProvider.GetCorrelationId">
            <summary>
            Gets the correlation id for the current activity or <c>null</c> if none defined.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Correlation.TraceActivityCorrelationIdProvider.SetCorrelationId(System.Guid)">
            <summary>
            Sets the correlation id for the current activity.
            </summary>
            <param name="correlationId">The correlation id.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConfiguration.Configure(Microsoft.IT.Commerce.Diagnostics.Configuration.DiagnosticsSettings)">
            <summary>
            Configures the diagnostics logging for the application.
            </summary>
            <param name="diagnosticsSettings">The diagnostics settings or <c>null</c> to read from config.</param>
            <remarks>
            If there exists custom configuration key-value pairs (see CreateFromCustomConfiguration)
            or a standard EntLib 'logginConfiguration' section in the app.config / web.config, 
            those settings take precedence over the specified <paramref name="diagnosticsSettings"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConfiguration.CreateFromDynamicSettings(Microsoft.IT.Commerce.Diagnostics.Configuration.DiagnosticsSettings)">
            <summary>
            Creates the log writer from the specified <paramref name="diagnosticsSettings"/>.
            </summary>
            <param name="diagnosticsSettings">The diagnostics settings.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConfiguration.CreateFromEntLibConfiguration">
            <summary>
            Creates the log writer from the standard Enterprise Library 'loggingConfiguration' section in configuration.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConfiguration.CreateFromCustomConfiguration">
            <summary>
            Creates the log writer from a custom defined set of configuration keys.
            </summary>
            <example>
            Diagnostics.CategoryNames = Event|Timing
            Diagnostics.Category.Event.FilterLevel = Warning
            Diagnostics.Category.Event.Listeners = AppInsights|EventLog
            Diagnostics.Category.Timing.FilterLevel = All
            Diagnostics.Category.Timing.Listeners = SQL|EventLog
            Diagnostics.Category.Timing.Listener.SQL.FilterLevel = All
            </example>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsLogger.#ctor(Microsoft.IT.Commerce.Diagnostics.Configuration.DiagnosticsSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.DiagnosticsLogger" /> class.
            </summary>
            <param name="diagnosticsSettings">The diagnostics settings.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsLogger`1.#ctor(Microsoft.IT.Commerce.Diagnostics.Configuration.DiagnosticsSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.DiagnosticsLogger`1" /> class.
            </summary>
            <param name="diagnosticsSettings">The diagnostics settings.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsLogger`1.Write(Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase,System.String)">
            <summary>
            Writes a timing event to the configured log listeners.
            </summary>
            <param name="diagnosticsEntity">The diagnostics entity.</param>
            <param name="methodName">Name of the calling method.  Note: Do NOT pass this parameter as the compiler will fill it in for you.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Utilities.GuidHelper.SafeCastObjectToNonEmptyGuid(System.Object)">
            <summary>
            Safe casts the object as a guid and returns the guid value.
            NOTE: Returns <c>null</c> when the object is <c>null</c> or is not a guid.
            NOTE: Returns <c>null</c> when the guid is equal to guid.empty (all zeros).
            </summary>
            <param name="guidObject">The guid as an object.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.IDiagnosticsConfiguration.Configure(Microsoft.IT.Commerce.Diagnostics.Configuration.DiagnosticsSettings)">
            <summary>
            Configures the diagnostics logging for the application.
            </summary>
            <param name="diagnosticsSettings">The diagnostics settings or <c>null</c> to read from config.</param>
            <remarks>
            If there exists custom configuration key-value pairs (see CreateFromCustomConfiguration)
            or a standard EntLib 'logginConfiguration' section in the app.config / web.config, 
            those settings take precedence over the specified <paramref name="diagnosticsSettings"/>.
            </remarks>
        </member>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.NullDiagnosticsLogger`1">
            <summary>
            Takes your logging requests and throws them away!
            </summary>
            <typeparam name="T">Type Logging</typeparam>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.NullDiagnosticsLogger`1.Write(Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase,System.String)">
            <summary>
            Ignores the Write
            </summary>
        </member>
    </members>
</doc>
