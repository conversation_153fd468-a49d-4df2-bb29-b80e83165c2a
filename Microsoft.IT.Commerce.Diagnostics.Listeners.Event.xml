<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.IT.Commerce.Diagnostics.Listeners.Event</name>
    </assembly>
    <members>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.Listeners.Event.EventLogTraceListener">
            <summary>
            A class providing the ability to send log records to the console.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.Event.EventLogTraceListener.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.Listeners.Event.EventLogTraceListener" /> class.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.Event.EventLogTraceListener.#ctor(Microsoft.IT.Commerce.Diagnostics.Listeners.Common.Settings.DiagnosticsEventLogSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.Listeners.Event.EventLogTraceListener" /> class.
            </summary>
            <param name="configEntity">The configuration entity.</param>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.Listeners.Event.EventLogTraceListener.IsThreadSafe">
            <summary>
            Gets a value indicating whether the trace listener is thread safe.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.Event.EventLogTraceListener.TraceData(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.Object)">
            <summary>
            Writes trace information, a data object and event information to the listener specific output.
            </summary>
            <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
            <param name="source">A name used to identify the output, typically the name of the application that generated the trace event.</param>
            <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values specifying the type of event that has caused the trace.</param>
            <param name="id">A numeric identifier for the event.</param>
            <param name="data">The trace data to emit.</param>
            <PermissionSet>
              <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
              <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
            </PermissionSet>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.Event.EventLogTraceListener.Write(System.String)">
            <summary>
            When overridden in a derived class, writes the specified message to the listener you create in the derived class.
            </summary>
            <param name="message">A message to write.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.Event.EventLogTraceListener.WriteLine(System.String)">
            <summary>
            When overridden in a derived class, writes a message to the listener you create in the derived class, followed by a line terminator.
            </summary>
            <param name="message">A message to write.</param>
        </member>
    </members>
</doc>
