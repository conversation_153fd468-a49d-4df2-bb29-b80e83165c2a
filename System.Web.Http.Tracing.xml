﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.Http.Tracing</name>
  </assembly>
  <members>
    <member name="T:System.Web.Http.HttpConfigurationTracingExtensions">
      <summary>This static class contains helper methods related to the registration of <see cref="T:System.Web.Http.Tracing.ITraceWriter" /> instances.</summary>
    </member>
    <member name="M:System.Web.Http.HttpConfigurationTracingExtensions.EnableSystemDiagnosticsTracing(System.Web.Http.HttpConfiguration)">
      <summary>Creates and registers an <see cref="T:System.Web.Http.Tracing.ITraceWriter" /> implementation to use for this application.</summary>
      <returns>The <see cref="T:System.Web.Http.Tracing.SystemDiagnosticsTraceWriter" /> which was created and registered.</returns>
      <param name="configuration">The <see cref="T:System.Web.Http.HttpConfiguration" /> for which to register the created trace writer.</param>
    </member>
    <member name="T:System.Web.Http.Tracing.SystemDiagnosticsTraceWriter">
      <summary>Implementation of <see cref="T:System.Web.Http.Tracing.ITraceWriter" /> that traces to <see cref="T:System.Diagnostics.Trace" /></summary>
    </member>
    <member name="M:System.Web.Http.Tracing.SystemDiagnosticsTraceWriter.#ctor"></member>
    <member name="M:System.Web.Http.Tracing.SystemDiagnosticsTraceWriter.Format(System.Web.Http.Tracing.TraceRecord)">
      <summary>Formats the contents of the given <see cref="T:System.Web.Http.Tracing.TraceRecord" /> into a single string containing comma-separated name-value pairs for each <see cref="T:System.Web.Http.Tracing.TraceRecord" /> property.</summary>
      <returns>A string containing comma-separated name-value pairs.</returns>
      <param name="traceRecord">The <see cref="T:System.Web.Http.Tracing.TraceRecord" /> from which to produce the result.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.SystemDiagnosticsTraceWriter.FormatDateTime(System.DateTime)">
      <summary>Formats a <see cref="T:System.DateTime" /> for the trace.</summary>
      <returns>The <see cref="T:System.DateTime" /> formatted as a string</returns>
      <param name="dateTime">The <see cref="T:System.DateTime" /></param>
    </member>
    <member name="M:System.Web.Http.Tracing.SystemDiagnosticsTraceWriter.FormatRequestEnvelope(System.Web.Http.Tracing.TraceRecord)">
      <summary>Formats the given <see cref="T:System.Web.Http.Tracing.TraceRecord" /> into a string describing either the initial receipt of the incoming request or the final send of the response, depending on <see cref="T:System.Web.Http.Tracing.TraceKind" />.</summary>
      <returns>A string containing comma-separated name-value pairs.</returns>
      <param name="traceRecord">The <see cref="T:System.Web.Http.Tracing.TraceRecord" /> from which to produce the result.</param>
    </member>
    <member name="P:System.Web.Http.Tracing.SystemDiagnosticsTraceWriter.IsVerbose">
      <summary>Gets or sets a value indicating whether the formatted message should be the verbose format, meaning it displays all fields of the <see cref="T:System.Web.Http.Tracing.TraceRecord" />.</summary>
      <returns>true means all <see cref="T:System.Web.Http.Tracing.TraceRecord" /> fields will be traced, false means only minimal information will be traced. The default value is false.</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.SystemDiagnosticsTraceWriter.MinimumLevel">
      <summary>Gets or sets the minimum trace level.</summary>
      <returns>Any <see cref="T:System.Web.Http.Tracing.TraceLevel" /> below this level will be ignored. The default for this property is <see cref="F:System.Web.Http.Tracing.TraceLevel.Info" />.</returns>
    </member>
    <member name="M:System.Web.Http.Tracing.SystemDiagnosticsTraceWriter.Trace(System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.Action{System.Web.Http.Tracing.TraceRecord})">
      <summary>Writes a trace to <see cref="T:System.Diagnostics.Trace" /> if the <paramref name="level" /> is greater than or equal <see cref="P:System.Web.Http.Tracing.SystemDiagnosticsTraceWriter.MinimumLevel" />.</summary>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> associated with this trace. It may be null but the resulting trace will contain no correlation ID.</param>
      <param name="category">The category for the trace. This can be any user-defined value. It is not interpreted by this implementation but is written to the trace.</param>
      <param name="level">The <see cref="T:System.Web.Http.Tracing.TraceLevel" /> of this trace. If it is less than <see cref="P:System.Web.Http.Tracing.SystemDiagnosticsTraceWriter.MinimumLevel" />, this trace request will be ignored.</param>
      <param name="traceAction">The user callback to invoke to fill in a <see cref="T:System.Web.Http.Tracing.TraceRecord" /> with additional information to add to the trace.</param>
    </member>
    <member name="P:System.Web.Http.Tracing.SystemDiagnosticsTraceWriter.TraceSource">
      <summary>Gets or sets the <see cref="P:System.Web.Http.Tracing.SystemDiagnosticsTraceWriter.TraceSource" /> to which the traces will be sent.</summary>
      <returns>This property allows a custom <see cref="P:System.Web.Http.Tracing.SystemDiagnosticsTraceWriter.TraceSource" /> to be used when writing the traces. This allows an application to configure and use its own <see cref="P:System.Web.Http.Tracing.SystemDiagnosticsTraceWriter.TraceSource" /> other than the default <see cref="T:System.Diagnostics.Trace" />. If the value is null, this trace writer will send traces to <see cref="T:System.Diagnostics.Trace" />.</returns>
    </member>
    <member name="M:System.Web.Http.Tracing.SystemDiagnosticsTraceWriter.TranslateHttpResponseException(System.Web.Http.Tracing.TraceRecord)">
      <summary>Examines the given <see cref="T:System.Web.Http.Tracing.TraceRecord" /> to determine whether it contains an <see cref="T:System.Web.Http.HttpResponseException" /> and if so, modifies the <see cref="T:System.Web.Http.Tracing.TraceRecord" /> to capture more detailed information.</summary>
      <param name="traceRecord">The <see cref="T:System.Web.Http.Tracing.TraceRecord" /> to examine and modify.</param>
    </member>
  </members>
</doc>