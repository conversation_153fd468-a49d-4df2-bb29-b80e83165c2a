<?xml version="1.0"?>
<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">

  <xs:complexType name="ParamType">
    <xs:attribute name="Name" type="xs:string" use="required"/>
    <xs:attribute name="Value" type="xs:string" use="required"/>
  </xs:complexType>

  <xs:complexType name="BodyType">
    <xs:sequence>
      <xs:element name="Param" type="ParamType" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:element name="body" type="BodyType"/>

</xs:schema>
