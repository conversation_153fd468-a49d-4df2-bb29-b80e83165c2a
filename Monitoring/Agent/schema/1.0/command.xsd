<?xml version="1.0"?>

<!-- This holds the schema for the XML that defines a command passed to monitoring agent -->

<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">


  <xs:complexType name="ParametersType">
    <xs:choice maxOccurs="unbounded">
      <xs:element name="Parameter" type="xs:string" minOccurs="1" maxOccurs="1" />
    </xs:choice>
  </xs:complexType>

  <xs:complexType name="CommandType">
    <xs:all>
      <xs:element name="Verb" type="xs:string" minOccurs="1" maxOccurs="1"/>    
      <xs:element name="Parameters" type="ParametersType" minOccurs="0" maxOccurs="1"/>
      <!-- The command will only be executed when ExecuteCondition is evaluated as TRUE or it's not present. -->      
      <xs:element name="ExecuteCondition" type="xs:string" minOccurs="0" maxOccurs="1"/>
    </xs:all>
    <xs:attribute name="version" type="xs:string" use="required"/>
  </xs:complexType>

  <xs:element name="Command" type="CommandType"/>

</xs:schema>

