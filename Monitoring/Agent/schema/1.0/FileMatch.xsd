<?xml version="1.0" encoding="utf-8"?>
<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">

  <xs:complexType name="MatchListingType">
    <xs:sequence>
      <xs:element name="FilePath" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="Match" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="FileMatchType">
    <xs:sequence>
      <xs:element name="FileMatch" type="MatchListingType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
    <xs:attribute name="version" type="xs:string" use="required"/>
  </xs:complexType>

  <xs:element name="FileMatches" type="FileMatchType" />

</xs:schema>
