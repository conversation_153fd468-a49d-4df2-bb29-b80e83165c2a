<?xml version="1.0" encoding="utf-8"?>
<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">

  <xs:simpleType name="DateTimeType">
    <xs:restriction base="xs:dateTime">
      <!-- enforce the time to be in UTC -->
      <xs:pattern value=".*Z" />
    </xs:restriction>
  </xs:simpleType>

  <xs:complexType name="ListingType">
    <xs:sequence>
      <xs:element name="FileName" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="LastModified" type="DateTimeType" minOccurs="0" maxOccurs="1" />
      <xs:element name="SizeInBytes" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="FileListingType">
    <xs:sequence>
      <xs:element name="DirectoryPath" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="Directory" type="ListingType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="File" type="ListingType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="DirectoryListingType">
    <xs:sequence>
      <xs:element name="DirectoryListing" type="FileListingType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
    <xs:attribute name="version" type="xs:string" use="required"/>
  </xs:complexType>

  <xs:element name="DirectoryListings" type="DirectoryListingType" />

</xs:schema>
