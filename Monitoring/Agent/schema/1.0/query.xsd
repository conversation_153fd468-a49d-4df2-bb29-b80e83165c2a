<?xml version="1.0"?>

<!-- This holds the schema for the XML that defines a query used in a scheduled task or sent to
     the monitoring agent via the WCF service -->

<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">

  <!-- TODO: add restriction so that the name cannot start with MA -->
  <xs:simpleType name="MonIDType">
    <xs:restriction base="xs:ID">
    </xs:restriction>
  </xs:simpleType>

  <!--
  XTable has the following restriction for column name. We might as well
  have the same restriction for all our tables
  Only alphanumeric characters and '_' are allowed.
  -->
  <xs:simpleType name="ValidColumnNameType">
    <xs:restriction base="xs:string">
      <xs:pattern value="[a-zA-Z0-9_]+" />
    </xs:restriction>
  </xs:simpleType>


  <xs:complexType name="SelectColumnType">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="alias" type="ValidColumnNameType" use="optional" />
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>

  <xs:complexType name="SelectColumnsType">
    <xs:sequence>
      <xs:element name="Column" type="SelectColumnType" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="DeclareBlockClauseType">
    <xs:sequence>
      <xs:element name="Declare" type="xs:string" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="FromClauseType">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="alias" type="xs:string" use="optional"/>
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>

  <xs:complexType name="SelectType">
    <xs:all>
      <xs:element name="SelectColumns" type="SelectColumnsType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="From" type="FromClauseType" minOccurs="0" maxOccurs="1" />
      <xs:element name="Where" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="Having" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="GroupBy" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="OrderBy" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="DeclareBlock" type="DeclareBlockClauseType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="StatementBlock" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="Join" type="xs:string" minOccurs="0" maxOccurs="1"/>
    </xs:all>
  </xs:complexType>

  <xs:complexType name="QueryType">
    <xs:sequence>
      <xs:element name="Select" type="SelectType" minOccurs="1" maxOccurs="1"/>
    </xs:sequence>
    <xs:attribute name="name" type="MonIDType" use="required"/>
  </xs:complexType>

  <!-- The TableQueryType is used for standalone queries sent to MA. -->
  <xs:complexType name="TableQueryType">
    <xs:all>
      <xs:element name="Query" type="QueryType"/>
    </xs:all>
    <xs:attribute name="version" type="xs:string" use="required"/>
  </xs:complexType>

  <xs:element name="TableQuery" type="TableQueryType"/>

</xs:schema>

