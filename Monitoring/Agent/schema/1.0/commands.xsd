<?xml version="1.0"?>

<!-- This holds the schema for the XML that defines a set of commands passed to the monitoring agent -->

<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">

  <xs:complexType name="ParametersType">
    <xs:choice maxOccurs="unbounded">
      <xs:element name="Parameter" type="xs:string" minOccurs="1" maxOccurs="1" />
    </xs:choice>
  </xs:complexType>

  <xs:complexType name="CommandType">
    <xs:sequence>
      <xs:element name="Verb" type="xs:string" minOccurs="1" maxOccurs="1"/>
      <xs:element name="Parameters" type="ParametersType" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>

  <xs:element name="Commands">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="Command" type="CommandType" minOccurs="1" maxOccurs="unbounded" />
      </xs:sequence>
      <xs:attribute name="version" type="xs:string" use="required"/>
    </xs:complexType>
  </xs:element>

</xs:schema>

