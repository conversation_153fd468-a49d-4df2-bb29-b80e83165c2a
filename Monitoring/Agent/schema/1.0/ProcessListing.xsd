<?xml version="1.0" encoding="utf-8"?>
<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">

  <xs:complexType name="ModulesType">
    <xs:sequence>
      <xs:element name="Name" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="Version" type="xs:string" minOccurs="1" maxOccurs="1" />
    </xs:sequence>
    <xs:attribute name="size" type="xs:nonNegativeInteger" use="required" />
    <xs:attribute name="base" type="xs:nonNegativeInteger" use="required" />
  </xs:complexType>

  <xs:complexType name="ProcessInfoType">
    <xs:sequence>
      <xs:element name="ImageName" type="xs:string" minOccurs="1" maxOccurs="1"/>
      <xs:element name="Module" type="ModulesType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
    <xs:attribute name="pid" type="xs:nonNegativeInteger" use="required" />
    <xs:attribute name="parentPid" type="xs:nonNegativeInteger" use="required"/>
    <xs:attribute name="numberOfThreads" type="xs:nonNegativeInteger" use="required"/>
    <xs:attribute name="workingSetSize" type="xs:nonNegativeInteger" use="optional" default="0" />
    <xs:attribute name="peakWorkingSetSize" type="xs:nonNegativeInteger" use="optional" default="0" />
  </xs:complexType>

  <xs:complexType name="ProcessListingType">
    <xs:sequence>
      <xs:element name="Process" type="ProcessInfoType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
    <xs:attribute name="version" type="xs:string" use="required"/>
  </xs:complexType>

  <xs:element name="ProcessListings" type="ProcessListingType" />

</xs:schema>
