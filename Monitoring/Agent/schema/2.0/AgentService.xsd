<?xml version="1.0" encoding="utf-8"?>
<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">

  <xs:simpleType name="NamespaceType">
    <xs:restriction base="xs:string">
      <xs:pattern value="([a-zA-Z][a-zA-Z0-9]*)*" />
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="IdentityPredefinedType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="TenantRole"/>
      <xs:enumeration value="ComputerName"/>
      <xs:enumeration value="SourceInstance"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:complexType name="IdentityComponentType">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="name" type="xs:string" use="required" />
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>

  <xs:complexType name="IdentityType">
    <xs:sequence>
      <xs:element name="IdentityComponent" type="IdentityComponentType" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
    <xs:attribute name="type" type="IdentityPredefinedType" use="optional" />
  </xs:complexType>

  <xs:element name="AgentService">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="Identity" type="IdentityType" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attribute name="version" type="xs:string" use="required" />
      <xs:attribute name="namespace" type="NamespaceType" use="required" />
    </xs:complexType>
  </xs:element>

</xs:schema>
