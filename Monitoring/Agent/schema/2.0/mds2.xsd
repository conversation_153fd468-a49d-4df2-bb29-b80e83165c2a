<?xml version="1.0" encoding="utf-8"?>
<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">

  <!--
  From MSDN
  The queue name must be a valid DNS name, conforming to the following naming rules:
  1.A queue name must start with a letter or number, and may contain only letters, numbers and the dash (-) character.
  2.The first and last letters in the queue name must be alphanumeric. The dash (-) character may not be the first or last letter.
  3.All letters in a queue name must be lowercase.
  4.A queue name must be from 3 through 63 characters long.
  -->
  <xs:simpleType name="XstoreQueueNameType">
    <xs:restriction base="xs:string">
      <xs:pattern value="[a-z0-9][a-z0-9\-]{1,61}[a-z0-9]" />
    </xs:restriction>
  </xs:simpleType>

  <!--
  XTable has the following restriction for column name. We might as well
  have the same restriction for all our tables
  Only alphanumeric characters and '_' are allowed.
  -->
  <xs:simpleType name="ValidColumnNameType">
    <xs:restriction base="xs:string">
      <xs:pattern value="[a-zA-Z0-9_]+" />
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="IfxTracingLevelType">
    <xs:restriction base="xs:string">
        <xs:enumeration value="None"/>
        <xs:enumeration value="Critical"/>
        <xs:enumeration value="Error"/>
        <xs:enumeration value="Warning"/>
        <xs:enumeration value="Informational"/>
        <xs:enumeration value="Verbose"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="IfxSamplingMethodType">
    <xs:restriction base="xs:string">
        <xs:enumeration value="SimpleRandom"/>
        <xs:enumeration value="Throttling"/>
        <xs:enumeration value="CorrelationPreservingHash"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="DateTimeType">
    <xs:restriction base="xs:dateTime">
      <!-- enforce the time to be in UTC -->
      <xs:pattern value=".*Z" />
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="IdentityPredefinedType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="TenantRole"/>
      <xs:enumeration value="ComputerName"/>
      <xs:enumeration value="SourceInstance"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="OnBehalfFirstTagMatchType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Prefix"/>
      <xs:enumeration value="Exact"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="ExtensionStartupType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="OnMaStart"/>
      <xs:enumeration value="OnExtensionCommand"/>
      <xs:enumeration value="Disabled"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="CounterFormatType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Full"/>
      <xs:enumeration value="Factored"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:complexType name="IdentityComponentType">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="name" type="xs:string" use="required" />
        <xs:attribute name="envariable" type="xs:string" use="optional" /> <!-- Linux only -->
        <xs:attribute name="useComputerName" type="xs:string" use="optional" /> <!-- Linux only -->
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>

  <xs:complexType name="IdentityType">
    <xs:sequence>
      <xs:element name="IdentityComponent" type="IdentityComponentType" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
    <xs:attribute name="type" type="IdentityPredefinedType" use="optional" default="SourceInstance" />
    <xs:attribute name="tenantNameAlias" type="xs:string" use="optional" />
    <xs:attribute name="roleNameAlias" type="xs:string" use="optional" />
    <xs:attribute name="roleInstanceNameAlias" type="xs:string" use="optional" />
  </xs:complexType>

  <xs:simpleType name="PercentType">
    <xs:restriction base="xs:integer">
      <xs:minInclusive value="0"/>
      <xs:maxInclusive value="100"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="NotifQueueType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Message"/>
      <xs:enumeration value="Address"/>
      <xs:enumeration value="GenevaNotification"/>
    </xs:restriction>
  </xs:simpleType>

  <!-- New binary is so we can test the new compressed blob format that holds either bond or non-bond event data.
       New binary can go away when it supersedes CentralCompressedBlob. Customers will just use CentralCompressedBlob and
       MDS will be compat with both the old hourly and new formats. -->
  <xs:simpleType name="StoreType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Central"/>
      <xs:enumeration value="Local"/>
      <xs:enumeration value="Csv" />
      <xs:enumeration value="Binary" />
      <xs:enumeration value="CentralBond" />
      <xs:enumeration value="ADXml" />
      <xs:enumeration value="CentralCompress"/>
      <xs:enumeration value="CentralCompressedBlob" />
      <xs:enumeration value="CentralJson" />
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="WhereToRunType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Central"/>
      <xs:enumeration value="Local"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="PriorityType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Normal"/>
      <xs:enumeration value="Low"/>
      <xs:enumeration value="High"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="EventVolumeType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Small"/>
      <xs:enumeration value="Medium"/>
      <xs:enumeration value="Large"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="MonTypeType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="mt:int32"/>
      <xs:enumeration value="mt:int64"/>
      <xs:enumeration value="mt:bool"/>
      <xs:enumeration value="mt:float64"/>
      <xs:enumeration value="mt:utc"/>
      <xs:enumeration value="mt:wstr"/>
    </xs:restriction>
  </xs:simpleType>

  <!-- Serialized is a generic format that MA cannot interpret. It will read and log the data as binary. -->
  <xs:simpleType name="EventFormatType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Xml"/>
      <xs:enumeration value="Manifest"/>
      <xs:enumeration value="EventSource"/>
      <xs:enumeration value="Serialized"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="DefaultEventFormatType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None"/>
      <xs:enumeration value="Escaped"/>
      <xs:enumeration value="Json"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="OneDSFormatType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Structured"/>
      <xs:enumeration value="Json"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="EtwProviderGuidType">
    <xs:restriction base="xs:string">
      <xs:pattern value="([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}[,;])*[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}" />
      <xs:pattern value="MonitoringAgentSourceEvents" />
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="EtwProviderNameType">
    <xs:restriction base="xs:string" />
  </xs:simpleType>

  <xs:simpleType name="CompressionType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="none"/>
      <xs:enumeration value="gzip"/>
      <xs:enumeration value="lz4"/>
      <xs:enumeration value="lz4hc"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="HeaderFieldNameType">
    <xs:restriction base="xs:token">
      <xs:enumeration value="Version"/>
      <xs:enumeration value="EventVersion"/>
      <xs:enumeration value="Level"/>
      <xs:enumeration value="LevelName"/>
      <xs:enumeration value="Pid"/>
      <xs:enumeration value="Tid"/>
      <xs:enumeration value="ProviderGuid"/>
      <xs:enumeration value="ProviderName"/>
      <xs:enumeration value="EventId"/>
      <xs:enumeration value="Task"/>
      <xs:enumeration value="TaskName"/>
      <xs:enumeration value="KeywordMask"/>
      <xs:enumeration value="KeywordName"/>
      <xs:enumeration value="EventMessage"/>
      <xs:enumeration value="Opcode"/>
      <xs:enumeration value="OpcodeName"/>
      <xs:enumeration value="ActivityId"/>
      <xs:enumeration value="RelatedActivityId"/>
      <xs:enumeration value="Channel"/>
      <xs:enumeration value="ChannelName"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="BondEnvelopeType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None"/>
      <xs:enumeration value="Flattened"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:complexType name="ImportType">
    <xs:sequence>
      <xs:element name="Condition" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
    <xs:attribute name="file" type="xs:string" use="required" />
    <xs:attribute name="forceLocal" type="xs:boolean" use="optional" />
  </xs:complexType>

  <xs:complexType name="ImportsType">
    <xs:sequence>
      <xs:element name="Import" type="ImportType" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="BlobStoreType">
    <xs:attribute name="container" type="xs:string" use="optional" default="mam" />
    <xs:attribute name="compressionType" type="CompressionType" use="optional" default="gzip" />
    <xs:attribute name="retentionInDays" type="xs:unsignedInt" use="optional" />
  </xs:complexType>

    <!-- isDefault specifies the default mapping MDS uses as the destination for centrally aggregated data.
    -->
  <xs:complexType name="AccountMappingType">
    <xs:attribute name="moniker" type="xs:string" use="required" />
	<xs:attribute name="comment" type="xs:string" use="optional" />
    <xs:attribute name="isDefault" type="xs:boolean" use="optional" />
  </xs:complexType>

    <!-- autoKey is set to true if MA should get the keys for this account from MDS directly; instead of,
         from this configuration file.
         If autoKey is true then you need the SAS key URL (bootstrap key) to locate the rest of the
         account information.
    -->
  <xs:complexType name="AccountType">
    <xs:sequence>
      <xs:element name="Mapping" type="AccountMappingType" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
    <xs:attribute name="moniker" type="xs:string" use="required" />
    <xs:attribute name="alias" type="xs:string" use="optional" />
    <xs:attribute name="name" type="xs:string" use="optional" />
    <xs:attribute name="key" type="xs:string" use="optional" />
    <xs:attribute name="uri" type="xs:anyURI" use="optional" default="https://core.windows.net" />
    <xs:attribute name="certificateStore" type="xs:string" use="optional" />
    <xs:attribute name="usePathStyleUris" type="xs:boolean" use="optional" default="false" />
    <xs:attribute name="isDefault" type="xs:boolean" use="optional" />
    <xs:attribute name="isSql" type="xs:boolean" use="optional" />
    <xs:attribute name="autoKey" type="xs:boolean" use="optional" default="false" />
    <xs:attribute name="autoKeyUri" type="xs:string" use="optional" />
    <xs:attribute name="streamingSubscriptionIntervalInMinutes" type="xs:int" use="optional" />
  </xs:complexType>

  <xs:complexType name="SasType">
    <xs:attribute name="moniker" type="xs:string" use="required" />
    <xs:attribute name="alias" type="xs:string" use="optional" />
    <xs:attribute name="key" type="xs:string" use="optional" />
    <xs:attribute name="containerMoniker" type="xs:string" use="optional" />
    <xs:attribute name="certificateStore" type="xs:string" use="optional" />
    <xs:attribute name="account" type="xs:string" use="optional" /> <!-- Linux only -->
  </xs:complexType>

  <xs:complexType name="AccountsType">
    <xs:sequence>
      <xs:element name="Account" type="AccountType" minOccurs="1" maxOccurs="unbounded" />
      <xs:element name="SharedAccessSignature" type="SasType" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>

  <xs:simpleType name="ResourceThrottlingType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None"/>
      <xs:enumeration value="Soft"/>
      <xs:enumeration value="Hard"/>
      <xs:enumeration value="false"/>
      <xs:enumeration value="true"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:complexType name="ResourceRestrictionsType">
    <xs:attribute name="disableSafeLimits" type="xs:boolean" use="optional" />
    <xs:attribute name="cpuPercentUsage" type="PercentType" use="optional" default="50" />
    <xs:attribute name="cpuThrottling" type="ResourceThrottlingType" use="optional" default="true" />
    <xs:attribute name="ioReadLimitInKBPerSecond" type="xs:unsignedInt" use="optional" />
    <xs:attribute name="ioReadThrottling" type="ResourceThrottlingType" use="optional" default="None" />
    <xs:attribute name="ioWriteLimitInKBPerSecond" type="xs:unsignedInt" use="optional" />
    <xs:attribute name="ioWriteThrottling" type="ResourceThrottlingType" use="optional" default="None" />
    <xs:attribute name="memoryLimitInKB" type="xs:unsignedInt" use="optional" />
    <xs:attribute name="memoryLimitInMB" type="xs:unsignedInt" use="optional" default="2000" />
    <xs:attribute name="memoryThrottling" type="ResourceThrottlingType" use="optional" default="true" />
    <xs:attribute name="workingSetLimitInMB" type="xs:unsignedInt" use="optional" default="2000" />
    <xs:attribute name="workingSetThrottling" type="ResourceThrottlingType" use="optional" default="false" />
    <xs:attribute name="recycleOnMemory" type="xs:boolean" use="optional" default="true" />
    <xs:attribute name="jobObjectName" type="xs:string" use="optional" />
  </xs:complexType>

  <xs:complexType name="AgentResourceUsageType">
    <xs:sequence>
      <xs:element name="BlobStore" type="BlobStoreType" minOccurs="0" maxOccurs="1" />
      <xs:element name="ExtensionResourceUsage" type="ResourceRestrictionsType" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
    <xs:attribute name="disableSafeLimits" type="xs:boolean" use="optional" />
    <xs:attribute name="cpuPercentUsage" type="PercentType" use="optional" default="50" />
    <xs:attribute name="cpuThrottling" type="xs:boolean" use="optional" default="true" />
    <xs:attribute name="ioReadLimitInKBPerSecond" type="xs:unsignedInt" use="optional" />
    <xs:attribute name="ioReadThrottling" type="xs:boolean" use="optional" default="false" />
    <xs:attribute name="ioWriteLimitInKBPerSecond" type="xs:unsignedInt" use="optional" />
    <xs:attribute name="ioWriteThrottling" type="xs:boolean" use="optional" default="false" />
    <xs:attribute name="memoryLimitInMB" type="xs:unsignedInt" use="optional" />
    <xs:attribute name="memoryThrottling" type="xs:boolean" use="optional" default="false" />
    <xs:attribute name="diskQuotaInMB" type="xs:unsignedInt" use="required" />
    <xs:attribute name="networkQuotaInKbps" type="xs:unsignedInt" use="optional" />
    <xs:attribute name="maxNodeDiagnosticsLatencyInSeconds" type="xs:unsignedInt" use="optional" />
    <xs:attribute name="workingSetLimitInMB" type="xs:unsignedInt" use="optional" default="2000" />
    <xs:attribute name="workingSetThrottling" type="ResourceThrottlingType" use="optional" default="false" />
    <xs:attribute name="counterObjectRefreshRateInMinutes" type="xs:unsignedInt" use="optional" default="5" />
    <xs:attribute name="jobObjectName" type="xs:string" use="optional" />
    <xs:attribute name="jobObjectCopy" type="xs:boolean" use="optional" default="false" />
  </xs:complexType>

  <xs:complexType name="AgentMetricsType">
    <xs:attribute name="mdmMonitoringAccount" type="xs:string" use="required" />
    <xs:attribute name="useFullIdentity" type="xs:boolean" use="optional" default="false" />
    <xs:attribute name="eventNameRegexFilter" type="xs:string" use="optional" />
  </xs:complexType>

  <xs:complexType name="ManagementType">
    <xs:sequence>
      <xs:element name="Identity" type="IdentityType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="AgentResourceUsage" type="AgentResourceUsageType" minOccurs="1" maxOccurs="1" />
      <xs:element name="AgentMetrics" type="AgentMetricsType" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
    <xs:attribute name="eventVolume" type="EventVolumeType" use="optional" default="Medium" />
    <xs:attribute name="defaultRetentionInDays" type="xs:unsignedInt" use="optional" default="29" />
    <xs:attribute name="secureTransport" type="xs:boolean" use="optional" default="true" />
    <xs:attribute name="deployToMds" type="xs:boolean" use="optional" default="true" />
    <xs:attribute name="bootstrapping" type="xs:boolean" use="optional" default="false" />
    <xs:attribute name="e2eDomain" type="xs:string" use="optional" />
    <xs:attribute name="e2eIndexLoad" type="EventVolumeType" use="optional" default="Medium" />
    <!-- Disables all of node diagonstics, including autokey, agent update, etc... -->
    <xs:attribute name="disableNodeDiagnostics" type="xs:boolean" use="optional" default="false" />
    <!-- If this is set to false, then on-demand requests and the TCP connection to MA are disabled, but
         node diagnostics still works for broadcast commands, like config update and autokey -->
    <xs:attribute name="onDemandRequests" type="xs:boolean" use="optional" default="true" />
    <xs:attribute name="onDemandRequestFlags" type="xs:string" use="optional" />
    <!-- Only support on demand requests needed for table search -->
    <xs:attribute name="localTableSearchOnly" type="xs:boolean" use="optional" default="false" />
    <!-- Use this to turn off MA pushing host logs to Agent Explorer - For external customer scenarios -->
    <xs:attribute name="noHostLogReporting" type="xs:boolean" use="optional" default="false" />
    <!-- OBSOLETE and replaced with onBehalfIdentity -->
    <xs:attribute name="serviceIdentity" type="xs:string" use="optional" />
    <!-- serviceIdentity is obsolete and this replaces it. They are interchangable, however -->
    <xs:attribute name="onBehalfIdentity" type="xs:string" use="optional" />
    <xs:attribute name="onBehalfFieldTags" type="xs:string" use="optional" />
    <xs:attribute name="onBehalfReplaceTags" type="xs:string" use="optional" />
    <!-- Determines how MA will compare customer ID's with logged customer identies. Default to the most secure way. -->
    <xs:attribute name="firstTagMatchMode" type="OnBehalfFirstTagMatchType" use="optional" default="Exact" />
  </xs:complexType>

  <xs:complexType name="EventBaseType">
    <xs:attribute name="account" type="xs:string" use="optional" />
    <xs:attribute name="auditAccount" type="xs:string" use="optional" />
    <xs:attribute name="storeType" type="StoreType" use="optional" />
    <xs:attribute name="retentionInDays" type="xs:unsignedInt" use="optional" />
    <xs:attribute name="deadline" type="xs:duration" use="optional" />
    <xs:attribute name="priority" type="PriorityType" use="optional" />
    <xs:attribute name="retryTimeout" type="xs:duration" use="optional" />
    <xs:attribute name="duration" type="xs:duration" use="optional" />
    <xs:attribute name="minDiskQuotaInMB" type="xs:unsignedInt" use="optional" />
    <xs:attribute name="diskQuotaInMB" type="xs:unsignedInt" use="optional" />
    <xs:attribute name="repeat" type="xs:unsignedInt" use="optional"/>
    <xs:attribute name="startTime" type="DateTimeType" use="optional" />
    <xs:attribute name="startTimeDeltaInSeconds" type="xs:int" use="optional" default="0" />
    <xs:attribute name="endTime" type="DateTimeType" use="optional"/>
    <xs:attribute name="noMdsGc" type="xs:boolean" use="optional" default="false" />
    <xs:attribute name="dontUsePerNDayTable" type="xs:boolean" use="optional" />
    <xs:attribute name="dontUseNPartitionSpread" type="xs:boolean" use="optional" default="false" />
    <!-- enable secondary indexing on specified columns for central events. The value
         should be comma seperated column names -->
    <xs:attribute name="additionalIndexes" type="xs:string" use="optional" />
    <xs:attribute name="activityIdFieldName" type="xs:string" use="optional" />
    <xs:attribute name="disableE2E" type="xs:boolean" use="optional" default="false" />
    <!-- Only recognized if the storing the data in blob format (e.g. Csv or file) -->
    <xs:attribute name="compressionType" type="CompressionType" use="optional" />
    <xs:attribute name="partitionFields" type="xs:string" use="optional" />
    <xs:attribute name="eventSas" type="xs:string" use="optional" />
    <!-- Identifies that rows in this event should be multiplexed to customer accounts based on the value of a field in the event
         The onBehalfFields attribute matches the onBehalfFieldTags attribute in the management section. The number of comma
         separated # of onBehalfFields <= # of onBehalfFieldTags.
    -->
    <xs:attribute name="onBehalfFields" type="xs:string" use="optional" />
    <!-- Old attribute for specifying the onbehalf field name. -->
    <xs:attribute name="customerResourceFieldName" type="xs:string" use="optional" />
    <!-- If enabled to log to a customer resource (on-behalf field), this can be used to specify to also log to the service account -->
    <xs:attribute name="logToServiceEvent" type="xs:boolean" use="optional" default="false"/>
    <!-- Enable to allow multiple file watchers with the same event name to have different containers.
         2/23 - Changing default from false to true, since it was kept false to prevent an incompatibility
         with MA22. -->
    <xs:attribute name="multiContainers" type="xs:boolean" use="optional" default="true" />
    <!-- How long to delay running the event derived from the external tables -->
    <xs:attribute name="queryDelay" type="xs:duration" use="optional" />
    <xs:attribute name="extendedAttributes" type="xs:string" use="optional"/>
    <xs:attribute name="bondEnvelopeType" type="BondEnvelopeType" use="optional"/>
  </xs:complexType>

  <!--
  From MSDN (event names following the xstore table name restrictions)
  -->
  <xs:simpleType name="EventNameType">
    <xs:restriction base="xs:string">
      <xs:pattern value="[a-zA-Z][\._a-zA-Z0-9]{0,62}" />
    </xs:restriction>
  </xs:simpleType>

  <xs:complexType name="EventWithNameType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:attribute name="eventName" type="EventNameType" use="required" />
        <xs:attribute name="physicalName" type="EventNameType" use="optional" />
        <xs:attribute name="noOnDiskIndex" type="xs:boolean" use="optional" default="false" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="EventWithNameAndIdType">
    <xs:complexContent>
      <xs:extension base="EventWithNameType">
        <xs:attribute name="id" type="xs:unsignedInt" use="optional" />
        <!-- If the ETW event comes from a TraceLogging source it uses name instead of id -->
        <xs:attribute name="name" type="xs:string" use="optional" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:simpleType name="CounterTypeType">
    <xs:restriction base="MonTypeType">
      <xs:enumeration value="mt:int32"/>
      <xs:enumeration value="mt:int64"/>
      <xs:enumeration value="mt:float64"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:complexType name="MdmDimensionType">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="name" type="xs:string" use="required" />
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>

  <xs:complexType name="MdmDimensionsType">
    <xs:sequence>
      <xs:element name="MdmDimension" type="MdmDimensionType" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="CounterSetType">
    <xs:complexContent>
      <xs:extension base="EventWithNameType">
        <xs:sequence>
          <xs:element name="MdmDimensions" type="MdmDimensionsType" minOccurs="0" maxOccurs="1" />
          <xs:element name="AddContainerMetadataFields" type="AddContainerMetadataFieldsType" minOccurs="0" maxOccurs="1" />
          <xs:element name="RemoveContainerMetadataFields" type="RemoveContainerMetadataFieldsType" minOccurs="0" maxOccurs="1" />
          <xs:element name="Counter" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
          <xs:element name="DockerStat" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
        <xs:attribute name="sampleRateInSeconds" type="xs:unsignedInt" use="optional" default="5" />
        <xs:attribute name="valueType" type="CounterTypeType" use="optional" default="mt:float64" />
        <xs:attribute name="mdmNamespace" type="xs:string" use="optional" />
        <xs:attribute name="mdmMonitoringAccount" type="xs:string" use="optional" />
        <xs:attribute name="format" type="CounterFormatType" use="optional" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="EtwEventType">
    <xs:complexContent>
      <xs:extension base="EventWithNameAndIdType">
        <xs:attribute name="logToDefault" type="xs:boolean" use="optional" default="false"/>
        <xs:attribute name="maValidationSuffix" type="xs:string" use="optional" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="HeartBeatType">
      <xs:complexContent>
          <xs:extension base="EventWithNameType">
              <xs:attribute name="sampleRateInSeconds" type="xs:unsignedInt" use="optional" default="0" />
          </xs:extension>
      </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="DefaultEventType">
      <xs:complexContent>
          <xs:extension base="EventWithNameType">
              <xs:attribute name="format" type="DefaultEventFormatType" use="optional" default="None" />
          </xs:extension>
      </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="EventSourceFilterType">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="key" type="xs:string" use="required" />
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>

  <xs:complexType name="EventSourceNameType">
    <xs:simpleContent>
      <xs:extension base="xs:string"/>
    </xs:simpleContent>
  </xs:complexType>

  <xs:complexType name="AdditionalHeaderFieldsType">
    <xs:sequence>
      <xs:element name="Field" type="HeaderFieldNameType" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="EventSourceFiltersType">
    <xs:sequence>
      <xs:element name="EventSourceFilter" type="EventSourceFilterType" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="EtwProviderType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="EventSourceName" type="EventSourceNameType" minOccurs="0" maxOccurs="1" />
          <xs:element name="EventSourceFilters" type="EventSourceFiltersType" minOccurs="0" maxOccurs="1" />
          <xs:element name="AdditionalHeaderFields" type="AdditionalHeaderFieldsType" minOccurs="0" maxOccurs="1" />
          <xs:element name="RemoveHeaderFields" type="AdditionalHeaderFieldsType" minOccurs="0" maxOccurs="1" />
          <xs:element name="DefaultEvent" type="DefaultEventType" minOccurs="0" maxOccurs="1" />
          <xs:element name="Event" type="EtwEventType" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
        <xs:attribute name="guid" type="EtwProviderGuidType" use="optional" />
        <xs:attribute name="name" type="EtwProviderNameType" use="optional" />
        <xs:attribute name="enableFlags" type="xs:string" use="optional" />
        <xs:attribute name="stackFlags" type="xs:string" use="optional" />
        <xs:attribute name="format" type="EventFormatType" use="required" />
        <xs:attribute name="useArrivalTimestamp" type="xs:boolean" use="optional" default="false" />
        <xs:attribute name="levelFilter" type="xs:byte" use="optional" default="0"/>
        <xs:attribute name="keywordFilter" type="xs:unsignedLong" use="optional" default="0"/>
        <xs:attribute name="ignoreKeyword_0" type="xs:boolean" use="optional" default="true" />
        <xs:attribute name="providerHeartbeats" type="xs:boolean" use="optional" default="true"/>
        <xs:attribute name="showFieldStructure" type="xs:boolean" use="optional" />
        <xs:attribute name="isProviderGroup" type="xs:boolean" use="optional" default="false" />
        <xs:attribute name="disableMessageFormatting" type="EventMsgFormattingType" use="optional" />
        <xs:attribute name="manifest" type="xs:string" use="optional" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="FileWatchItemType">
    <xs:complexContent>
      <xs:extension base="EventWithNameType">
        <xs:sequence>
          <xs:element name="Directory" type="xs:string" minOccurs="0" maxOccurs="1" />
        </xs:sequence>
        <xs:attribute name="directoryQuotaInMB" type="xs:unsignedInt" use="optional" default="0" />
        <xs:attribute name="lastChangeOffsetInSeconds" type="xs:unsignedInt" use="optional" />
        <xs:attribute name="removeEmptyDirectories" type="xs:boolean" use="optional" default="true" />
        <xs:attribute name="useIdentityPath" type="xs:boolean" use="optional" default="true" />
        <xs:attribute name="filter" type="xs:string" use="optional" />
        <xs:attribute name="contextParam" type="xs:string" use="optional" />
        <xs:attribute name="container" type="xs:string" use="optional" />
        <xs:attribute name="uploadDelayInSeconds" type="xs:unsignedInt" use="optional" default="0" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="CrashDumpItemType">
    <xs:complexContent>
      <xs:extension base="EventWithNameType">
        <xs:sequence>
          <!-- If AzureWatson is used then this directory will be used as a WER root: <Directory>\WER\ReportQueue, <Directory>\WER\UserDumps -->
          <xs:element name="Directory" type="xs:string" minOccurs="0" maxOccurs="1" />
          <xs:element name="ProcessList" type="ProcessListType" minOccurs="1" maxOccurs="1" />
        </xs:sequence>
        <!-- 
            If the size of directory holding dump files grows larger,
            the older dump files start getting deleted.
        -->
        <xs:attribute name="directoryQuotaInMB" type="xs:unsignedInt" use="optional" default="0" />
        <!-- 
            When the percentage of the free space on the disk that hosts the queue drops below,
            no dump files are created. That is a failsafe for the case when the agent is not running.
            The AzureWatson QoS stream that the crash dump generation code emits, provides the way to 
            discover that condition.
        -->
        <xs:attribute name="minFreeDiskSpacePercentage" type="xs:unsignedInt" use="optional" default="20" />
        <xs:attribute name="dumpType" type="DumpTypeType" default="Mini" />
        <xs:attribute name="bucketing" type="xs:boolean" default="true" />
        <xs:attribute name="bucketingGranularity" type="xs:string" use="optional" default="NameSpace" />
        <xs:attribute name="container" type="xs:string" use="optional" />
        <!-- use azure watson protocol for now we set it default to false but then we will change it to true. -->
        <xs:attribute name="useAzureWatson" type="xs:boolean" use="optional" default="false" />
        <!-- user can override which endpoint ma should talk to. By default it will use the one broadcasted by MDS. For users who want to use azure Watson should set this -->
        <xs:attribute name="watsonEndpoint" type="xs:string" use="optional" />
        <xs:attribute name="dumpTimeoutSeconds" type="xs:unsignedInt" use="optional" default="0" />
        <xs:attribute name="aedebugTimeoutSeconds" type="xs:unsignedInt" use="optional" default="0" />
        <!-- DO NOT USE THE watsonWERDriveLetter ATTRIBUTE WILL BE REMOVE IN MA 32, USE THE DIRECTORY ELEMENT INSTEAD -->
        <xs:attribute name="watsonWERDriveLetter" type="xs:string" use="optional" />
        <!--
            Whether all threads in the faulting process have to be suspended during dump collection or not.
            If a crash handling is initialted via the kernel path, the faulting thread is frozen; for the
            unhandled exception filter path the faulting thread waits on the handle of the reporting process.
            It may be dangerous to suspend all threads if a crashing process is a system-wide RPC server, etc.
            For the absolute majority of cases this setting has to be set to true.
        -->
        <xs:attribute name="suspendThreads" type="xs:boolean" use="optional" />
        <!--
            Exclude regions whose combined size is above 512 MiB which are made up of the same allocation size.
            If allocation size is A and allocation number for it is N, then all regions with A*N >= 512MiB are excluded.
            Default: false.
        -->
        <xs:attribute name="excludeLargeVaRegions" type="xs:boolean" use="optional" />
        <!--
            Terminate the process by default. The opposite is to resume the faulting process so it exits from the
            system default unhandled exception filter.
            Default: true
        -->
        <xs:attribute name="forceTermination" type="xs:boolean" use="optional" />
        <!--
            If termination fails (extremely rare cases), then resume it. Leaving the process suspended may be useful for
            debugging.
            Default: true
        -->
        <xs:attribute name="resumeIfTerminationFails" type="xs:boolean" use="optional" />
        <!--
            Do not create dump files at all. Spare the system the additional load incurred when dump files are created,
            the dump files with full memory in particular as in that case all the memory pages may be briefly paged in when walking
            the memory contents of the faulting process thus increasing its working set and memory pressure in the system.
            Crash reports are sent to Azure Watson in any case.
            Default: false
        -->
        <xs:attribute name="noDumpFiles" type="xs:boolean" use="optional" />
        <!--
            Create a smaller dump file only which takes less time and other resources. By default, Azure Watson creates two dump files
            to improve the chances of reporting valuable diagnostic information. The larger dump creation may fail quite often,
            especially if a watchdog process watches after the faulting one so the faulting process cannot be suspended for a long time
            without being terminated by the watchdog.
            Default: false
        -->
        <xs:attribute name="noFullMemoryDumps" type="xs:boolean" use="optional" />
        <!--
            Use more threads in the thread pool when uploading. The thread pool is created on demand, each additional thread allocates 2..8 MiB depending
            on the compressed dump file's size. The limit for uploading dump files is 400 GiB currently per dump file.
            Default: 4
        -->
        <xs:attribute name="uploadWorkerCount" type="xs:unsignedInt" use="optional" default="4"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="ProcessListType">
    <xs:sequence>
      <xs:element name="Process" type="ProcessListItemType" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="ProcessListItemType">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="dumpTimeoutSeconds" type="xs:unsignedInt" use="optional" default="0" />
        <xs:attribute name="aedebugTimeoutSeconds" type="xs:unsignedInt" use="optional" default="0" />
        <xs:attribute name="suspendThreads" type="xs:boolean" use="optional" />
        <xs:attribute name="excludeLargeVaRegions" type="xs:boolean" use="optional" />
        <xs:attribute name="forceTermination" type="xs:boolean" use="optional" />
        <xs:attribute name="resumeIfTerminationFails" type="xs:boolean" use="optional" />
        <xs:attribute name="noDumpFiles" type="xs:boolean" use="optional" />
        <xs:attribute name="noFullMemoryDumps" type="xs:boolean" use="optional" />
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>

  <xs:simpleType name="DumpTypeType">
    <xs:restriction base="xs:string" >
      <xs:enumeration value="Mini"/>
      <xs:enumeration value="Heap"/>
      <xs:enumeration value="Full"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="ColumnNameType">
    <xs:restriction base="xs:string">
      <xs:pattern value="[a-zA-Z_][a-zA-Z0-9_]{0,254}" />
    </xs:restriction>
  </xs:simpleType>

  <xs:complexType name="WindowsEventLogSubscriptionType">
    <xs:complexContent>
      <xs:extension base="EventWithNameType">
        <xs:sequence>
          <xs:element name="Column" type="ColumnType" minOccurs="1" maxOccurs="unbounded" />
        </xs:sequence>
        <xs:attribute name="query" type="xs:string" use="required" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="ColumnType">
    <xs:choice>
      <xs:element name="Value" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="Range" type="xs:string" minOccurs="2" maxOccurs="2"/>
    </xs:choice>
    <xs:attribute name="name" type="ColumnNameType" use="required" />
    <xs:attribute name="type" type="MonTypeType" use="optional" />
    <xs:attribute name="defaultAssignment" type="xs:string" use="optional" />
  </xs:complexType>

  <xs:complexType name="AutoLoggerEventType" >
    <xs:sequence>
      <xs:element name="Column" type="ColumnType" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
    <xs:attribute name="eventName" type="EventNameType" use="required" />
    <xs:attribute name="count" type="xs:unsignedInt" use="required" />
    <xs:attribute name="level" type="xs:unsignedInt" use="optional" />
  </xs:complexType>

  <xs:complexType name="AutoLoggerType">
    <xs:sequence>
      <xs:element name="Event" type="AutoLoggerEventType" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
    <xs:attribute name="duration" type="xs:duration" use="required" />
  </xs:complexType>

  <xs:complexType name="ContainerPathType" >
    <xs:attribute name="name" type="xs:string" use="required" />
	<xs:attribute name="account" type="xs:string" use="optional" />
    <xs:attribute name="retentionInDays" type="xs:unsignedInt" use="optional" />
  </xs:complexType>

  <xs:complexType name="ContainerType">
    <xs:sequence>
      <xs:element name="Path" type="ContainerPathType" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
    <xs:attribute name="name" type="xs:string" use="required" />
  </xs:complexType>

  <xs:complexType name="NotificationQueueActionType">
    <xs:sequence>
      <xs:element name="Message" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
    <xs:attribute name="queueName" type="XstoreQueueNameType" use="required" />
    <xs:attribute name="queueType" type="NotifQueueType" use="optional" default="Message" />
    <xs:attribute name="account" type="xs:string" use="optional" />
  </xs:complexType>

  <xs:complexType name="PostTaskActionsType">
    <xs:sequence>
      <xs:element name="SignalEventAction" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="NotificationQueueAction" type="NotificationQueueActionType" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="DataMiningEventType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:attribute name="eventName" type="xs:string" use="required" />
        <xs:attribute name="generateChangeScore" type="xs:boolean" default="true" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="DerivedEventType">
    <xs:complexContent>
      <xs:extension base="EventWithNameType">
        <xs:sequence>
          <xs:element name="PostTaskActions" type="PostTaskActionsType" minOccurs="0" maxOccurs="1" />
          <xs:element name="Query" type="xs:string" minOccurs="1" maxOccurs="1" />
        </xs:sequence>
        <xs:attribute name="source" type="xs:string" use="optional" />
        <xs:attribute name="sourceRegex" type="xs:string" use="optional" />
        <xs:attribute name="externalSource" type="xs:string" use="optional" />
        <xs:attribute name="externalSourceNumVersions" type="xs:unsignedInt" use="optional" default="0" />
        <xs:attribute name="overwriteDestTable" type="xs:boolean" use="optional" default="false" />
        <xs:attribute name="whereToRun" type="WhereToRunType" use="optional" />
        <xs:attribute name="keepEventNameAsIs" type="xs:boolean" use="optional" default="false" />
        <xs:attribute name="dontUploadMetaObject" type="xs:boolean" use="optional" default="false" />
        <xs:attribute name="extension" type="xs:string" use="optional" />
        <xs:attribute name="noOnDisk" type="xs:boolean" use="optional" default="false" />
        <xs:attribute name="allowDuplicates" type="xs:boolean" use="optional" default="false" />
        <xs:attribute name="keepMonikerMapping" type="xs:boolean" use="optional" default="false" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="IisLogWatchItemType">
    <xs:complexContent>
      <xs:extension base="EventWithNameType">
        <xs:attribute name="filter" type="xs:string" use="optional" />
        <xs:attribute name="directoryQuotaInMB" type="xs:unsignedInt" use="optional" default="0" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="IisLogSubscriptionsType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="Subscription" type="IisLogWatchItemType" minOccurs="1" maxOccurs="unbounded" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="TextLogDelimitersType">
    <xs:sequence>
      <xs:element name="Delimiter" type="xs:string" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
    <xs:attribute name="consecutiveDelimitersAsOne" type="xs:boolean" use="optional" />
  </xs:complexType>

  <xs:complexType name="TextLogColumnType">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="name" type="xs:string" use="optional" />
        <xs:attribute name="type" type="xs:string" use="optional" />
        <xs:attribute name="isTimestamp" type="xs:boolean" use="optional" default="false" />
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>

  <xs:complexType name="TextLogSchemaType">
    <xs:sequence>
      <xs:element name="Column" type="TextLogColumnType" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
    <xs:attribute name="header" type="xs:boolean" use="optional" default="false" />
    <xs:attribute name="maxRowSize" type="xs:unsignedInt" use="optional" default="32000" />
    <xs:attribute name="textQualifier" type="xs:string" use="optional" default="&quot;" />
    <xs:attribute name="rowEndPattern" type="xs:string" use="optional" />
    <xs:attribute name="rowComment" type="xs:string" use="optional" />
    <xs:attribute name="columnNamePrefix" type="xs:string" use="optional" />
    <xs:attribute name="defaultType" type="xs:string" use="optional" />
  </xs:complexType>

  <xs:simpleType name="TextLogFormatType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Delimited"/>
      <xs:enumeration value="W3c"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:complexType name="TextLogWatchItemType">
    <xs:complexContent>
      <xs:extension base="EventWithNameType">
        <xs:sequence>
          <xs:element name="Delimiters" type="TextLogDelimitersType" minOccurs="0" maxOccurs="1" />
          <xs:element name="Schema" type="TextLogSchemaType" minOccurs="0" maxOccurs="1" />
        </xs:sequence>
        <xs:attribute name="format" type="TextLogFormatType" use="required" />
        <xs:attribute name="path" type="xs:string" use="required" />
        <xs:attribute name="nameFilter" type="xs:string" use="optional" />
        <xs:attribute name="ignoreSubDirectories" type="xs:boolean" use="optional" default="false" />
        <xs:attribute name="fileCacheSize" type="xs:unsignedInt" use="optional" default="128" />
        <xs:attribute name="fileCacheRefreshInSeconds" type="xs:unsignedInt" use="optional" default="3600" />
        <xs:attribute name="directoryQuotaInMB" type="xs:unsignedInt" use="optional" default="0" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="TextLogSubscriptionsType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="Subscription" type="TextLogWatchItemType" minOccurs="1" maxOccurs="unbounded" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

   <xs:complexType name="HeartBeatsType">
       <xs:complexContent>
         <xs:extension base="EventBaseType">
             <xs:sequence>
                 <xs:element name="HeartBeat" type="HeartBeatType" minOccurs="1" maxOccurs="1" /> <!-- max of 1 for now -->
             </xs:sequence>
         </xs:extension>
       </xs:complexContent>
   </xs:complexType>

  <xs:simpleType name="ContainerMetadataFieldType">
    <xs:restriction base="xs:token">
      <xs:enumeration value="ContainerName"/>
      <xs:enumeration value="ContainerId"/>
      <xs:enumeration value="ContainerImageId"/>
      <xs:enumeration value="ContainerImageOsVersion"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:complexType name="AddContainerMetadataFieldsType">
    <xs:sequence>
      <xs:element name="Field" type="ContainerMetadataFieldType" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="EnvironmentVariable" type="ContainerEnvironmentVariableType" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="RemoveContainerMetadataFieldsType">
    <xs:sequence>
      <xs:element name="Field" type="ContainerMetadataFieldType" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="ContainerEnvironmentVariableType">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="variableName" type="xs:string" use="required" />
        <xs:attribute name="columnName" type="xs:string" use="required" />
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>

  <xs:complexType name="CounterSetsType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="CounterSet" type="CounterSetType" minOccurs="1" maxOccurs="unbounded" />
        </xs:sequence>
        <xs:attribute name="mdmMonitoringAccount" type="xs:string" use="optional" />
        <xs:attribute name="dockerEngine" type="xs:string" use="optional" />
        <xs:attribute name="format" type="CounterFormatType" use="optional" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <!-- If set to true MA does not do event message parameter substitution. If set to all, MA does not
       do the substitution for every provider in the config (even imports), unless those providers specify
       false explicitly -->
  <xs:simpleType name="EventMsgFormattingType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="true"/>
      <xs:enumeration value="false"/>
      <xs:enumeration value="all"/>
    </xs:restriction>
  </xs:simpleType>

  <!-- If persist session is true then MA will not stop the current ETW session when it shuts down or
       refresh the session when it starts back up. The result is that events logged while MA is shutdown
       are still recorded.

       useSystemTime - If this is true the ETW session is started with the use system time as timestamp option.
                       If this is set to true anywhere, then it affects all <Providers> elements.

       -->
  <xs:complexType name="EtwProvidersType">
    <xs:sequence>
      <xs:element name="AdditionalHeaderFields" type="AdditionalHeaderFieldsType" minOccurs="0" maxOccurs="1" />
      <xs:element name="RemoveHeaderFields" type="AdditionalHeaderFieldsType" minOccurs="0" maxOccurs="1" />
      <xs:element name="EtwProvider" type="EtwProviderType" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
    <xs:attribute name="persist" type="xs:string" use="optional" default="true" />
    <xs:attribute name="traceSessionName" type="xs:string" use="optional" />
    <xs:attribute name="attachToSession" type="xs:boolean" use="optional" />
    <xs:attribute name="disableMessageFormatting" type="EventMsgFormattingType" use="optional" />
    <xs:attribute name="showFieldStructure" type="xs:boolean" use="optional" />
    <xs:attribute name="useSystemTime" type="xs:boolean" use="optional" default="false" />
    <xs:attribute name="maxSessionBufferSizeMB" type="xs:unsignedInt" use="optional" />
  </xs:complexType>

  <xs:complexType name="OneDSEventType">
      <xs:complexContent>
          <xs:extension base="EventWithNameType">
              <xs:attribute name="format" type="OneDSFormatType" use="optional" />
          </xs:extension>
      </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="OneDSProviderType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="DefaultEvent" type="DefaultEventType" minOccurs="0" maxOccurs="1" />
          <xs:element name="Event" type="OneDSEventType" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
        <xs:attribute name="name" type="xs:string" use="required" />
        <xs:attribute name="format" type="OneDSFormatType" use="optional" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="OneDSProvidersType">
    <xs:sequence>
      <xs:element name="OneDSProvider" type="OneDSProviderType" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="FileMonitorsType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="FileWatchItem" type="FileWatchItemType" minOccurs="1" maxOccurs="unbounded" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="DirectoryWatchItemType">
    <xs:complexContent>
      <xs:extension base="EventWithNameType">
        <xs:sequence>
          <xs:element name="Directory" type="xs:string" minOccurs="1" maxOccurs="1" />
        </xs:sequence>
        <xs:attribute name="manageDiskQuota"  type="xs:boolean" use="optional" default="false" />
        <xs:attribute name="useAccountInfoInTableMetaData"  type="xs:boolean" use="optional" default="false" />
        <xs:attribute name="useEventNameInTableMetaData" type="xs:boolean" use="optional" default="false" />
        <xs:attribute name="noMAManagement" type="xs:boolean"  use="optional" default="false" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="DynamicEventsType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="DirectoryWatchItem" type="DirectoryWatchItemType" minOccurs="1" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="CrashDumpMonitorsType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="CrashDumpItem" type="CrashDumpItemType" minOccurs="1" maxOccurs="1" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>


  <xs:complexType name="WindowsEventLogSubscriptionsType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="Subscription" type="WindowsEventLogSubscriptionType" minOccurs="1" maxOccurs="unbounded" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="AutoLoggersType">
    <xs:sequence>
      <xs:element name="AutoLogger" type="AutoLoggerType" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="ContainersType">
    <xs:sequence>
      <xs:element name="Container" type="ContainerType" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="DerivedEventsType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="DerivedEvent" type="DerivedEventType" minOccurs="1" maxOccurs="unbounded" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="DataMiningEventsType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="DataMiningEvent" type="DataMiningEventType" minOccurs="1" maxOccurs="unbounded" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="DeclarationsType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="Declaration" type="DeclarationType" minOccurs="1" maxOccurs="unbounded" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="DeclarationType">
    <xs:complexContent>
      <xs:extension base="EventWithNameType" />
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="PlugInType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="Event" type="EventWithNameType" minOccurs="1" maxOccurs="unbounded"/>
          <xs:element name="Body" type="xs:string" minOccurs="1" maxOccurs="1"/>
        </xs:sequence>
        <xs:attribute name="module" type="xs:string" use="required" />
        <xs:attribute name="runAsExtension" type="xs:boolean" use="optional" default="false" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="PluginsType">
    <xs:sequence>
      <xs:element name="PlugIn" type="PlugInType" minOccurs="1" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="SchemaColumnType">
    <xs:attribute name="name" type="ValidColumnNameType" use="required" />
    <xs:attribute name="type" type="MonTypeType" use="required" />
  </xs:complexType>

  <xs:simpleType name="TSColumnKindType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="PartitionKey"/>
      <xs:enumeration value="RowKey"/>
      <xs:enumeration value="Custom"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:complexType name="SchemaType">
    <xs:sequence>
      <xs:element name="Column" type="SchemaColumnType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
    <xs:attribute name="name" type="xs:string" use="required" />
    <xs:attribute name="tsColumnKind" type="TSColumnKindType" use="required" />
    <xs:attribute name="isTSColumnSuffix" type="xs:boolean" use="required" />
    <xs:attribute name="useFakeTS" type="xs:boolean" use="optional" default="false" />
  </xs:complexType>

  <xs:complexType name="SchemasType">
    <xs:sequence>
      <xs:element name="Schema" type="SchemaType" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Linux only -->
  <xs:complexType name="MdsdSchemasType">
    <xs:sequence>
      <xs:element name="Schema" type="MdsdSchemaType" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Linux only -->
  <xs:complexType name="MdsdSchemaType">
    <xs:sequence>
      <xs:element name="Column" type="MdsdSchemaColumnType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
    <xs:attribute name="name" type="xs:string" use="required" />
  </xs:complexType>
  <!-- Linux only -->
  <xs:complexType name="MdsdSchemaColumnType">
    <xs:attribute name="name" type="ValidColumnNameType" use="required" />
    <xs:attribute name="mdstype" type="MonTypeType" use="required" />
    <xs:attribute name="type" type="xs:string" use="required" />
  </xs:complexType>
  <!-- Linux only -->
  <xs:complexType name="MdsdSourcesType">
    <xs:sequence>
      <xs:element name="Source" type="MdsdSourceType" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Linux only -->
  <xs:complexType name="MdsdSourceType">
    <xs:attribute name="name" type="xs:string" use="required" />
    <xs:attribute name="schema" type="xs:string" use="optional" />
    <xs:attribute name="dynamic_schema" type="xs:boolean" use="optional" />
  </xs:complexType>
  <!-- Linux only -->
  <xs:complexType name="MdsdOMIQueriesType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="OMIQuery" type="MdsdOmiQueryType" minOccurs="1" maxOccurs="unbounded" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Linux only -->
  <xs:complexType name="MdsdOmiQueryType">
    <xs:complexContent>
      <xs:extension base="EventWithNameType">
        <xs:attribute name="sampleRateInSeconds" type="xs:unsignedInt" use="required" />
        <xs:attribute name="omiNamespace" type="xs:string" use="required"/>
        <xs:attribute name="cqlQuery" type="xs:string" use="required"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Linux only -->
  <xs:complexType name="MdsdEventsType">
      <xs:sequence>
        <xs:element name="MdsdEventSource" type="MdsdEventSourceType" minOccurs="1" maxOccurs="unbounded" />
      </xs:sequence>
  </xs:complexType>
  <!-- Linux only -->
  <xs:complexType name="MdsdEventSourceType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="RouteEvent" type="MdsdRouteEventType" minOccurs="1" maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="source" type="xs:string" use="required"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Linux only -->
  <xs:complexType name="MdsdRouteEventType">
    <xs:complexContent>
      <xs:extension base="EventWithNameType">
        <xs:sequence>
          <xs:element name="Filter" type="MdsdRouteEventFilterType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Linux only -->
  <xs:complexType name="MdsdRouteEventFilterType">
    <xs:attribute name="name" type="xs:string" use="required" />
    <xs:attribute name="op" type="xs:string" use="required" />
    <xs:attribute name="value" type="xs:unsignedByte" use="required" />
  </xs:complexType>

  <xs:complexType name="ExternalEventType">
    <xs:complexContent>
      <xs:extension base="EventWithNameType">
        <xs:attribute name="schema" type="xs:string" use="optional" />
        <xs:attribute name="timestampColName" type="xs:string" use="optional" />
        <xs:attribute name="partitionKeyLoadSpreadIndexMax" type="xs:int" use="optional" default="-1" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="ExternalEventsType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="Schemas" type="SchemasType" minOccurs="0" maxOccurs="1" />
          <xs:element name="Event" type="ExternalEventType" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="ExternalStreamingEventType">
    <xs:complexContent>
      <xs:extension base="ExternalEventType">
        <xs:attribute name="publisher" type="xs:string" use="optional"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="ExternalStreamingEventsType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="ExternalStreamingEvent" type="ExternalStreamingEventType" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="StreamingAnnotationBaseType" abstract="true">
    <!-- Initially annotation will be put as xml in the content. Later on when schema for specific annotations is defined.
         We will remove this element.

         usePublisherId - If this is set to true MA will use a publisher ID to tie one MA instance to one EH partition. Any event
                          that shared the event hub this is enabled on gets the publisher ID property set.
    -->
    <xs:sequence>
      <xs:element name="Content" type="xs:string" />
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="EventPublisherType">
    <xs:sequence>
      <xs:element name="Content" type="xs:string" />
      <xs:element name="Key" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
    <xs:attribute name="retryTimeout" type="xs:duration" use="optional" default="PT0M" />
    <xs:attribute name="usePublisherId" type="xs:boolean" use="optional" default="false" />
  </xs:complexType>

  <xs:complexType name="CosmosAnnotationType">
    <xs:complexContent>
      <xs:extension base="StreamingAnnotationBaseType">
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="ErrorAnnotationType">
    <xs:complexContent>
      <xs:extension base="StreamingAnnotationBaseType">
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="IndexingAnnotationType">
    <xs:complexContent>
      <xs:extension base="StreamingAnnotationBaseType">
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="ScrubbingAnnotationType">
    <xs:complexContent>
      <xs:extension base="StreamingAnnotationBaseType">
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="DistributedTracingAnnotationType">
    <xs:complexContent>
        <xs:extension base="StreamingAnnotationBaseType">
        </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="OnBehalfAnnotationType">
    <xs:complexContent>
        <xs:extension base="StreamingAnnotationBaseType">
            <xs:attribute name="directMode" type="xs:boolean" use="optional" default="false" />
        </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="DGrepAnnotationType">
    <xs:complexContent>
      <xs:extension base="StreamingAnnotationBaseType">
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="LogAnalyticsAnnotationType">
    <xs:complexContent>
      <xs:extension base="StreamingAnnotationBaseType">
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

   <!-- Selective indexing annotation is for users to enable indexing on DGrep for better performance in
       DGrep search -->
  <xs:complexType name="SelectiveIndexingAnnotationType">
    <xs:complexContent>
      <xs:extension base="StreamingAnnotationBaseType">
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <!-- Gdpr annotation is for users to control GDPR scoping of the events -->
  <xs:complexType name="GdprAnnotationType">
    <xs:complexContent>
      <xs:extension base="StreamingAnnotationBaseType">
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  
  <!-- OMS annotation is for users to send their local events to OMS workspaces -->
  <xs:complexType name="OMSAnnotationType">
    <xs:complexContent>
      <xs:extension base="StreamingAnnotationBaseType">
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <!-- Streaming annotations consisting of various annotations like cosmos, error, scrubbing, indexing
       which will be applied to events matching the regular expression -->
 <xs:complexType name="EventStreamingAnnotationType">
    <xs:all>
      <xs:element name="Cosmos" type="CosmosAnnotationType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="Error" type="ErrorAnnotationType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="Indexing" type="IndexingAnnotationType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="Scrubbing" type="ScrubbingAnnotationType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="DistributedTracing" type="DistributedTracingAnnotationType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="OnBehalf" type="OnBehalfAnnotationType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="DGrep" type="DGrepAnnotationType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="LogAnalytics" type="LogAnalyticsAnnotationType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="EventPublisher" type="EventPublisherType" minOccurs="0" maxOccurs="1"/>
	    <xs:element name="SelectiveIndexing" type="SelectiveIndexingAnnotationType" minOccurs="0" maxOccurs="1"/>
	    <xs:element name="Gdpr" type="GdprAnnotationType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="OMS" type="OMSAnnotationType" minOccurs="0" maxOccurs="1"/>
    </xs:all>
   <!-- name is treated as regular expression -->
   <xs:attribute name="name" type="xs:string" use="required"/>
  </xs:complexType>

  <!-- List of streaming annotations applied to the events in this configuration file -->
  <xs:complexType name="EventStreamingAnnotationsType">
      <xs:sequence>
        <xs:element name="EventStreamingAnnotation" type="EventStreamingAnnotationType" maxOccurs="unbounded"/>
      </xs:sequence>
  </xs:complexType>

  <xs:complexType name="ExtensionType">
    <xs:sequence>
      <xs:element name="CommandLine" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="AlternativeExtensionLocation" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="Body" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="ResourceUsage" type="ResourceRestrictionsType" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
    <xs:attribute name="extensionName" type="xs:string" use="required" />
    <xs:attribute name="startup" type="ExtensionStartupType" use="optional" default="OnMaStart"/>
    <xs:attribute name="idleTimeInMinutes" type="xs:unsignedInt" use="optional" default="15" />
    <xs:attribute name="logConsoleOutput" type="xs:boolean" use="optional" default="false" />
    <xs:attribute name="private" type="xs:boolean" use="optional" default="false" />
  </xs:complexType>

  <xs:complexType name="ExtensionsType">
    <xs:sequence>
      <xs:element name="Extension" type="ExtensionType" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="IFxEventType">
    <xs:complexContent>
      <xs:extension base="EventWithNameType">
        <xs:attribute name="id" type="xs:string" use="required" />
        <xs:attribute name="samplingRate" type="xs:double" use="optional" />
        <xs:attribute name="samplingMethod" type="IfxSamplingMethodType" use="optional" />
        <xs:attribute name="maxLevelSampled" type="IfxTracingLevelType" use="optional" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="IFxEventsType">
    <xs:complexContent>
      <xs:extension base="EventBaseType">
        <xs:sequence>
          <xs:element name="AdditionalHeaderFields" type="AdditionalHeaderFieldsType" minOccurs="0" maxOccurs="1" />
          <xs:element name="RemoveHeaderFields" type="AdditionalHeaderFieldsType" minOccurs="0" maxOccurs="1" />
          <xs:element name="Event" type="IFxEventType" minOccurs="1" maxOccurs="unbounded" />
        </xs:sequence>
        <xs:attribute name="guid" type="EtwProviderGuidType" use="optional" />
        <xs:attribute name="providerHeartbeats" type="xs:boolean" use="optional" default="true"/>
        <xs:attribute name="sessionName" type="xs:string" use="optional"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="ContainerListenerType">
    <xs:sequence>
      <xs:element name="CounterSets" type="CounterSetsType"   minOccurs="0" maxOccurs="1" />
      <xs:element name="EtwProviders" type="EtwProvidersType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="IfxEvents" type="IFxEventsType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="EnvelopeSchema" type="EnvelopeSchemaType" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
    <xs:attribute name="tcpPort" type="xs:string" use="optional"/>
    <xs:attribute name="dockerNetwork" type="xs:string" use="optional"/>
    <xs:attribute name="listenerIp" type="xs:string" use="optional"/>
  </xs:complexType>

  <!-- It will default to false for now, but after MDM is prod ready we can change the default to true. -->
  <xs:complexType name="MultidimensionalMetricsType">
    <xs:attribute name="enabled" type="xs:boolean" use="optional" default="false" />
  </xs:complexType>

  <xs:complexType name="EventsType">
    <xs:sequence>
      <xs:element name="EventDeclarations" type="DeclarationsType" minOccurs="0" maxOccurs="1" />
      <xs:element name="HeartBeats" type="HeartBeatsType" minOccurs="0" maxOccurs="1" />
      <xs:element name="OMI" type="MdsdOMIQueriesType" minOccurs="0" maxOccurs="unbounded" /> <!-- Linux only -->
      <xs:element name="MdsdEvents" type="MdsdEventsType" minOccurs="0" maxOccurs="1" /> <!-- Linux only -->
      <xs:element name="ExternalEvents" type="ExternalEventsType" minOccurs="0" maxOccurs="1" />
      <xs:element name="ExternalStreamingEvents" type="ExternalStreamingEventsType" minOccurs="0" maxOccurs="1" />
      <xs:element name="DynamicEvents" type="DynamicEventsType"  minOccurs="0" maxOccurs="1" />
      <xs:element name="CounterSets" type="CounterSetsType"   minOccurs="0" maxOccurs="1" />
      <xs:element name="EtwProviders" type="EtwProvidersType" minOccurs="0" maxOccurs="1" />
      <xs:element name="DataMiningEvents" type="DataMiningEventsType" minOccurs="0" maxOccurs="1" />
      <xs:element name="FileMonitors" type="FileMonitorsType" minOccurs="0" maxOccurs="1" />
      <xs:element name="WindowsEventLogSubscriptions" type="WindowsEventLogSubscriptionsType" minOccurs="0" maxOccurs="1" />
      <xs:element name="CrashDumpMonitor" type="CrashDumpMonitorsType" minOccurs="0" maxOccurs="1" />
      <xs:element name="IisLogSubscriptions" type="IisLogSubscriptionsType" minOccurs="0" maxOccurs="1" />
      <xs:element name="TextLogSubscriptions" type="TextLogSubscriptionsType" minOccurs="0" maxOccurs="1" />
      <xs:element name="IfxEvents" type="IFxEventsType" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="OneDSProviders" type="OneDSProvidersType" minOccurs="0" maxOccurs="1" />
      <xs:element name="ContainerListener" type="ContainerListenerType" minOccurs="0" maxOccurs="1" />
      <xs:element name="MultidimensionalMetrics" type="MultidimensionalMetricsType" minOccurs="0" maxOccurs="1" />
      <xs:element name="Plugins" type="PluginsType" minOccurs="0" maxOccurs="1" />
      <xs:element name="DerivedEvents" type="DerivedEventsType" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="Extensions" type="ExtensionsType" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>

  <!-- Secondary indexing could also be specified using a seperate specification that match event names
       based on a regular expression -->
  <xs:complexType name="EventIndexAnnotationType">
    <!-- name is treated as regular expression -->
    <xs:attribute name="name" type="xs:string" use="required" />
    <!-- comma seperated column names -->
    <xs:attribute name="additionalIndexes" type="xs:string" use="required" />
  </xs:complexType>

  <xs:complexType name="EventIndexAnnotationsType">
    <xs:sequence>
      <xs:element name="EventIndexAnnotation" type="EventIndexAnnotationType" minOccurs="1" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:simpleType name="NamespaceType">
    <xs:restriction base="xs:string">
      <xs:pattern value="([a-zA-Z][a-zA-Z0-9]*)*" />
    </xs:restriction>
  </xs:simpleType>

  <xs:complexType name="EnvelopeSchemaFieldType">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="name" type="xs:string" use="required" />
        <xs:attribute name="envariable" type="xs:string" use="optional" /> <!-- Linux only -->
        <xs:attribute name="useComputerName" type="xs:string" use="optional" /> <!-- Linux only -->
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>

  <xs:complexType name="EnvelopeSchemaExtensionType">
    <xs:sequence>
      <xs:element name="Field" type="EnvelopeSchemaFieldType" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
    <xs:attribute name="name" type="xs:string" use="required" />
  </xs:complexType>

  <xs:complexType name="EnvelopeSchemaType">
    <xs:sequence>
      <xs:element name="Field" type="EnvelopeSchemaFieldType" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="Extension" type="EnvelopeSchemaExtensionType" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>

  <xs:element name="MonitoringManagement">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="Imports" type="ImportsType" minOccurs="0" maxOccurs="1" />
        <xs:element name="Accounts" type="AccountsType" minOccurs="0" maxOccurs="1" />
        <xs:element name="Management" type="ManagementType" minOccurs="0" maxOccurs="1" />
        <xs:element name="Schemas" type="MdsdSchemasType" minOccurs="0" maxOccurs="1" /> <!-- Linux only -->
        <xs:element name="Sources" type="MdsdSourcesType" minOccurs="0" maxOccurs="1" /> <!-- Linux only -->
        <xs:element name="Events" type="EventsType" minOccurs="0" maxOccurs="1" />
        <xs:element name="EventIndexAnnotations" type="EventIndexAnnotationsType" minOccurs="0" maxOccurs="1" />
        <xs:element name="AutoLoggers" type="AutoLoggersType" minOccurs="0" maxOccurs="1" />
        <xs:element name="Containers" type="ContainersType" minOccurs="0" maxOccurs="1" />
        <xs:element name="EventStreamingAnnotations" type="EventStreamingAnnotationsType" minOccurs="0" maxOccurs="1" />
        <xs:element name="EnvelopeSchema" type="EnvelopeSchemaType" minOccurs="0" maxOccurs="1" />
      </xs:sequence>
      <xs:attribute name="version" type="xs:string" use="required" />
      <xs:attribute name="namespace" type="NamespaceType" use="optional" />
      <xs:attribute name="eventVersion" type="xs:unsignedInt" use="optional" />
      <xs:attribute name="timestamp" type="xs:dateTime" use="required" />
    </xs:complexType>
  </xs:element>

</xs:schema>
