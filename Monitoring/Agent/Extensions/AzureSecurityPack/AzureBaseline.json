{"Id": "{7e27cc78-4b24-4c02-bd6c-0c615778bbf8}", "OriginalId": "{f5620dc8-c1e6-42fb-8fea-a6fb46a5f759}", "SchemaVersion": "2", "Name": "AzureWinBaselineInternal", "Version": "3", "Rules": [{"RuleId": "{f520c2de-12a8-4e86-867d-b76c596a1cb8}", "AZID": "AZ-WIN-00099", "Name": "Audit Application Generated", "Description": "This subcategory reports when applications attempt to generate audit events by using the Windows auditing application programming interfaces (APIs). Events for this subcategory include:\n– 4665: An attempt was made to create an application client context. \n– 4666: An application attempted an operation:  \n– 4667: An application client context was deleted. \n– 4668: An application was initialized. \nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9222-69AE-11D9-BED3-************}", "ExpectedValue": "Success and Failure", "RemediateValue": "Success and Failure", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Object Access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{cdea6f27-cd50-4fc3-a64b-cb71a6ca72d6}", "AZID": "AZ-WIN-00001", "Name": "Audit Central Policy Staging", "Description": "", "Severity": "Optional", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9246-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Object Access"], "Filter": ["OSVersion = [WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{79358cc2-cc09-48c5-9564-ff14bdd5897b}", "AZID": "AZ-WIN-00091", "Name": "Audit Certification Services", "Description": "This subcategory reports when Certification Services operations are performed. Events for this subcategory include:\n– 4868: The certificate manager denied a pending certificate request. \n– 4869: Certificate Services received a resubmitted certificate request. \n– 4870: Certificate Services revoked a certificate. \n– 4871: Certificate Services received a request to publish the certificate revocation list (CRL).\n– 4872: Certificate Services published the certificate revocation list (CRL). \n– 4873: A certificate request extension changed. \n– 4874: One or more certificate request attributes changed. \n– 4875: Certificate Services received a request to shut down. \n– 4876: Certificate Services backup started. \n– 4877: Certificate Services backup completed. \n– 4878: Certificate Services restore started. \n– 4879: Certificate Services restore completed. \n– 4880: Certificate Services started. \n– 4881: Certificate Services stopped. \n– 4882 : The security permissions for Certificate Services changed. \n– 4883: Certificate Services retrieved an archived key. \n– 4884: Certificate Services imported a certificate into its database. \n– 4885: The audit filter for Certificate Services changed. \n– 4886: Certificate Services received a certificate request. \n– 4887: Certificate Services approved a certificate request and issued a certificate. \n– 4888: Certificate Services denied a certificate request. \n– 4889: Certificate Services set the status of a certificate request to pending. \n– 4890: The certificate manager settings for Certificate Services changed. \n– 4891: A configuration entry changed in Certificate Services. \n– 4892: A property of Certificate Services changed. \n– 4893: Certificate Services archived a key. \n– 4894: Certificate Services imported and archived a key. \n– 4895: Certificate Services published the CA certificate to Active Directory Domain Services.\n– 4896: One or more rows have been deleted from the certificate database. \n– 4897: Role separation enabled:  \n– 4898: Certificate Services loaded a template. \n– 4899: A Certificate Services template was updated.\n– 4900: Certificate Services template security was updated.\n– 5120: OCSP Responder Service Started.\n– 5121: OCSP Responder Service Stopped.\n– 5122: A Configuration entry changed in the OCSP Responder Service.\n– 5123: A configuration entry changed in the OCSP Responder Service.\n– 5124: A security setting was updated on OCSP Responder Service.\n– 5125: A request was submitted to OCSP Responder Service.\n– 5126: Signing Certificate was automatically updated by the OCSP Responder Service.\n– 5127: The OCSP Revocation Provider successfully updated the revocation information.\nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9221-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Object Access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{5fb4de76-c10f-43e8-bfc6-a95fb8cc5c88}", "AZID": "AZ-WIN-00092", "Name": "Audit Detailed Directory Service Replication", "Description": "This subcategory reports detailed information about the information replicating between domain controllers. These events can be very high in volume. Events for this subcategory include:\n– 4928: An Active Directory replica source naming context was established. \n– 4929 : An Active Directory replica source naming context was removed. \n– 4930 : An Active Directory replica source naming context was modified. \n– 4931 : An Active Directory replica destination naming context was modified. \n– 4934 : Attributes of an Active Directory object were replicated. \n– 4935 : Replication failure begins. \n– 4936 : Replication failure ends. \n– 4937 : A lingering object was removed from a replica. \nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE923E-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-DS Access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{04212107-de72-4eb7-a427-1876b5604a98}", "AZID": "AZ-WIN-00100", "Name": "Audit Detailed File Share", "Description": "This policy setting allows you to audit attempts to access files and folders on a shared folder. The Detailed File Share setting logs an event every time a file or folder is accessed, whereas the File Share setting only records one event for any connection established between a client and file share.  Detailed File Share audit events include detailed information about the permissions or other criteria used to grant or deny access.\n\nIf you configure this policy setting, an audit event is generated when an attempt is made to access a file or folder on a share. The administrator can specify whether to audit only successes, only failures, or both successes and failures.\n\nNote: There are no system access control lists (SACLs) for shared folders. If this policy setting is enabled, access to all shared files and folders on the system is audited.\n\nVolume: High on a file server or domain controller because of SYSVOL network access required by Group Policy.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9244-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Object Access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{e40b12a5-37b5-44be-9824-97f4456924d9}", "AZID": "AZ-WIN-00093", "Name": "Audit Directory Service Replication", "Description": "This subcategory reports when replication between two domain controllers begins and ends. Events for this subcategory include:\n– 4932: Synchronization of a replica of an Active Directory naming context has begun.\n– 4933: Synchronization of a replica of an Active Directory naming context has ended.\nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE923D-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-DS Access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{71f81231-8d02-4460-b03e-41c675040ca8}", "AZID": "AZ-WIN-00101", "Name": "Audit DPAPI Activity", "Description": "This subcategory reports encrypt or decrypt calls into the data protections application interface (DPAPI). DPAPI is used to protect secret information such as stored password and key information. Events for this subcategory include:\n– 4692: Backup of data protection master key was attempted. \n– 4693: Recovery of data protection master key was attempted. \n– 4694: Protection of auditable protected data was attempted. \n– 4695: Unprotection of auditable protected data was attempted. \nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE922D-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Detailed Tracking"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{1926dc04-79ea-4a6e-9e35-892c27876bf5}", "AZID": "AZ-WIN-00102", "Name": "Audit File Share", "Description": "This subcategory reports when a file share is accessed. By itself, this policy setting will not cause auditing of any events. It determines whether to audit the event of a user who accesses a file share object that has a specified system access control list (SACL), effectively enabling auditing to take place.\nA SACL is comprised of access control entries (ACEs). Each ACE contains three pieces of information:\n• The security principal (user, computer, or group) to be audited.\n• The specific access type to be audited, called an access mask.\n• A flag to indicate whether to audit failed access events, successful access events, or both.\nIf you configure the Audit object access setting to Success, an audit entry is generated each time that a user successfully accesses an object with a specified SACL. If you configure this policy setting to Failure, an audit entry is generated each time that a user fails in an attempt to access an object with a specified SACL.\nOrganizations should define only the actions they want enabled when they configure SACLs. For example, you might want to enable the Write and Append Data auditing setting on executable files to track when they are changed or replaced, because computer viruses, worms, and Trojan horses typically target executable files. Similarly, you might want to track when sensitive documents are accessed or changed.\nEvents for this subcategory include:\n– 5140: A network share object was accessed.\nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9224-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Object Access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{f4dd7fad-643f-4f28-a4ec-bc7ea03bc7f2}", "AZID": "AZ-WIN-00103", "Name": "Audit File System", "Description": "This subcategory reports when file system objects are accessed. Only file system objects with SACLs cause audit events to be generated, and only when they are accessed in a manner matching their SACL. By itself, this policy setting will not cause auditing of any events. It determines whether to audit the event of a user who accesses a file system object that has a specified system access control list (SACL), effectively enabling auditing to take place.\nA SACL is comprised of access control entries (ACEs). Each ACE contains three pieces of information:\n• The security principal (user, computer, or group) to be audited.\n• The specific access type to be audited, called an access mask.\n• A flag to indicate whether to audit failed access events, successful access events, or both.\nIf you configure the Audit object access setting to Success, an audit entry is generated each time that a user successfully accesses an object with a specified SACL. If you configure this policy setting to Failure, an audit entry is generated each time that a user fails in an attempt to access an object with a specified SACL.\nOrganizations should define only the actions they want enabled when they configure SACLs. For example, you might want to enable the Write and Append Data auditing setting on executable files to track when they are changed or replaced, because computer viruses, worms, and Trojan horses typically target executable files. Similarly, you might want to track when sensitive documents are accessed or changed.\nEvents for this subcategory include:\n– 4664: An attempt was made to create a hard link. \n– 4985: The state of a transaction has changed. \n– 5051: A file was virtualized. \nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE921D-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Object Access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{10191095-e6fa-47d3-aa8e-d58b36241f2a}", "AZID": "AZ-WIN-00104", "Name": "Audit Filtering Platform Connection", "Description": "This subcategory reports when connections are allowed or blocked by WFP. These events can be high in volume. Events for this subcategory include:\n– 5031: The Windows Firewall Service blocked an application from accepting incoming connections on the network. \n– 5154: The Windows Filtering Platform has permitted an application or service to listen on a port for incoming connections. \n– 5155 : The Windows Filtering Platform has blocked an application or service from listening on a port for incoming connections. \n– 5156: The Windows Filtering Platform has allowed a connection. \n– 5157: The Windows Filtering Platform has blocked a connection. \n– 5158: The Windows Filtering Platform has permitted a bind to a local port. \n– 5159: The Windows Filtering Platform has blocked a bind to a local port. \nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9226-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Object Access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{3dab02aa-72b8-46b6-844f-695554bf59dd}", "AZID": "AZ-WIN-00105", "Name": "Audit Filtering Platform Packet Drop", "Description": "This subcategory reports when packets are dropped by Windows Filtering Platform (WFP). These events can be very high in volume. Events for this subcategory include:\n– 5152: The Windows Filtering Platform blocked a packet. \n– 5153: A more restrictive Windows Filtering Platform filter has blocked a packet. \nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9225-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Object Access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{260bbf9d-209d-4589-bf81-0f5329a36b87}", "AZID": "AZ-WIN-00106", "Name": "Audit Filtering Platform Policy Change", "Description": "This subcategory reports the addition and removal of objects from WFP, including startup filters. These events can be very high in volume. Events for this subcategory include:\n– 4709: IPsec Services was started. \n– 4710: IPsec Services was disabled. \n– 4711: May contain any one of the following:  \n• PAStore Engine applied locally cached copy of Active Directory storage IPsec policy on the computer.\n• PAStore Engine applied Active Directory storage IPsec policy on the computer.\n• PAStore Engine applied local registry storage IPsec policy on the computer.\n• PAStore Engine failed to apply locally cached copy of Active Directory storage IPsec policy on the computer.\n• PAStore Engine failed to apply Active Directory storage IPsec policy on the computer.\n• PAStore Engine failed to apply local registry storage IPsec policy on the computer.\n• PAStore Engine failed to apply some rules of the active IPsec policy on the computer.\n• PAStore Engine failed to load directory storage IPsec policy on the computer.\n• PAStore Engine loaded directory storage IPsec policy on the computer.\n• PAStore Engine failed to load local storage IPsec policy on the computer.\n• PAStore Engine loaded local storage IPsec policy on the computer.\n• PAStore Engine polled for changes to the active IPsec policy and detected no changes.\n– 4712: IPsec Services encountered a potentially serious failure. \n– 5040: A change has been made to IPsec settings. An Authentication Set was added. \n– 5041: A change has been made to IPsec settings. An Authentication Set was modified. \n– 5042: A change has been made to IPsec settings. An Authentication Set was deleted. \n– 5043: A change has been made to IPsec settings. A Connection Security Rule was added. \n– 5044: A change has been made to IPsec settings. A Connection Security Rule was modified. \n– 5045: A change has been made to IPsec settings. A Connection Security Rule was deleted. \n– 5046: A change has been made to IPsec settings. A Crypto Set was added. \n– 5047: A change has been made to IPsec settings. A Crypto Set was modified. \n– 5048: A change has been made to IPsec settings. A Crypto Set was deleted. \n– 5440: The following callout was present when the Windows Filtering Platform Base Filtering Engine started. \n– 5441: The following filter was present when the Windows Filtering Platform Base Filtering Engine started. \n– 5442: The following provider was present when the Windows Filtering Platform Base Filtering Engine started. \n– 5443: The following provider context was present when the Windows Filtering Platform Base Filtering Engine started. \n– 5444 : The following sub-layer was present when the Windows Filtering Platform Base Filtering Engine started. \n– 5446: A Windows Filtering Platform callout has been changed. \n– 5448: A Windows Filtering Platform provider has been changed. \n– 5449: A Windows Filtering Platform provider context has been changed. \n– 5450: A Windows Filtering Platform sub-layer has been changed. \n– 5456: PAStore Engine applied Active Directory storage IPsec policy on the computer. \n– 5457: PAStore Engine failed to apply Active Directory storage IPsec policy on the computer. \n– 5458 : PAStore Engine applied locally cached copy of Active Directory storage IPsec policy on the computer. \n– 5459: PAStore Engine failed to apply locally cached copy of Active Directory storage IPsec policy on the computer. \n– 5460: PAStore Engine applied local registry storage IPsec policy on the computer. \n– 5461: PAStore Engine failed to apply local registry storage IPsec policy on the computer. \n– 5462: PAStore Engine failed to apply some rules of the active IPsec policy on the computer. Use the IP Security Monitor snap-in to diagnose the problem. \n– 5463: PAStore Engine polled for changes to the active IPsec policy and detected no changes. \n– 5464: PAStore Engine polled for changes to the active IPsec policy, detected changes, and applied them to IPsec Services. \n– 5465: PAStore Engine received a control for forced reloading of IPsec policy and processed the control successfully. \n– 5466: PAStore Engine polled for changes to the Active Directory IPsec policy, determined that Active Directory cannot be reached, and will use the cached copy of the Active Directory IPsec policy instead. Any changes made to the Active Directory IPsec policy since the last poll could not be applied. \n– 5467: PAStore Engine polled for changes to the Active Directory IPsec policy, determined that Active Directory can be reached, and found no changes to the policy. The cached copy of the Active Directory IPsec policy is no longer being used. \n– 5468: PAStore Engine polled for changes to the Active Directory IPsec policy, determined that Active Directory can be reached, found changes to the policy, and applied those changes. The cached copy of the Active Directory IPsec policy is no longer being used. \n– 5471: PAStore Engine loaded local storage IPsec policy on the computer. \n– 5472: PAStore Engine failed to load local storage IPsec policy on the computer. \n– 5473: PAStore Engine loaded directory storage IPsec policy on the computer. \n– 5474: PAStore Engine failed to load directory storage IPsec policy on the computer. \n– 5477: PAStore Engine failed to add quick mode filter. \nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9233-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Policy Change"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{58221c40-e279-43e2-b297-e48a0904cbb6}", "AZID": "AZ-WIN-00107", "Name": "Audit Handle Manipulation", "Description": "This subcategory reports when a handle to an object is opened or closed. Only objects with SACLs cause these events to be generated, and only if the attempted handle operation matches the SACL. Handle Manipulation events are only generated for object types where the corresponding Object Access subcategory is enabled, for example File System or Registry. Events for this subcategory include:\n– 4656: A handle to an object was requested. \n– 4658: The handle to an object was closed. \n– 4690: An attempt was made to duplicate a handle to an object. \nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9223-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Object Access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{42d93501-c545-4a29-8cb0-74d314dbd955}", "AZID": "AZ-WIN-00108", "Name": "Audit IPsec Extended Mode", "Description": "This subcategory reports the results of AuthIP during Extended Mode negotiations. Events for this subcategory include:\n– 4978: During Extended Mode negotiation, IPsec received an invalid negotiation packet. If this problem persists, it could indicate a network issue or an attempt to modify or replay this negotiation. \n– 4979: IPsec Main Mode and Extended Mode security associations were established.\n– 4980: IPsec Main Mode and Extended Mode security associations were established.\n– 4981: IPsec Main Mode and Extended Mode security associations were established.\n– 4982: IPsec Main Mode and Extended Mode security associations were established.\n– 4983: An IPsec Extended Mode negotiation failed. The corresponding Main Mode security association has been deleted. \n– 4984: An IPsec Extended Mode negotiation failed. The corresponding Main Mode security association has been deleted.\nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE921A-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Logon/Logoff"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{aea21999-40da-4417-85c1-fc8b0614152a}", "AZID": "AZ-WIN-00003", "Name": "Audit IPsec Main Mode", "Description": "", "Severity": "Optional", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9218-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Logon/Logoff"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{18c9af48-eee0-41fd-81f9-b0a004291c22}", "AZID": "AZ-WIN-00109", "Name": "Audit IPsec Quick Mode", "Description": "This subcategory reports the results of IKE protocol and AuthIP during Quick Mode negotiations.\n– 4654: An IPsec Quick Mode negotiation failed. Events for this subcategory include:\n– 4977: During Quick Mode negotiation, IPsec received an invalid negotiation packet. If this problem persists, it could indicate a network issue or an attempt to modify or replay this negotiation.\n– 5451: An IPsec Quick Mode security association was established.\n– 5452: An IPsec Quick Mode security association ended.\nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9219-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Logon/Logoff"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{4d909207-5803-4047-9b53-1d5029d8fe4e}", "AZID": "AZ-WIN-00004", "Name": "Audit Kerberos Authentication Service", "Description": "This subcategory reports events generated by the Kerberos Authentication Server. These events occur on the computer that is authoritative for the credentials. Events for this subcategory include:\n– 4768: A Kerberos authentication ticket (TGT) was requested.\n– 4771: Kerberos pre-authentication failed.\n– 4772: A Kerberos authentication ticket request failed.\nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9242-69AE-11D9-BED3-************}", "ExpectedValue": "Success and Failure", "RemediateValue": "Success and Failure", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Account <PERSON><PERSON>"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{ef24ca92-6c0b-4444-802d-532983a3a050}", "AZID": "AZ-WIN-00005", "Name": "Audit Kerberos Service Ticket Operations", "Description": "This subcategory reports generated by Kerberos ticket request processes on the domain controller that is authoritative for the domain account. Events for this subcategory include:\n– 4769: A Kerberos service ticket was requested.\n– 4770: A Kerberos service ticket was renewed.\n– 4773: A Kerberos service ticket request failed.\nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9240-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Account <PERSON><PERSON>"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{a30f6d7d-f3dc-442c-8a1f-921123c6250c}", "AZID": "AZ-WIN-00119", "Name": "Bypass traverse checking", "Description": "This policy setting allows users who do not have the Traverse Folder access permission to pass through folders when they browse an object path in the NTFS file system or the registry. This user right does not allow users to list the contents of a folder.\n\nWhen configuring a user right in the SCM enter a comma delimited list of accounts. Accounts can be either local or located in Active Directory, they can be groups, users, or computers.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeChangeNotifyPrivilege", "ExpectedValue": "Administrators, Authenticated Users, Backup Operators, Local Service, Network Service", "RemediateValue": "Administrators, Authenticated Users, Backup Operators, Local Service, Network Service", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "LESSOREQUAL", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{3f2d92c2-5850-4f2d-b245-f5089aa975dd}", "AZID": "CCE-35818-4", "Name": "Configure 'Access this computer from the network'", "Description": "This policy setting allows other users on the network to connect to the computer and is required by various network protocols that include Server Message Block (SMB)based protocols, NetBIOS, Common Internet File System (CIFS), and Component Object Model Plus (COM+). - **Level 1 - Domain Controller.** The recommended state for this setting is: `Administrators, Authenticated Users, ENTERPRISE DOMAIN CONTROLLERS`. - **Level 1 - Member Server.** The recommended state for this setting is: `Administrators, Authenticated Users`.", "Severity": "Critical", "Vulnerability": "Users who can connect from their computer to the network can access resources on target computers for which they have permission. For example, the Access this computer from the network user right is required for users to connect to shared printers and folders. If this user right is assigned to the Everyone group, then anyone in the group will be able to read the files in those shared folders. However, this situation is unlikely for new installations of Windows Server 2003 with Service Pack 1 (SP1), because the default share and NTFS permissions in Windows Server 2003 do not include the Everyone group. This vulnerability may have a higher level of risk for computers that you upgrade from Windows NT 4.0 or Windows 2000, because the default permissions for these operating systems are not as restrictive as the default permissions in Windows Server 2003.", "Impact": "If you remove the Access this computer from the network user right on domain controllers for all users, no one will be able to log on to the domain or use network resources. If you remove this user right on member servers, users will not be able to connect to those servers through the network. Successful negotiation of IPsec connections requires that the initiating machine has this right, therefore it is recommended that it is assigned to the Users group. If you have installed optional components such as ASP.NET or Internet Information Services (IIS), you may need to assign this user right to additional accounts that are required by those components. It is important to verify that authorized users are assigned this user right for the computers they need to access the network.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeNetworkLogonRight", "ExpectedValue": "Administrators, Authenticated Users", "RemediateValue": "Administrators, Authenticated Users", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "LESSOREQUAL", "Categories": ["ASR: Network", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{574f0e8d-83ca-4a46-a6cd-8dd062ab32dd}", "AZID": "CCE-37072-6", "Name": "Configure 'Allow log on through Remote Desktop Services'", "Description": "This policy setting determines which users or groups have the right to log on as a Terminal Services client. Remote desktop users require this user right. If your organization uses Remote Assistance as part of its help desk strategy, create a group and assign it this user right through Group Policy. If the help desk in your organization does not use Remote Assistance, assign this user right only to the Administrators group or use the restricted groups feature to ensure that no user accounts are part of the Remote Desktop Users group. Restrict this user right to the Administrators group, and possibly the Remote Desktop Users group, to prevent unwanted users from gaining access to computers on your network by means of the Remote Assistance feature. - **Level 1 - Domain Controller.** The recommended state for this setting is: `Administrators`. - **Level 1 - Member Server.** The recommended state for this setting is: `Administrators, Remote Desktop Users`. **Note:** A Member Server that holds the _Remote Desktop Services_ Role with _Remote Desktop Connection Broker_ Role Service will require a special exception to this recommendation, to allow the `Authenticated Users` group to be granted this user right. **Note #2:** The above lists are to be treated as whitelists, which implies that the above principals need not be present for assessment of this recommendation to pass.", "Severity": "Critical", "Vulnerability": "Any account with the Allow log on through Terminal Services user right can log on to the remote console of the computer. If you do not restrict this user right to legitimate users who need to log on to the console of the computer, unauthorized users could download and run malicious software to elevate their privileges.", "Impact": "Removal of the Allow log on through Terminal Services user right from other groups or membership changes in these default groups could limit the abilities of users who perform specific administrative roles in your environment. You should confirm that delegated activities will not be adversely affected.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeRemoteInteractiveLogonRight", "ExpectedValue": "Administrators, Remote Desktop Users", "RemediateValue": "Administrators, Remote Desktop Users", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: RDP", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{e97bdde4-ccec-42e6-a17f-7993cb03a0d6}", "AZID": "CCE-35823-4", "Name": "Configure 'Create symbolic links'", "Description": "This policy setting determines which users can create symbolic links. In Windows Vista, existing NTFS file system objects, such as files and folders, can be accessed by referring to a new kind of file system object called a symbolic link. A symbolic link is a pointer (much like a shortcut or .lnk file) to another file system object, which can be a file, folder, shortcut or another symbolic link. The difference between a shortcut and a symbolic link is that a shortcut only works from within the Windows shell. To other programs and applications, shortcuts are just another file, whereas with symbolic links, the concept of a shortcut is implemented as a feature of the NTFS file system. Symbolic links can potentially expose security vulnerabilities in applications that are not designed to use them. For this reason, the privilege for creating symbolic links should only be assigned to trusted users. By default, only Administrators can create symbolic links. - **Level 1 - Domain Controller.** The recommended state for this setting is: `Administrators`. - **Level 1 - Member Server.** The recommended state for this setting is: `Administrators` and (when the _Hyper-V_ Role is installed) `NT VIRTUAL MACHINEVirtual Machines`.", "Severity": "Critical", "Vulnerability": "Users who have the Create Symbolic Links user right could inadvertently or maliciously expose your system to symbolic link attacks. Symbolic link attacks can be used to change the permissions on a file, to corrupt data, to destroy data, or as a Denial of Service attack.", "Impact": "In most cases there will be no impact because this is the default configuration, however, on Windows Servers with the Hyper-V server role installed this user right should also be granted to the special group \"Virtual Machines\" otherwise you will not be able to create new virtual machines.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeCreateSymbolicLinkPrivilege", "ExpectedValue": "Administrators, NT VIRTUAL MACHINE\\Virtual Machines", "RemediateValue": "Administrators, NT VIRTUAL MACHINE\\Virtual Machines", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "LESSOREQUAL", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{fbe348fd-0402-4e31-8482-66ae9ae82ea2}", "AZID": "CCE-37954-5", "Name": "Configure 'Deny access to this computer from the network'", "Description": "This policy setting prohibits users from connecting to a computer from across the network, which would allow users to access and potentially modify data remotely. In high security environments, there should be no need for remote users to access data on a computer. Instead, file sharing should be accomplished through the use of network servers. - **Level 1 - Domain Controller.** The recommended state for this setting is to include: ``Guests, Local account``. - **Level 1 - Member Server.** The recommended state for this setting is to include: `Guests, Local account and member of Administrators group`. **Caution:** Configuring a standalone (non-domain-joined) server as described above may result in an inability to remotely administer the server. **Note:** Configuring a member server or standalone server as described above may adversely affect applications that create a local service account and place it in the Administrators group - in which case you must either convert the application to use a domain-hosted service account, or remove `Local account and member of Administrators` group from this User Right Assignment. Using a domain-hosted service account is strongly preferred over making an exception to this rule, where possible.", "Severity": "Critical", "Vulnerability": "Users who can log on to the computer over the network can enumerate lists of account names, group names, and shared resources. Users with permission to access shared folders and files can connect over the network and possibly view or modify data.", "Impact": "If you configure the Deny access to this computer from the network user right for other groups, you could limit the abilities of users who are assigned to specific administrative roles in your environment. You should verify that delegated tasks will not be negatively affected.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeDenyNetworkLogonRight", "ExpectedValue": "Guests, Local Account", "RemediateValue": "Guests, Local Account", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "LESSOREQUAL", "Categories": ["ASR: Network", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{045634b9-61c9-414f-ad91-74dcfee9c076}", "AZID": "CCE-36860-5", "Name": "Configure 'Enable computer and user accounts to be trusted for delegation'", "Description": "This policy setting allows users to change the Trusted for Delegation setting on a computer object in Active Directory. Abuse of this privilege could allow unauthorized users to impersonate other users on the network. - **Level 1 - Domain Controller.** The recommended state for this setting is: `Administrators`. - **Level 1 - Member Server.** The recommended state for this setting is: `No One`.", "Severity": "Critical", "Vulnerability": "Misuse of the Enable computer and user accounts to be trusted for delegation user right could allow unauthorized users to impersonate other users on the network. An attacker could exploit this privilege to gain access to network resources and make it difficult to determine what has happened after a security incident.", "Impact": "None - this is the default configuration.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeEnableDelegationPrivilege", "ExpectedValue": "No One", "RemediateValue": "No One", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{5d72b92f-e6b0-4898-b24a-49241c3a70a4}", "AZID": "CCE-35906-7", "Name": "Configure 'Manage auditing and security log'", "Description": "This policy setting determines which users can change the auditing options for files and directories and clear the Security log. For environments running Microsoft Exchange Server, the `Exchange Servers` group must possess this privilege on Domain Controllers to properly function. Given this, DCs granting the `Exchange Servers` group this privilege do conform with this benchmark. If the environment does not use Microsoft Exchange Server, then this privilege should be limited to only `Administrators` on DCs. - **Level 1 - Domain Controller.** The recommended state for this setting is: `Administrators` and (when Exchange is running in the environment) `Exchange Servers`. - **Level 1 - Member Server.** The recommended state for this setting is: `Administrators`.", "Severity": "Critical", "Vulnerability": "The ability to manage the Security event log is a powerful user right and it should be closely guarded. Anyone with this user right can clear the Security log to erase important evidence of unauthorized activity.", "Impact": "None - this is the default configuration.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeSecurityPrivilege", "ExpectedValue": "Administrators", "RemediateValue": "Administrators", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{3e6b8e00-6ffa-43a8-8cc0-8e34d2e55bf5}", "AZID": "CCE-37194-8", "Name": "Configure 'Network access: Remotely accessible registry paths'", "Description": "This policy setting determines which registry paths will be accessible over the network, regardless of the users or groups listed in the access control list (ACL) of the `winreg` registry key. **Note:** This setting does not exist in Windows XP. There was a setting with that name in Windows XP, but it is called \"Network access: Remotely accessible registry paths and sub-paths\" in Windows Server 2003, Windows Vista, and Windows Server 2008. **Note #2:** When you configure this setting you specify a list of one or more objects. The delimiter used when entering the list is a line feed or carriage return, that is, type the first object on the list, press the Enter button, type the next object, press Enter again, etc. The setting value is stored as a comma-delimited list in group policy security templates. It is also rendered as a comma-delimited list in Group Policy Editor's display pane and the Resultant Set of Policy console. It is recorded in the registry as a line-feed delimited list in a REG-_MULTI-_SZ value. The recommended state for this setting is: ``` System-CurrentControlSet-Control-ProductOptions System-CurrentControlSet-Control-Server Applications Software-Microsoft-Windows NT-CurrentVersion ```", "Severity": "Critical", "Vulnerability": "The registry is a database that contains computer configuration information, and much of the information is sensitive. An attacker could use this information to facilitate unauthorized activities. To reduce the risk of such an attack, suitable ACLs are assigned throughout the registry to help protect it from access by unauthorized users.", "Impact": "None - this is the default configuration. However, if you remove the default registry paths from the list of accessible ones, remote management tools such as the Microsoft Baseline Security Analyzer and Microsoft Systems Management Server could fail, as they require remote access to the registry to properly monitor and manage computers. **Note:** If you want to allow remote access, you must also enable the Remote Registry service.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\SecurePipeServers\\Winreg\\AllowedExactPaths\\Machine", "ExpectedValue": "System\\CurrentControlSet\\Control\\ProductOptions|#|System\\CurrentControlSet\\Control\\Server Applications|#|Software\\Microsoft\\Windows NT\\CurrentVersion", "RemediateValue": "System\\CurrentControlSet\\Control\\ProductOptions|#|System\\CurrentControlSet\\Control\\Server Applications|#|Software\\Microsoft\\Windows NT\\CurrentVersion", "Remediate": "false", "ValueType": "REG_MULTI_SZ", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Network", "Local Policies-Network access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{43e5a1a0-3b79-4ba9-ab3d-7a5dd18b144a}", "AZID": "CCE-36347-3", "Name": "Configure 'Network access: Remotely accessible registry paths and sub-paths'", "Description": "This policy setting determines which registry paths and sub-paths will be accessible over the network, regardless of the users or groups listed in the access control list (ACL) of the `winreg` registry key. **Note:** In Windows XP this setting is called \"Network access: Remotely accessible registry paths,\" the setting with that same name in Windows Vista, Windows Server 2008, and Windows Server 2003 does not exist in Windows XP. **Note #2:** When you configure this setting you specify a list of one or more objects. The delimiter used when entering the list is a line feed or carriage return, that is, type the first object on the list, press the Enter button, type the next object, press Enter again, etc. The setting value is stored as a comma-delimited list in group policy security templates. It is also rendered as a comma-delimited list in Group Policy Editor's display pane and the Resultant Set of Policy console. It is recorded in the registry as a line-feed delimited list in a REG-_MULTI-_SZ value. The recommended state for this setting is: ``` System-CurrentControlSet-Control-Print-Printers System-CurrentControlSet-Services-Eventlog Software-Microsoft-OLAP Server Software-Microsoft-Windows NT-CurrentVersion-Print Software-Microsoft-Windows NT-CurrentVersion-Windows System-CurrentControlSet-Control-ContentIndex System-CurrentControlSet-Control-Terminal Server System-CurrentControlSet-Control-Terminal Server-UserConfig System-CurrentControlSet-Control-Terminal Server-DefaultUserConfiguration Software-Microsoft-Windows NT-CurrentVersion-Perflib System-CurrentControlSet-Services-SysmonLog ``` The recommended state for servers that hold the _Active Directory Certificate Services_ Role with _Certification Authority_ Role Service includes the above list and: ``` System-CurrentControlSet-Services-CertSvc ``` The recommended state for servers that have the _WINS Server_ Feature installed includes the above list and: ``` System-CurrentControlSet-Services-WINS ```", "Severity": "Critical", "Vulnerability": "The registry contains sensitive computer configuration information that could be used by an attacker to facilitate unauthorized activities. The fact that the default ACLs assigned throughout the registry are fairly restrictive and help to protect the registry from access by unauthorized users reduces the risk of such an attack.", "Impact": "None - this is the default configuration. However, if you remove the default registry paths from the list of accessible ones, remote management tools such as the Microsoft Baseline Security Analyzer and Microsoft Systems Management Server could fail, as they require remote access to the registry to properly monitor and manage computers. **Note:** If you want to allow remote access, you must also enable the Remote Registry service.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\SecurePipeServers\\Winreg\\AllowedPaths\\Machine", "ExpectedValue": "System\\CurrentControlSet\\Control\\Print\\Printers|#|System\\CurrentControlSet\\Services\\Eventlog|#|Software\\Microsoft\\OLAP Server|#|Software\\Microsoft\\Windows NT\\CurrentVersion\\Print|#|Software\\Microsoft\\Windows NT\\CurrentVersion\\Windows|#|System\\CurrentControlSet\\Control\\ContentIndex|#|System\\CurrentControlSet\\Control\\Terminal Server|#|System\\CurrentControlSet\\Control\\Terminal Server\\UserConfig|#|System\\CurrentControlSet\\Control\\Terminal Server\\DefaultUserConfiguration|#|Software\\Microsoft\\Windows NT\\CurrentVersion\\Perflib|#|System\\CurrentControlSet\\Services\\SysmonLog", "RemediateValue": "System\\CurrentControlSet\\Control\\Print\\Printers|#|System\\CurrentControlSet\\Services\\Eventlog|#|Software\\Microsoft\\OLAP Server|#|Software\\Microsoft\\Windows NT\\CurrentVersion\\Print|#|Software\\Microsoft\\Windows NT\\CurrentVersion\\Windows|#|System\\CurrentControlSet\\Control\\ContentIndex|#|System\\CurrentControlSet\\Control\\Terminal Server|#|System\\CurrentControlSet\\Control\\Terminal Server\\UserConfig|#|System\\CurrentControlSet\\Control\\Terminal Server\\DefaultUserConfiguration|#|Software\\Microsoft\\Windows NT\\CurrentVersion\\Perflib|#|System\\CurrentControlSet\\Services\\SysmonLog", "Remediate": "false", "ValueType": "REG_MULTI_SZ", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Network", "Local Policies-Network access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{ca2bde70-dd40-4cbb-823b-c377b0a1b5cb}", "AZID": "AZ-WIN-00120", "Name": "Devices: Allow undock without having to log on", "Description": "This policy setting determines whether a portable computer can be undocked if the user does not log on to the system. Enable this policy setting to eliminate a Logon requirement and allow use of an external hardware eject button to undock the computer. If you disable this policy setting, a user must log on and have been assigned the Remove computer from docking station user right to undock the computer.", "Severity": "Informational", "Vulnerability": "If this policy setting is enabled, anyone with physical access to portable computers in docking stations could remove them and possibly tamper with them. ", "Impact": "Users who have docked their computers will have to log on to the local console before they can undock their computers. For computers that do not have docking stations, this policy setting will have no impact.", "DataSourceType": "Registry", "DataSourceKey": "Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\UndockWithoutLogon", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Devices", "Local Policies-Devices"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{f3117bf3-e54a-496a-9976-74b1caca3105}", "AZID": "AZ-WIN-00121", "Name": "Disable 'Configure local setting override for reporting to Microsoft MAPS'", "Description": "This policy setting configures a local override for the configuration to join Microsoft MAPS. This setting can only be set by Group Policy.    If you enable this setting the local preference setting will take priority over Group Policy.    If you disable or do not configure this setting Group Policy will take priority over the local preference setting.", "Severity": "Important", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows Defender\\SpyNet\\LocalSettingOverrideSpynetReporting", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Antimalware", "Administrative Templates (Computer)-Windows Defender"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{16394616-4a4d-4416-9985-b8a3251eb70c}", "AZID": "AZ-WIN-00122", "Name": "Disable SMB v1 client (remove dependency on LanmanWorkstation)", "Description": "APPLIES ONLY TO: Windows 7 and Windows Servers 2008, 2008R2 and 2012 (NOT 2012R2):\n\nTo disable client-side processing of the SMBv1 protocol (recommended), do ALL of the following:\n* Set the SMBv1 client driver to \"Disable driver\" using the \"Configure SMB v1 client driver\" setting;\n* Enable this setting;\n* In the \"Configure LanmanWorkstation dependencies\" text box, enter the following three lines of text:\nBowser\nMRxSmb20\nNSI\n\nTo restore the default behavior for client-side SMBv1 protocol processing, do ALL of the following:\n* Set the SMBv1 client driver to \"Manual start\" using the \"Configure SMB v1 client driver\" setting;\n* Enable this setting;\n* In the \"Configure LanmanWorkstation dependencies\" text box, enter the following four lines of text:\nBowser\nMRxSmb10\nMRxSmb20\nNSI\n\nWARNING: DO NOT SELECT THE \"DISABLED\" RADIO BUTTON UNDER ANY CIRCUMSTANCES!\n\nChanges to this setting require a reboot to take effect.\n\nFor more information, see https:--support.microsoft.com-kb-2696547", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\LanmanWorkstation\\DependsOnService", "ExpectedValue": "Bowser|#|MRxSmb20|#|NSI", "RemediateValue": "Bowser|#|MRxSmb20|#|NSI", "Remediate": "false", "ValueType": "REG_MULTI_SZ", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: SMB", "Local Policies-Network access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{a002b800-92a4-45cb-bbee-76c91739ddff}", "AZID": "AZ-WIN-00123", "Name": "Disable SMB v1 server", "Description": "Disabling this setting disables server-side processing of the SMBv1 protocol. (Recommended.)\n\nEnabling this setting enables server-side processing of the SMBv1 protocol. (Default.)\n\nChanges to this setting require a reboot to take effect.\n\nFor more information, see https:--support.microsoft.com-kb-2696547", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\LanmanServer\\Parameters\\SMB1", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: SMB", "Local Policies-Network access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{843079e3-4803-4b52-8b36-c554c4623204}", "AZID": "AZ-WIN-00124", "Name": "Disable Windows Search Service", "Description": "This registry setting disables the Windows Search Service", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "System\\CurrentControlSet\\Services\\Wsearch\\Start", "ExpectedValue": "4", "RemediateValue": "4", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Search", "Administrative Templates (Computer)-Search"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{4054c4db-7927-4344-87b4-156c1d681598}", "AZID": "AZ-WIN-00125", "Name": "Enable 'Scan removable drives' by setting DisableRemovableDriveScanning (REG_DWORD) to 0", "Description": "This policy setting allows you to manage whether or not to scan for malicious software and unwanted software in the contents of removable drives such as USB flash drives when running a full scan.    If you enable this setting removable drives will be scanned during any type of scan.    If you disable or do not configure this setting removable drives will not be scanned during a full scan. Removable drives may still be scanned during quick scan and custom scan.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows Defender\\Scan\\DisableRemovableDriveScanning", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Antimalware", "Administrative Templates (Computer)-Windows Defender"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{eb4fff3b-5e49-4f9d-92b2-0f08fc65f9ce}", "AZID": "AZ-WIN-00126", "Name": "Enable 'Send file samples when further analysis is required' for 'Send Safe Samples'", "Description": "This policy setting configures behaviour of samples submission when opt-in for MAPS telemetry is set.        Possible options are:        (0x0) Always prompt        (0x1) Send safe samples automatically        (0x2) Never send        (0x3) Send all samples automatically    ", "Severity": "Important", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows Defender\\SpyNet\\SubmitSamplesConsent", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Antimalware", "Administrative Templates (Computer)-Windows Defender"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{a917e66c-e3e4-4a7b-8f72-e8163994aabc}", "AZID": "AZ-WIN-00127", "Name": "Enable 'Turn on behavior monitoring'", "Description": "This policy setting allows you to configure behavior monitoring.    If you enable or do not configure this setting behavior monitoring will be enabled.    If you disable this setting behavior monitoring will be disabled.", "Severity": "Important", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows Defender\\Real-Time Protection\\DisableBehaviorMonitoring", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Antimalware", "Administrative Templates (Computer)-Windows Defender"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{baffb3e2-a1b8-4805-a9c7-484a04b29c08}", "AZID": "AZ-WIN-00128", "Name": "Enable Windows Error Reporting (Policy Check)", "Description": "Custom Azure setting", "Severity": "Important", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Windows Error Reporting\\Disabled", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Administrative Templates (Computer)-Windows Error Reporting"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{d9180e11-4ace-40a5-af08-267675d98fc0}", "AZID": "AZ-WIN-00129", "Name": "Enable Windows Error Reporting (Registry Check)", "Description": "Custom Azure setting", "Severity": "Important", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\Windows Error Reporting\\Disabled", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Administrative Templates (Computer)-Windows Error Reporting"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{131ecdaf-4a45-44ef-8d8e-eb7f4acf2fa6}", "AZID": "CCE-37056-9", "Name": "Ensure 'Access Credential Manager as a trusted caller' is set to 'No One'", "Description": "This security setting is used by Credential Manager during Backup and Restore. No accounts should have this user right, as it is only assigned to Winlogon. Users' saved credentials might be compromised if this user right is assigned to other entities. The recommended state for this setting is: `No One`.", "Severity": "Important", "Vulnerability": "If an account is given this right the user of the account may create an application that calls into Credential Manager and is returned the credentials for another user.", "Impact": "None - this is the default configuration.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeTrustedCredManAccessPrivilege", "ExpectedValue": "No One", "RemediateValue": "No One", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{d3d9ac7b-8bcc-42e8-8752-29902eda04dd}", "AZID": "CCE-37432-2", "Name": "Ensure 'Accounts: Guest account status' is set to 'Disabled'", "Description": "This policy setting determines whether the Guest account is enabled or disabled. The Guest account allows unauthenticated network users to gain access to the system. The recommended state for this setting is: `Disabled`. **Note:** This setting will have no impact when applied to the domain controller organizational unit via group policy because domain controllers have no local account database. It can be configured at the domain level via group policy, similar to account lockout and password policy settings.", "Severity": "Critical", "Vulnerability": "The default Guest account allows unauthenticated network users to log on as <PERSON> with no password. These unauthorized users could access any resources that are accessible to the Guest account over the network. This capability means that any network shares with permissions that allow access to the Guest account, the Guests group, or the Everyone group will be accessible over the network, which could lead to the exposure or corruption of data.", "Impact": "All network users will need to authenticate before they can access shared resources. If you disable the Guest account and the Network Access: Sharing and Security Model option is set to Guest Only, network logons, such as those performed by the Microsoft Network Server (SMB Service), will fail. This policy setting should have little impact on most organizations because it is the default setting in Microsoft Windows 2000, Windows XP, and Windows Server™ 2003.", "DataSourceType": "Policy", "DataSourceKey": "[System Access]EnableGuestAccount", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "UINTEGER", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Local accounts", "Local Policies-Accounts"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{3715ec67-6cd4-49c0-8c82-27001a0e332b}", "AZID": "CCE-37615-2", "Name": "Ensure 'Accounts: Limit local account use of blank passwords to console logon only' is set to 'Enabled'", "Description": "This policy setting determines whether local accounts that are not password protected can be used to log on from locations other than the physical computer console. If you enable this policy setting, local accounts that have blank passwords will not be able to log on to the network from remote client computers. Such accounts will only be able to log on at the keyboard of the computer. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "Blank passwords are a serious threat to computer security and should be forbidden through both organizational policy and suitable technical measures. In fact, the default settings for Active Directory domains require complex passwords of at least seven characters. However, if users with the ability to create new accounts bypass your domain-based password policies, they could create accounts with blank passwords. For example, a user could build a stand-alone computer, create one or more accounts with blank passwords, and then join the computer to the domain. The local accounts with blank passwords would still function. Anyone who knows the name of one of these unprotected accounts could then use it to log on.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Lsa\\LimitBlankPasswordUse", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Local accounts", "Local Policies-Accounts"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{c7f8ee96-6b8e-47e8-80b1-2e0985edeafd}", "AZID": "CCE-36876-1", "Name": "Ensure 'Act as part of the operating system' is set to 'No One'", "Description": "This policy setting allows a process to assume the identity of any user and thus gain access to the resources that the user is authorized to access. The recommended state for this setting is: `No One`.", "Severity": "Critical", "Vulnerability": "The Act as part of the operating system user right is extremely powerful. Anyone with this user right can take complete control of the computer and erase evidence of their activities.", "Impact": "There should be little or no impact because the Act as part of the operating system user right is rarely needed by any accounts other than the Local System account.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeTcbPrivilege", "ExpectedValue": "No One", "RemediateValue": "No One", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{abb1bcab-f4da-4a9c-be63-7564a0bca7b8}", "AZID": "CCE-36254-1", "Name": "Ensure 'Allow Basic authentication' is set to 'Disabled'", "Description": "This policy setting allows you to manage whether the Windows Remote Management (WinRM) service accepts Basic authentication from a remote client. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "Basic authentication is less robust than other authentication methods available in WinRM because credentials including passwords are transmitted in plain text. An attacker who is able to capture packets on the network where WinRM is running may be able to determine the credentials used for accessing remote hosts via WinRM.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\WinRM\\Client\\AllowBasic", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: WinRM", "Administrative Templates (Computer)-WinRM Client"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{10afd846-02ae-44fc-9482-09e99ac65aeb}", "AZID": "AZ-WIN-00130", "Name": "Ensure 'Allow Cortana above lock screen' is set to 'Disabled'", "Description": "This policy setting determines whether or not the user can interact with <PERSON><PERSON><PERSON> using speech while the system is locked. The recommended state for this setting is: `Disabled`.", "Severity": "Important", "Vulnerability": "Access to any computer resource should not be allowed when the device is locked.", "Impact": "The system will need to be unlocked for the user to interact with <PERSON><PERSON><PERSON> using speech.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Windows Search\\AllowCortanaAboveLock", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Unnecessary software & services", "Administrative Templates (Computer)-Search"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{a44246b2-d2a5-4216-a300-075128a28da3}", "AZID": "AZ-WIN-00131", "Name": "Ensure 'Allow Cortana' is set to 'Disabled'", "Description": "This policy setting specifies whether Co<PERSON>na is allowed on the device. The recommended state for this setting is: `Disabled`.", "Severity": "Important", "Vulnerability": "If Cortana is enabled, sensitive information could be contained in search history and sent out to Microsoft.", "Impact": "Cortana will be turned off. Users will still be able to use search to find things on the device and on the Internet.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Windows Search\\AllowCortana", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Search", "Administrative Templates (Computer)-Search"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{484c747f-1418-4c27-a944-c3b1e1690b33}", "AZID": "CCE-38277-0", "Name": "Ensure 'Allow indexing of encrypted files' is set to 'Disabled'", "Description": "This policy setting controls whether encrypted items are allowed to be indexed. When this setting is changed, the index is rebuilt completely. Full volume encryption (such as BitLocker Drive Encryption or a non-Microsoft solution) must be used for the location of the index to maintain security for encrypted files. The recommended state for this setting is: `Disabled`.", "Severity": "Important", "Vulnerability": "Indexing and allowing users to search encrypted files could potentially reveal confidential data stored within the encrypted files.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Windows Search\\AllowIndexingEncryptedStoresOrItems", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Search", "Administrative Templates (Computer)-Search"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{11ca2201-2673-4f04-bad3-3265e1a53a5b}", "AZID": "AZ-WIN-00132", "Name": "Ensure 'Allow Input Personalization' is set to 'Disabled'", "Description": "This policy enables the automatic learning component of input personalization that includes speech, inking, and typing. Automatic learning enables the collection of speech and handwriting patterns, typing history, contacts, and recent calendar information. It is required for the use of Cortana. Some of this collected information may be stored on the user's OneDrive, in the case of inking and typing; some of the information will be uploaded to Microsoft to personalize speech. The recommended state for this setting is: `Disabled`.", "Severity": "Important", "Vulnerability": "If this setting is Enabled sensitive information could be stored in the cloud or sent to Microsoft.", "Impact": "Automatic learning of speech, inking, and typing stops and users cannot change its value via PC Settings.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\InputPersonalization\\AllowInputPersonalization", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Unnecessary software & services", "Administrative Templates (Computer)-Regional and Language Options"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{e65f6c30-070b-47d6-8f50-904dae410bf4}", "AZID": "CCE-38354-7", "Name": "Ensure 'Allow Microsoft accounts to be optional' is set to 'Enabled'", "Description": "This policy setting lets you control whether Microsoft accounts are optional for Windows Store apps that require an account to sign in. This policy only affects Windows Store apps that support it. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "Enabling this setting allows an organization to use their enterprise user accounts instead of using their Microsoft accounts when accessing Windows store apps. This provides the organization with greater control over relevant credentials. Microsoft accounts cannot be centrally managed and as such enterprise credential security policies cannot be applied to them, which could put any information accessed by using Microsoft accounts at risk.", "Impact": "Windows Store apps that typically require a Microsoft account to sign in will allow users to sign in with an enterprise account instead.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\MSAOptional", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["App Security", "Administrative Templates (Computer)-App runtime"], "Filter": ["OSVersion = [WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{e7dbbeb4-eac8-49c5-b8c0-bea6f8fbd37c}", "AZID": "CCE-35899-4", "Name": "Ensure 'Allow remote access to the Plug and Play interface' is set to 'Disabled'", "Description": "This policy setting allows you to allow or deny remote access to the Plug and Play interface. The recommended state for this setting is: `Disabled.`", "Severity": "Important", "Vulnerability": "Allowing remote access to the Plug and Play interface could give hackers another attack vector to a system.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\DeviceInstall\\Settings\\AllowRemoteRPC", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Devices", "Administrative Templates (Computer)-Device Installation"], "Filter": ["OSVersion = [WS2008, WS2008R2]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{fb48056d-0b04-405e-9e7d-286d55f6ec61}", "AZID": "AZ-WIN-00133", "Name": "Ensure 'Allow search and Cortana to use location' is set to 'Disabled'", "Description": "This policy setting specifies whether search and Cortana can provide location aware search and Cortana results. The recommended state for this setting is: `Disabled`.", "Severity": "Important", "Vulnerability": "In an Enterprise having Cortana and Search having access to location is unnecessary. Organizations may not want this information shared out.", "Impact": "Search and Cortana will not have access to location information.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Windows Search\\AllowSearchToUseLocation", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Search", "Administrative Templates (Computer)-Search"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{14afe28a-6199-49ff-9789-dabb89ed714e}", "AZID": "AZ-WIN-00134", "Name": "Ensure 'Allow Telemetry' is set to 'Enabled: 0 - Security [Enterprise Only]'", "Description": "This policy setting determines the amount of diagnostic and usage data reported to Microsoft. A value of 0 will send minimal data to Microsoft. This data includes Malicious Software Removal Tool (MSRT) & Windows Defender data, if enabled, and telemetry client settings. Setting a value of 0 applies to enterprise, EDU, IoT and server devices only. Setting a value of 0 for other devices is equivalent to choosing a value of 1. A value of 1 sends only a basic amount of diagnostic and usage data. Note that setting values of 0 or 1 will degrade certain experiences on the device. A value of 2 sends enhanced diagnostic and usage data. A value of 3 sends the same data as a value of 2, plus additional diagnostics data, including the files and content that may have caused the problem. Windows 10 telemetry settings apply to the Windows operating system and some first party apps. This setting does not apply to third party apps running on Windows 10. The recommended state for this setting is: `Enabled: 0 - Security [Enterprise Only]`. **Note:** If the \"Allow Telemetry\" setting is configured to \"0 - Security [Enterprise Only]\", then the options in Windows Update to defer upgrades and updates will have no effect.", "Severity": "Important", "Vulnerability": "Sending any data to a 3rd party vendor is a security concern and should only be done on an as needed basis.", "Impact": "Note that setting values of 0 or 1 will degrade certain experiences on the device.", "DataSourceType": "Registry", "DataSourceKey": "Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\DataCollection\\AllowTelemetry", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Administrative Templates (Computer)-Data Collection and Preview Builds"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{2785f384-9901-4c9d-8dca-8ff2b5068fde}", "AZID": "CCE-38223-4", "Name": "Ensure 'Allow unencrypted traffic' is set to 'Disabled'", "Description": "This policy setting allows you to manage whether the Windows Remote Management (WinRM) service sends and receives unencrypted messages over the network. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "Encrypting WinRM network traffic reduces the risk of an attacker viewing or modifying WinRM messages as they transit the network.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\WinRM\\Client\\AllowUnencryptedTraffic", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: WinRM", "Administrative Templates (Computer)-WinRM Client"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{5d42c180-4350-49ec-9bb6-e51e1258022c}", "AZID": "CCE-36400-0", "Name": "Ensure 'Allow user control over installs' is set to 'Disabled'", "Description": "Permits users to change installation options that typically are available only to system administrators. The security features of Windows Installer prevent users from changing installation options typically reserved for system administrators, such as specifying the directory to which files are installed. If Windows Installer detects that an installation package has permitted the user to change a protected option, it stops the installation and displays a message. These security features operate only when the installation program is running in a privileged security context in which it has access to directories denied to the user. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "In an Enterprise environment, only IT staff with administrative rights should be installing or changing software on a system. Allowing users the ability can risk unapproved software from being installed our removed from a system which could cause the system to become vulnerable.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\\EnableUserControl", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["User Rights", "Administrative Templates (Computer)-Windows Installer"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{2eda113a-0fb7-446c-856a-83e010d36671}", "AZID": "CCE-37490-0", "Name": "Ensure 'Always install with elevated privileges' is set to 'Disabled'", "Description": "This setting controls whether or not Windows Installer should use system permissions when it installs any program on the system. **Note:** This setting appears both in the Computer Configuration and User Configuration folders. To make this setting effective, you must enable the setting in both folders. **Caution:** If enabled, skilled users can take advantage of the permissions this setting grants to change their privileges and gain permanent access to restricted files and folders. Note that the User Configuration version of this setting is not guaranteed to be secure. The recommended state for this setting is: `Disabled`.", "Severity": "Important", "Vulnerability": "Users with limited privileges can exploit this feature by creating a Windows Installer installation package that creates a new local account that belongs to the local built-in Administrators group, adds their current account to the local built-in Administrators group, installs malicious software, or performs other unauthorized activities.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\\AlwaysInstallElevated", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["User Rights", "Administrative Templates (Computer)-Windows Installer"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{534b62bb-1403-43f9-abfc-42dfe8c0c21b}", "AZID": "CCE-37929-7", "Name": "Ensure 'Always prompt for password upon connection' is set to 'Enabled'", "Description": "This policy setting specifies whether Terminal Services always prompts the client computer for a password upon connection. You can use this policy setting to enforce a password prompt for users who log on to Terminal Services, even if they already provided the password in the Remote Desktop Connection client. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "Users have the option to store both their username and password when they create a new Remote Desktop connection shortcut. If the server that runs Terminal Services allows users who have used this feature to log on to the server but not enter their password, then it is possible that an attacker who has gained physical access to the user's computer could connect to a Terminal Server through the Remote Desktop connection shortcut, even though they may not know the user's password.", "Impact": "Users cannot automatically log on to Terminal Services by supplying their passwords in the Remote Desktop Connection client. They will be prompted for a password to log on.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows NT\\Terminal Services\\fPromptForPassword", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: RDP", "Administrative Templates (Computer)-Security"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{8dc6d72f-c9d2-4417-a728-df4d770fcb11}", "AZID": "CCE-37348-0", "Name": "Ensure 'Always use classic logon' is set to 'Enabled'", "Description": "This policy setting forces the user to log on to the computer using the classic logon screen. By default, a workgroup is set to use the simple logon screen. This setting only works when the computer is not on a domain. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "Explicitly requiring a user to enter their username and password is ideal and a requirement when utilizing the classic logon method. This setting is primarily important because it does not permit the use of a simple logon screen with user accounts presented. **Note:** Systems joined to a domain typically are not impacted by this recommendation as username, password, and domain are required for system access. However, this setting is important for standalone systems.", "Impact": "The classic logon screen is always presented to the user at logon, rather than the simple logon screen.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\LogonType", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["Identity Management", "Administrative Templates (Computer)-Logon"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{0d2b0df9-7b74-4617-84a8-af51d5814c68}", "AZID": "CCE-37775-4", "Name": "Ensure 'Application: Control Event Log behavior when the log file reaches its maximum size' is set to 'Disabled'", "Description": "This policy setting controls Event Log behavior when the log file reaches its maximum size. The recommended state for this setting is: `Disabled`. **Note:** Old events may or may not be retained according to the \"Backup log automatically when full\" policy setting.", "Severity": "Critical", "Vulnerability": "If new events are not recorded it may be difficult or impossible to determine the root cause of system problems or the unauthorized activities of malicious users.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\EventLog\\Application\\Retention", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_SZ", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["Logging & Auditing", "Administrative Templates (Computer)-Application"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{360082b5-036a-460c-bf68-b0a64efca2a9}", "AZID": "CCE-37948-7", "Name": "Ensure 'Application: Specify the maximum log file size (KB)' is set to 'Enabled: 32,768 or greater'", "Description": "This policy setting specifies the maximum size of the log file in kilobytes. The maximum log file size can be configured between 1 megabyte (1,024 kilobytes) and 2 terabytes (2,147,483,647 kilobytes) in kilobyte increments. The recommended state for this setting is: `Enabled: 32,768 or greater`.", "Severity": "Critical", "Vulnerability": "If events are not recorded it may be difficult or impossible to determine the root cause of system problems or the unauthorized activities of malicious users.", "Impact": "When event logs fill to capacity, they will stop recording information unless the retention method for each is set so that the computer will overwrite the oldest entries with the most recent ones. To mitigate the risk of loss of recent data, you can configure the retention method so that older events are overwritten as needed. The consequence of this configuration is that older events will be removed from the logs. Attackers can take advantage of such a configuration, because they can generate a large number of extraneous events to overwrite any evidence of their attack. These risks can be somewhat reduced if you automate the archival and backup of event log data. Ideally, all specifically monitored events should be sent to a server that uses Microsoft System Center Operations Manager (SCOM) or some other automated monitoring tool. Such a configuration is particularly important because an attacker who successfully compromises a server could clear the Security log. If all events are sent to a monitoring server, then you will be able to gather forensic information about the attacker's activities.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\EventLog\\Application\\MaxSize", "ExpectedValue": "32768", "RemediateValue": "32768", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Administrative Templates (Computer)-Application"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{359995b3-30ce-451d-b41b-81cd04d22a09}", "AZID": "AZ-WIN-00110", "Name": "Audit Kernel Object", "Description": "This subcategory reports when kernel objects such as processes and mutexes are accessed. Only kernel objects with SACLs cause audit events to be generated, and only when they are accessed in a manner matching their SACL. Typically kernel objects are only given SACLs if the AuditBaseObjects or AuditBaseDirectories auditing options are enabled.\nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE921F-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Object Access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{b0ae9428-da18-4196-98f3-a7a8dfd4d9e9}", "AZID": "AZ-WIN-00111", "Name": "Audit MPSSVC Rule-Level Policy Change", "Description": "This subcategory reports changes in policy rules used by the Microsoft Protection Service (MPSSVC.exe). This service is used by Windows Firewall and by Microsoft OneCare. Events for this subcategory include:\n– 4944: The following policy was active when the Windows Firewall started. \n– 4945: A rule was listed when the Windows Firewall started. \n– 4946: A change has been made to Windows Firewall exception list. A rule was added. \n– 4947: A change has been made to Windows Firewall exception list. A rule was modified. \n– 4948: A change has been made to Windows Firewall exception list. A rule was deleted. \n– 4949: Windows Firewall settings were restored to the default values. \n– 4950: A Windows Firewall setting has changed. \n– 4951: A rule has been ignored because its major version number was not recognized by Windows Firewall. \n– 4952 : Parts of a rule have been ignored because its minor version number was not recognized by Windows Firewall. The other parts of the rule will be enforced. \n– 4953: A rule has been ignored by Windows Firewall because it could not parse the rule. \n– 4954: Windows Firewall Group Policy settings have changed. The new settings have been applied. \n– 4956: Windows Firewall has changed the active profile. \n– 4957: Windows Firewall did not apply the following rule:  \n– 4958: Windows Firewall did not apply the following rule because the rule referred to items not configured on this computer:  \nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9232-69AE-11D9-BED3-************}", "ExpectedValue": "Success and Failure", "RemediateValue": "Success and Failure", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Policy Change"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{02b98148-d822-4f2e-9dca-6fb5f5472a69}", "AZID": "AZ-WIN-00096", "Name": "Audit Network Policy Server", "Description": "This subcategory reports events generated by RADIUS (IAS) and Network Access Protection (NAP) user access requests. These requests can be <PERSON>, <PERSON><PERSON>, <PERSON>ard, <PERSON>uarantine, <PERSON>, and <PERSON><PERSON>. Auditing this setting will result in a medium or high volume of records on NPS and IAS servers. Events for this subcategory include:\nNote All the events in the Network Policy Server subcategory are available only in Windows Vista Service Pack 1 and in Windows Server 2008. \n– 6272: Network Policy Server granted access to a user.\n– 6273: Network Policy Server denied access to a user.\n– 6274: Network Policy Server discarded the request for a user.\n– 6275: Network Policy Server discarded the accounting request for a user.\n– 6276: Network Policy Server quarantined a user.\n– 6277: Network Policy Server granted access to a user but put it on probation because the host did not meet the defined health policy. \n– 6278: Network Policy Server granted full access to a user because the host met the defined health policy. \n– 6279: Network Policy Server locked the user account due to repeated failed authentication attempts.\n– 6280: Network Policy Server unlocked the user account. \n– 8191: Network Policy Server unlocked the user account.\nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9243-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Logon/Logoff"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{32308a39-87da-4a19-a068-43e94912b0be}", "AZID": "AZ-WIN-00112", "Name": "Audit Non Sensitive Privilege Use", "Description": "This subcategory reports when a user account or service uses a non-sensitive privilege. A non-sensitive privilege includes the following user rights:  Access Credential Manager as a trusted caller, Access this computer from the network, Add workstations to domain, Adjust memory quotas for a process, Allow log on locally, Allow log on through Terminal Services, Bypass traverse checking, Change the system time, Create a pagefile, Create global objects, Create permanent shared objects, Create symbolic links, <PERSON><PERSON> access this computer from the network, <PERSON><PERSON> log on as a batch job, <PERSON><PERSON> log on as a service, <PERSON><PERSON> log on locally, <PERSON><PERSON> log on through Terminal Services, Force shutdown from a remote system, Increase a process working set, Increase scheduling priority, Lock pages in memory, Log on as a batch job, Log on as a service, Modify an object label, Perform volume maintenance tasks, Profile single process, Profile system performance, Remove computer from docking station, Shut down the system, and Synchronize directory service data. Auditing this subcategory will create a very high volume of events. Events for this subcategory include:\n– 4672: Special privileges assigned to new logon.\n– 4673: A privileged service was called.\n– 4674: An operation was attempted on a privileged object.\nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9229-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Privilege Use"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{aaa7f83d-b883-4852-97f5-01ec1f126e71}", "AZID": "AZ-WIN-00006", "Name": "Audit Other Account Logon Events", "Description": "This subcategory reports the events that occur in response to credentials submitted for a user account logon request that do not relate to credential validation or Kerberos tickets. These events occur on the computer that is authoritative for the credentials. For domain accounts, the domain controller is authoritative, whereas for local accounts, the local computer is authoritative. In domain environments, most of the Account Logon events occur in the Security log of the domain controllers that are authoritative for the domain accounts. However, these events can occur on other computers in the organization when local accounts are used to log on.\nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9241-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "0", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Account <PERSON><PERSON>"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{4dcdc7e0-4f16-42bc-8c94-cd52d24af381}", "AZID": "AZ-WIN-00113", "Name": "Audit Other Object Access Events", "Description": "This subcategory reports other object access-related events such as Task Scheduler jobs and COM+ objects. Events for this subcategory include:\n– 4671: An application attempted to access a blocked ordinal through the TBS. \n– 4691: Indirect access to an object was requested. \n– 4698: A scheduled task was created. \n– 4699 : A scheduled task was deleted. \n– 4700 : A scheduled task was enabled. \n– 4701: A scheduled task was disabled. \n– 4702 : A scheduled task was updated.\n– 5888: An object in the COM+ Catalog was modified. \n– 5889: An object was deleted from the COM+ Catalog. \n– 5890: An object was added to the COM+ Catalog. \nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9227-69AE-11D9-BED3-************}", "ExpectedValue": "Success and Failure", "RemediateValue": "Success and Failure", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Object Access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{88b87546-b3c8-434f-9cc6-01e117033296}", "AZID": "AZ-WIN-00114", "Name": "Audit Other Policy Change Events", "Description": "This subcategory reports other types of security policy changes such as configuration of the Trusted Platform Module (TPM) or cryptographic providers. Events for this subcategory include:\n– 4909: The local policy settings for the TBS were changed. \n– 4910: The group policy settings for the TBS were changed. \n– 5063: A cryptographic provider operation was attempted. \n– 5064: A cryptographic context operation was attempted. \n– 5065: A cryptographic context modification was attempted. \n– 5066: A cryptographic function operation was attempted. \n– 5067: A cryptographic function modification was attempted. \n– 5068: A cryptographic function provider operation was attempted. \n– 5069: A cryptographic function property operation was attempted. \n– 5070: A cryptographic function property modification was attempted. \n– 5447: A Windows Filtering Platform filter has been changed. \n– 6144: Security policy in the group policy objects has been applied successfully. \n– 6145: One or more errors occurred while processing security policy in the group policy objects. \nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9234-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Policy Change"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{5c134ea6-aece-4b49-9be9-e924e3c9b155}", "AZID": "AZ-WIN-00097", "Name": "Audit Other Privilege Use Events", "Description": "This subcategory reports when a user account or service uses a sensitive privilege. A sensitive privilege includes the following user rights:  Act as part of the operating system, Back up files and directories, Create a token object, Debug programs, Enable computer and user accounts to be trusted for delegation, Generate security audits, Impersonate a client after authentication, Load and unload device drivers, Manage auditing and security log, Modify firmware environment values, Replace a process-level token, Restore files and directories, and Take ownership of files or other objects. Auditing this subcategory will create a high volume of events. Events for this subcategory include:\n– 4672: Special privileges assigned to new logon.\n– 4673: A privileged service was called.\n– 4674: An operation was attempted on a privileged object.\nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE922A-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Privilege Use"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{5C550429-EE09-41D6-8864-F0A8C1DB6BA8}", "AZID": "CCE-36925-6", "Name": "Ensure 'Include command line in process creation events' is set to 'Disabled'", "Description": "This policy setting determines what information is logged in security audit events when a new process has been created. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "When this policy setting is enabled, any user who has read access to the security events can read the command-line arguments for any successfully created process. Command-line arguments may contain sensitive or private information such as passwords or user data.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\Audit\\ProcessCreationIncludeCmdLine_Enabled", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Administrative Templates (Computer)-Audit Process Creation"], "Filter": ["OSVersion = [WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{e96fe119-2038-4713-96df-efb52824f64e}", "AZID": "AZ-WIN-00008", "Name": "Audit Policy: Logon-Logoff: IPsec Main Mode", "Description": "This subcategory reports the results of Internet Key Exchange (IKE) protocol and Authenticated Internet Protocol (AuthIP) during Main Mode negotiations. Events for this subcategory include:\n– 4646: IKE DoS-prevention mode started.\n– 4650: An IPsec Main Mode security association was established. Extended Mode was not enabled. Certificate authentication was not used.\n– 4651: An IPsec Main Mode security association was established. Extended Mode was not enabled. A certificate was used for authentication.\n– 4652: An IPsec Main Mode negotiation failed.\n– 4653: An IPsec Main Mode negotiation failed.\n– 4655: An IPsec Main Mode security association ended.\n– 4976: During Main Mode negotiation, IPsec received an invalid negotiation packet. If this problem persists, it could indicate a network issue or an attempt to modify or replay this negotiation.\n– 5049: An IPsec Security Association was deleted.\n– 5453: An IPsec negotiation with a remote computer failed because the IKE and AuthIP IPsec Keying Modules (IKEEXT) service is not started.\nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9218-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Logon/Logoff"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Exclude]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{1cb3410b-b302-4d9e-974e-84f7c9a8d4a0}", "AZID": "AZ-WIN-00009", "Name": "Audit Process Termination", "Description": "", "Severity": "Important", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE922C-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Detailed Tracking"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{ed2d22bc-ac83-437b-8a55-76adf3423c62}", "AZID": "AZ-WIN-00115", "Name": "Audit Registry", "Description": "This subcategory reports when registry objects are accessed. Only registry objects with SACLs cause audit events to be generated, and only when they are accessed in a manner matching their SACL. By itself, this policy setting will not cause auditing of any events. It determines whether to audit the event of a user who accesses a registry object that has a specified system access control list (SACL), effectively enabling auditing to take place.\nA SACL is comprised of access control entries (ACEs). Each ACE contains three pieces of information:\n• The security principal (user, computer, or group) to be audited.\n• The specific access type to be audited, called an access mask.\n• A flag to indicate whether to audit failed access events, successful access events, or both.\nIf you configure the Audit object access setting to Success, an audit entry is generated each time that a user successfully accesses an object with a specified SACL. If you configure this policy setting to Failure, an audit entry is generated each time that a user fails in an attempt to access an object with a specified SACL.\nOrganizations should define only the actions they want enabled when they configure SACLs. For example, you might want to enable the Write and Append Data auditing setting on executable files to track when they are changed or replaced, because computer viruses, worms, and Trojan horses typically target executable files. Similarly, you might want to track when sensitive documents are accessed or changed.\nEvents for this subcategory include:\n– 4657 : A registry value was modified.\n– 5039: A registry key was virtualized.\nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE921E-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Object Access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{5c283d0e-f8f9-4277-8995-8a6311cec06f}", "AZID": "AZ-WIN-00116", "Name": "Audit RPC Events", "Description": "This subcategory reports remote procedure call (RPC) connection events. Events for this subcategory include:\n– 5712: A Remote Procedure Call (RPC) was attempted. \nRefer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE922E-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Detailed Tracking"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{d13fcf70-d3c8-494a-9eb9-70a88b3b33cd}", "AZID": "AZ-WIN-00117", "Name": "Audit SAM", "Description": "This subcategory reports when SAM objects are accessed. Refer to the Microsoft Knowledgebase article “Description of security events in Windows Vista and in Windows Server 2008” for the most recent information about this setting: http:--support.microsoft.com-default.aspx-kb-947226.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9220-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Object Access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{59a84e67-7d1f-4c50-8b00-4a7bc72ea273}", "AZID": "AZ-WIN-00118", "Name": "Audit User/Device <PERSON>laims", "Description": "", "Severity": "Important", "Vulnerability": "", "Impact": "", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9247-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Logon/Logoff"], "Filter": ["OSVersion = [WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{732CAD0E-FFE4-45E7-814A-89D24A41564A}", "AZID": "CCE-37133-6", "Name": "Ensure 'Audit Account Lockout' is set to 'Success and Failure'", "Description": "This subcategory reports when a user's account is locked out as a result of too many failed logon attempts. Events for this subcategory include: - 4625: An account failed to log on. The recommended state for this setting is: `Success`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9217-69AE-11D9-BED3-************}", "ExpectedValue": "Success and Failure", "RemediateValue": "Success and Failure", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Logon/Logoff"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{cdad2863-96de-407f-8f8c-6e0c6dd8b86c}", "AZID": "CCE-38329-9", "Name": "Ensure 'Audit Application Group Management' is set", "Description": "This policy setting allows you to audit events generated by changes to application groups such as the following: - Application group is created, changed, or deleted. - Member is added or removed from an application group. Application groups are utilized by Windows Authorization Manager, which is a flexible framework created by Microsoft for integrating role-based access control (RBAC) into applications. More information on Windows Authorization Manager is available at [MSDN - Windows Authorization Manager](https:--msdn.microsoft.com-en-us-library-bb897401.aspx). The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing events in this category may be useful when investigating an incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9239-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Account Management"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{f789d729-095c-4e26-be9e-183f305f9e27}", "AZID": "CCE-38327-3", "Name": "Ensure 'Audit Authentication Policy Change' is set to 'Success'", "Description": "This subcategory reports changes in authentication policy. Events for this subcategory include: - 4706: A new trust was created to a domain. - 4707: A trust to a domain was removed. - 4713: Kerberos policy was changed. - 4716: Trusted domain information was modified. - 4717: System security access was granted to an account. - 4718: System security access was removed from an account. - 4739: Domain Policy was changed. - 4864: A namespace collision was detected. - 4865: A trusted forest information entry was added. - 4866: A trusted forest information entry was removed. - 4867: A trusted forest information entry was modified. The recommended state for this setting is: `Success`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9230-69AE-11D9-BED3-************}", "ExpectedValue": "Success", "RemediateValue": "Success", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Policy Change"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{ca5d1a59-f141-441d-a57e-6f8bdf078ff3}", "AZID": "CCE-36320-0", "Name": "Ensure 'Audit Authorization Policy Change' is set to 'No Auditing'", "Description": "This subcategory reports changes in authorization policy. Events for this subcategory include: - 4704: A user right was assigned. - 4705: A user right was removed. - 4706: A new trust was created to a domain. - 4707: A trust to a domain was removed. - 4714: Encrypted data recovery policy was changed. The recommended state for this setting is: `Success`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9231-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Policy Change"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{164BCF05-B0FE-456F-8A25-04D7D920F88A}", "AZID": "CCE-38004-8", "Name": "Ensure 'Audit Computer Account Management' is set to 'Success and Failure'", "Description": "This subcategory reports each event of computer account management, such as when a computer account is created, changed, deleted, renamed, disabled, or enabled. Events for this subcategory include: - 4741: A computer account was created. - 4742: A computer account was changed. - 4743: A computer account was deleted. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing events in this category may be useful when investigating an incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9236-69AE-11D9-BED3-************}", "ExpectedValue": "Success and Failure", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Account Management"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{4f8fd732-facf-4184-a29c-61fdd40db89d}", "AZID": "CCE-37741-6", "Name": "Ensure 'Audit Credential Validation' is set", "Description": "This subcategory reports the results of validation tests on credentials submitted for a user account logon request. These events occur on the computer that is authoritative for the credentials. For domain accounts, the domain controller is authoritative, whereas for local accounts, the local computer is authoritative. In domain environments, most of the Account Logon events occur in the Security log of the domain controllers that are authoritative for the domain accounts. However, these events can occur on other computers in the organization when local accounts are used to log on. Events for this subcategory include: - 4774: An account was mapped for logon. - 4775: An account could not be mapped for logon. - 4776: The domain controller attempted to validate the credentials for an account. - 4777: The domain controller failed to validate the credentials for an account. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE923F-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Account <PERSON><PERSON>"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{2fc86442-f648-4910-a570-f1c086178f23}", "AZID": "CCE-37850-5", "Name": "Ensure 'Audit: Force audit policy subcategory settings (Windows Vista or later) to override audit policy category settings' is set to 'Enabled'", "Description": "This policy setting allows administrators to enable the more precise auditing capabilities present in Windows Vista. The Audit Policy settings available in Windows Server 2003 Active Directory do not yet contain settings for managing the new auditing subcategories. To properly apply the auditing policies prescribed in this baseline, the Audit: Force audit policy subcategory settings (Windows Vista or later) to override audit policy category settings setting needs to be configured to Enabled. The recommended state for this setting is: `Enabled`. **Important:** Be very cautious about audit settings that can generate a large volume of traffic. For example, if you enable either success or failure auditing for all of the Privilege Use subcategories, the high volume of audit events generated can make it difficult to find other types of entries in the Security log. Such a configuration could also have a significant impact on system performance.", "Severity": "Critical", "Vulnerability": "Prior to the introduction of auditing subcategories in Windows Vista, it was difficult to track events at a per-system or per-user level. The larger event categories created too many events and the key information that needed to be audited was difficult to find.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Lsa\\SCENoApplyLegacyAuditPolicy", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["Logging & Auditing", "Local Policies-Audit"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{6907b165-e70a-4b88-b624-3e32a15c93b1}", "AZID": "CCE-35907-5", "Name": "Ensure 'Audit: Shut down system immediately if unable to log security audits' is set to 'Disabled'", "Description": "This policy setting determines whether the system shuts down if it is unable to log Security events. It is a requirement for Trusted Computer System Evaluation Criteria (TCSEC)-C2 and Common Criteria certification to prevent auditable events from occurring if the audit system is unable to log them. Microsoft has chosen to meet this requirement by halting the system and displaying a stop message if the auditing system experiences a failure. When this policy setting is enabled, the system will be shut down if a security audit cannot be logged for any reason. If the Audit: Shut down system immediately if unable to log security audits setting is enabled, unplanned system failures can occur. The administrative burden can be significant, especially if you also configure the Retention method for the Security log to Do not overwrite events (clear log manually). This configuration causes a repudiation threat (a backup operator could deny that they backed up or restored data) to become a denial of service (DoS) vulnerability, because a server could be forced to shut down if it is overwhelmed with logon events and other security events that are written to the Security log. Also, because the shutdown is not graceful, it is possible that irreparable damage to the operating system, applications, or data could result. Although the NTFS file system guarantees its integrity when an ungraceful computer shutdown occurs, it cannot guarantee that every data file for every application will still be in a usable form when the computer restarts. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "If the computer is unable to record events to the Security log, critical evidence or important troubleshooting information may not be available for review after a security incident. Also, an attacker could potentially generate a large volume of Security log events to purposely force a computer shutdown.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Lsa\\CrashOnAuditFail", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["Logging & Auditing", "Local Policies-Audit"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{21f4be1d-7729-45a5-8b55-e90fff597667}", "AZID": "CCE-36978-5", "Name": "Ensure 'Automatically send memory dumps for OS-generated error reports' is set to 'Enabled'", "Description": "This policy setting controls whether memory dumps in support of OS-generated error reports can be sent to Microsoft automatically. This policy does not apply to error reports generated by 3rd-party products, or additional data other than memory dumps. The recommended state for this setting is: `Disabled`.", "Severity": "Important", "Vulnerability": "Memory dumps may contain sensitive information and should not be automatically sent to anyone.", "Impact": "All memory dumps are uploaded according to the default consent and notification settings.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Windows Error Reporting\\AutoApproveOSDumps", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Administrative Templates (Computer)-Windows Error Reporting"], "Filter": ["OSVersion = [WS2012R2]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{4f8fd732-facf-4184-a29c-61fdd40db89d}", "AZID": "CCE-37741-6", "Name": "Ensure 'Audit Credential Validation' is set to 'Success and Failure'", "Description": "This subcategory reports the results of validation tests on credentials submitted for a user account logon request. These events occur on the computer that is authoritative for the credentials. For domain accounts, the domain controller is authoritative, whereas for local accounts, the local computer is authoritative. In domain environments, most of the Account Logon events occur in the Security log of the domain controllers that are authoritative for the domain accounts. However, these events can occur on other computers in the organization when local accounts are used to log on. Events for this subcategory include: - 4774: An account was mapped for logon. - 4775: An account could not be mapped for logon. - 4776: The domain controller attempted to validate the credentials for an account. - 4777: The domain controller failed to validate the credentials for an account. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE923F-69AE-11D9-BED3-************}", "ExpectedValue": "Success and Failure", "RemediateValue": "Success and Failure", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Account <PERSON><PERSON>"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{22b93657-329d-4904-8145-b6568a6098ed}", "AZID": "AZ-WIN-00136", "Name": "Ensure Azure Watson is configured correctly (Registry Check:DefaultConsent)", "Description": "Custom Azure setting", "Severity": "Important", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\Windows Error Reporting\\Consent\\DefaultConsent", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Antimalware", "Administrative Templates (Computer)-Reporting"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{2c5347d6-f7a9-47d3-ac3e-5d7d4fe949c8}", "AZID": "AZ-WIN-00137", "Name": "Ensure Azure Watson is configured correctly (Registry Check:ForceQueue)", "Description": "Custom Azure setting", "Severity": "Important", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\Windows Error Reporting\\ForceQueue", "ExpectedValue": "2", "RemediateValue": "2", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Antimalware", "Administrative Templates (Computer)-Reporting"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{877cfb8a-1504-4641-9caf-405768ff91f4}", "AZID": "CCE-35912-5", "Name": "Ensure 'Back up files and directories' is set to 'Administrators, Backup Operators'", "Description": "This policy setting allows users to circumvent file and directory permissions to back up the system. This user right is enabled only when an application (such as NTBACKUP) attempts to access a file or directory through the NTFS file system backup application programming interface (API). Otherwise, the assigned file and directory permissions apply. The recommended state for this setting is: `Administrators`.", "Severity": "Critical", "Vulnerability": "Users who are able to back up data from a computer could take the backup media to a non-domain computer on which they have administrative privileges and restore the data. They could take ownership of the files and view any unencrypted data that is contained within the backup set.", "Impact": "Changes in the membership of the groups that have the Back up files and directories user right could limit the abilities of users who are assigned to specific administrative roles in your environment. You should confirm that authorized backup administrators are still able to perform backup operations.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeBackupPrivilege", "ExpectedValue": "Adminstrators, Backup Operators", "RemediateValue": "Adminstrators, Backup Operators", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "LESSOREQUAL", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{bd13ed1c-cb4f-4d46-903f-6b2061f37ead}", "AZID": "AZ-WIN-00138", "Name": "Ensure 'Block user from showing account details on sign-in' is set to 'Enabled'", "Description": "This policy prevents the user from showing account details (email address or user name) on the sign-in screen. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "An attacker with access to the console (for example, someone with physical access or someone who is able to connect to the server through Terminal Services) could view the name of the last user who logged on to the server. The attacker could then try to guess the password, use a dictionary, or use a brute-force attack to try and log on.", "Impact": "The user cannot choose to show account details on the sign-in screen.", "DataSourceType": "Registry", "DataSourceKey": "Software\\Policies\\Microsoft\\Windows\\System\\BlockUserFromShowingAccountDetailsOnSignin", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["Identity Management", "Administrative Templates (Computer)-Logon"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{51480e76-0d6b-476c-b9a1-388f625a5e66}", "AZID": "CCE-37912-3", "Name": "Ensure 'Boot-Start Driver Initialization Policy' is set to 'Enabled: Good, unknown and bad but critical'", "Description": "This policy setting allows you to specify which boot-start drivers are initialized based on a classification determined by an Early Launch Antimalware boot-start driver. The Early Launch Antimalware boot-start driver can return the following classifications for each boot-start driver: - `Good`: The driver has been signed and has not been tampered with. - `Bad`: The driver has been identified as malware. It is recommended that you do not allow known bad drivers to be initialized. - `Bad, but required for boot`: The driver has been identified as malware, but the computer cannot successfully boot without loading this driver. - `Unknown`: This driver has not been attested to by your malware detection application and has not been classified by the Early Launch Antimalware boot-start driver. If you enable this policy setting you will be able to choose which boot-start drivers to initialize the next time the computer is started. If your malware detection application does not include an Early Launch Antimalware boot-start driver or if your Early Launch Antimalware boot-start driver has been disabled, this setting has no effect and all boot-start drivers are initialized. The recommended state for this setting is: `Enabled: Good, unknown and bad but critical`.", "Severity": "Important", "Vulnerability": "This policy setting helps reduce the impact of malware that has already infected your system.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Policies\\EarlyLaunch\\DriverLoadPolicy", "ExpectedValue": "3", "RemediateValue": "3", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Antimalware", "Administrative Templates (Computer)-Early Launch Antimalware"], "Filter": ["OSVersion = [WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{8b6f479f-13a9-40d1-a2d6-bd9c27d2b7dc}", "AZID": "CCE-37452-0", "Name": "Ensure 'Change the system time' is set to 'Administrators, LOCAL SERVICE'", "Description": "This policy setting determines which users and groups can change the time and date on the internal clock of the computers in your environment. Users who are assigned this user right can affect the appearance of event logs. When a computer's time setting is changed, logged events reflect the new time, not the actual time that the events occurred. When configuring a user right in the SCM enter a comma delimited list of accounts. Accounts can be either local or located in Active Directory, they can be groups, users, or computers. **Note:** Discrepancies between the time on the local computer and on the domain controllers in your environment may cause problems for the Kerberos authentication protocol, which could make it impossible for users to log on to the domain or obtain authorization to access domain resources after they are logged on. Also, problems will occur when Group Policy is applied to client computers if the system time is not synchronized with the domain controllers. The recommended state for this setting is: `Administrators, LOCAL SERVICE`.", "Severity": "Critical", "Vulnerability": "Users who can change the time on a computer could cause several problems. For example, time stamps on event log entries could be made inaccurate, time stamps on files and folders that are created or modified could be incorrect, and computers that belong to a domain may not be able to authenticate themselves or users who try to log on to the domain from them. Also, because the Kerberos authentication protocol requires that the requestor and authenticator have their clocks synchronized within an administrator-defined skew period, an attacker who changes a computer's time may cause that computer to be unable to obtain or grant Kerberos tickets. The risk from these types of events is mitigated on most domain controllers, member servers, and end-user computers because the Windows Time service automatically synchronizes time with domain controllers in the following ways: - All client desktop computers and member servers use the authenticating domain controller as their inbound time partner. - All domain controllers in a domain nominate the primary domain controller (PDC) emulator operations master as their inbound time partner. - All PDC emulator operations masters follow the hierarchy of domains in the selection of their inbound time partner. - The PDC emulator operations master at the root of the domain is authoritative for the organization. Therefore it is recommended that you configure this computer to synchronize with a reliable external time server. This vulnerability becomes much more serious if an attacker is able to change the system time and then stop the Windows Time service or reconfigure it to synchronize with a time server that is not accurate.", "Impact": "There should be no impact, because time synchronization for most organizations should be fully automated for all computers that belong to the domain. Computers that do not belong to the domain should be configured to synchronize with an external source.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeSystemtimePrivilege", "ExpectedValue": "Administrators, LOCAL SERVICE", "RemediateValue": "Administrators, LOCAL SERVICE", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "LESSOREQUAL", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{8ed0c2c5-af57-4434-9ae8-fe93bc39bfd0}", "AZID": "CCE-37700-2", "Name": "Ensure 'Change the time zone' is set to 'Administrators, LOCAL SERVICE'", "Description": "This setting determines which users can change the time zone of the computer. This ability holds no great danger for the computer and may be useful for mobile workers. The recommended state for this setting is: `Administrators, LOCAL SERVICE`.", "Severity": "Critical", "Vulnerability": "Changing the time zone represents little vulnerability because the system time is not affected. This setting merely enables users to display their preferred time zone while being synchronized with domain controllers in different time zones.", "Impact": "None - this is the default configuration.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeTimeZonePrivilege", "ExpectedValue": "Administrators, LOCAL SERVICE", "RemediateValue": "Administrators, LOCAL SERVICE", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "LESSOREQUAL", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{0e617f2a-c950-4be1-a1e0-b32933f0f4ae}", "AZID": "CCE-37112-0", "Name": "Ensure 'Configure Default consent' is set to 'Enabled: Send all data'", "Description": "This setting allows you to set the default consent handling for error reports. The recommended state for this setting is: `Enabled: Always ask before sending data`", "Severity": "Important", "Vulnerability": "Error reports may contain sensitive information and should not be sent to anyone automatically.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Windows Error Reporting\\Consent\\DefaultConsent", "ExpectedValue": "4", "RemediateValue": "4", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["Logging & Auditing", "Administrative Templates (Computer)-<PERSON><PERSON>"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{7450d70c-391d-4932-be4a-3f3bfecc0eb5}", "AZID": "CCE-36388-7", "Name": "Ensure 'Configure Offer Remote Assistance' is set to 'Disabled'", "Description": "This policy setting allows you to turn on or turn off Offer (Unsolicited) Remote Assistance on this computer. Help desk and support personnel will not be able to proactively offer assistance, although they can still respond to user assistance requests. The recommended state for this setting is: `Disabled`.", "Severity": "Important", "Vulnerability": "A user might be tricked and accept an unsolicited Remote Assistance offer from a malicious user.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows NT\\Terminal Services\\fAllowUnsolicited", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: RDP", "Administrative Templates (Computer)-Remote Assistance"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{99cd4fc9-bcf1-4def-8ce6-5a3c4ea8f8c9}", "AZID": "CCE-36169-1", "Name": "Ensure 'Configure registry policy processing: Do not apply during periodic background processing' is set to 'Enabled: FALSE'", "Description": "The \"Do not apply during periodic background processing\" option prevents the system from updating affected policies in the background while the computer is in use. When background updates are disabled, policy changes will not take effect until the next user logon or system restart. The recommended state for this setting is: `Enabled: FALSE` (unchecked).", "Severity": "Critical", "Vulnerability": "Setting this option to false (unchecked) will ensure that domain policy changes take effect more quickly, as compared to waiting until the next user logon or system restart.", "Impact": "Group Policies will be reapplied every time they are refreshed, which could have a slight impact on performance.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Group Policy\\{35378EAC-683F-11D2-A89A-00C04FBBCFA2}\\NoBackgroundPolicy", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["GPO settings", "Administrative Templates (Computer)-Group Policy"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{b2e8d5f9-3d4e-4b8b-b6a1-ddcd60f437b9}", "AZID": "CCE-36169-1a", "Name": "Ensure 'Configure registry policy processing: Process even if the Group Policy objects have not changed' is set to 'Enabled: TRUE'", "Description": "The \"Process even if the Group Policy objects have not changed\" option updates and reapplies policies even if the policies have not changed. The recommended state for this setting is: `Enabled: TRUE` (checked).", "Severity": "Critical", "Vulnerability": "Setting this option to true (checked) will ensure unauthorized changes that might have been configured locally are forced to match the domain-based Group Policy settings again.", "Impact": "Group Policies will be reapplied even if they have not been changed, which could have a slight impact on performance.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Group Policy\\{35378EAC-683F-11D2-A89A-00C04FBBCFA2}\\NoGPOListChanges", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["GPO settings", "Administrative Templates (Computer)-Group Policy"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{b17eabc0-5d73-4861-acc8-d5b97bc53f12}", "AZID": "CCE-37281-3", "Name": "Ensure 'Configure Solicited Remote Assistance' is set to 'Disabled'", "Description": "This policy setting allows you to turn on or turn off Solicited (Ask for) Remote Assistance on this computer. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "There is slight risk that a rogue administrator will gain access to another user's desktop session, however, they cannot connect to a user's computer unannounced or control it without permission from the user. When an expert tries to connect, the user can still choose to deny the connection or give the expert view-only privileges. The user must explicitly click the Yes button to allow the expert to remotely control the workstation.", "Impact": "Users on this computer cannot use e-mail or file transfer to ask someone for help. Also, users cannot use instant messaging programs to allow connections to this computer.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows NT\\Terminal Services\\fAllowToGetHelp", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: RDP", "Administrative Templates (Computer)-Remote Assistance"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{2af6b74d-7727-4fcf-8f41-71c89ca0614f}", "AZID": "CCE-35859-8", "Name": "Ensure 'Configure Windows SmartScreen' is set to 'Enabled'", "Description": "This policy setting allows you to manage the behavior of Windows SmartScreen. Windows SmartScreen helps keep PCs safer by warning users before running unrecognized programs downloaded from the Internet. Some information is sent to Microsoft about files and programs run on PCs with this feature enabled. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "Windows SmartScreen helps keep PCs safer by warning users before running unrecognized programs downloaded from the Internet. However, due to the fact that some information is sent to Microsoft about files and programs run on PCs some organizations may prefer to disable it.", "Impact": "Only administrators will be able to run unrecognized programs downloaded from the Internet. If users with a standard account try, they won't be able to unless they get an administrator to authorize it.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\System\\EnableSmartScreen", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Antimalware", "Administrative Templates (Computer)-File Explorer"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{e588914e-fbb8-4926-9ccf-8ea781b07610}", "AZID": "AZ-WIN-00139", "Name": "Ensure 'Continue experiences on this device' is set to 'Disabled'", "Description": "This policy setting determines whether the Windows device is allowed to participate in cross-device experiences (continue experiences). The recommended state for this setting is: `Disabled`.", "Severity": "Important", "Vulnerability": "A cross-device experience is when a system can access app and send messages to other devices. In an enterprise environment only trusted systems should be communicating within the network. Access to any other system should be prohibited.", "Impact": "The Windows device will not be discoverable by other devices, and cannot participate in cross-device experiences.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\System\\EnableCdp", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Network", "Administrative Templates (Computer)-Group Policy"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{29395413-af1d-4052-86c4-2b059fd4a778}", "AZID": "CCE-37433-0", "Name": "Ensure 'Audit Directory Service Access' is set (DC only)", "Description": "This subcategory reports when an AD DS object is accessed. Only objects with SACLs cause audit events to be generated, and only when they are accessed in a manner that matches their SACL. These events are similar to the directory service access events in previous versions of Windows Server. This subcategory applies only to domain controllers. Events for this subcategory include: - 4662 : An operation was performed on an object. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE923B-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-DS Access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{04251e82-4442-4923-ac77-992891a5042b}", "AZID": "CCE-35821-8", "Name": "Ensure 'Create a pagefile' is set to 'Administrators'", "Description": "This policy setting allows users to change the size of the pagefile. By making the pagefile extremely large or extremely small, an attacker could easily affect the performance of a compromised computer. The recommended state for this setting is: `Administrators`.", "Severity": "Critical", "Vulnerability": "Users who can change the page file size could make it extremely small or move the file to a highly fragmented storage volume, which could cause reduced computer performance.", "Impact": "None - this is the default configuration.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeCreatePagefilePrivilege", "ExpectedValue": "Administrators", "RemediateValue": "Administrators", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{d3f866fb-8adf-4ec6-adc7-93bb9ebcccdd}", "AZID": "CCE-36861-3", "Name": "Ensure 'Create a token object' is set to 'No One'", "Description": "This policy setting allows a process to create an access token, which may provide elevated rights to access sensitive data. The recommended state for this setting is: `No One`.", "Severity": "Important", "Vulnerability": "A user account that is given this user right has complete control over the system and can lead to the system being compromised. It is highly recommended that you do not assign any user accounts this right. The operating system examines a user's access token to determine the level of the user's privileges. Access tokens are built when users log on to the local computer or connect to a remote computer over a network. When you revoke a privilege, the change is immediately recorded, but the change is not reflected in the user's access token until the next time the user logs on or connects. Users with the ability to create or modify tokens can change the level of access for any currently logged on account. They could escalate their own privileges or create a DoS condition.", "Impact": "None - this is the default configuration.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeCreateTokenPrivilege", "ExpectedValue": "No One", "RemediateValue": "No One", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{c0a4a0ed-1585-4857-8e2b-30b1bb48c6ea}", "AZID": "CCE-37453-8", "Name": "Ensure 'Create global objects' is set to 'Administrators, LOCAL SERVICE, NETWORK SERVICE, SERVICE'", "Description": "This policy setting determines whether users can create global objects that are available to all sessions. Users can still create objects that are specific to their own session if they do not have this user right. Users who can create global objects could affect processes that run under other users' sessions. This capability could lead to a variety of problems, such as application failure or data corruption. The recommended state for this setting is: `Administrators, LOCAL SERVICE, NETWORK SERVICE, SERVICE`. **Note:** A Member Server with Microsoft SQL Server _and_ its optional \"Integration Services\" component installed will require a special exception to this recommendation for additional SQL-generated entries to be granted this user right.", "Severity": "Important", "Vulnerability": "Users who can create global objects could affect Windows services and processes that run under other user or system accounts. This capability could lead to a variety of problems, such as application failure, data corruption and elevation of privilege.", "Impact": "None - this is the default configuration.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeCreateGlobalPrivilege", "ExpectedValue": "Administrators, SERVICE, LOCAL SERVICE, NETWORK SERVICE", "RemediateValue": "Administrators, SERVICE, LOCAL SERVICE, NETWORK SERVICE", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "LESSOREQUAL", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{03766d3c-81c2-438e-8192-91787f2ae69a}", "AZID": "CCE-36532-0", "Name": "Ensure 'Create permanent shared objects' is set to 'No One'", "Description": "This user right is useful to kernel-mode components that extend the object namespace. However, components that run in kernel mode have this user right inherently. Therefore, it is typically not necessary to specifically assign this user right. The recommended state for this setting is: `No One`.", "Severity": "Important", "Vulnerability": "Users who have the Create permanent shared objects user right could create new shared objects and expose sensitive data to the network.", "Impact": "None - this is the default configuration.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeCreatePermanentPrivilege", "ExpectedValue": "No One", "RemediateValue": "No One", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{********-b2f0-4a4e-b66a-6954bb8473bf}", "AZID": "CCE-36923-1", "Name": "Ensure 'Den<PERSON> log on as a batch job' to include 'Guests'", "Description": "This policy setting determines which accounts will not be able to log on to the computer as a batch job. A batch job is not a batch (.bat) file, but rather a batch-queue facility. Accounts that use the Task Scheduler to schedule jobs need this user right. The **Deny log on as a batch job** user right overrides the **Log on as a batch job** user right, which could be used to allow accounts to schedule jobs that consume excessive system resources. Such an occurrence could cause a DoS condition. Failure to assign this user right to the recommended accounts can be a security risk. The recommended state for this setting is to include: `Guests`.", "Severity": "Critical", "Vulnerability": "Accounts that have the <PERSON><PERSON> log on as a batch job user right could be used to schedule jobs that could consume excessive computer resources and cause a DoS condition.", "Impact": "If you assign the Deny log on as a batch job user right to other accounts, you could deny users who are assigned to specific administrative roles the ability to perform their required job activities. You should confirm that delegated tasks will not be affected adversely. For example, if you assign this user right to the IWAM_(ComputerName) account, the MSM Management Point will fail. On a newly installed computer that runs Windows Server 2003 this account does not belong to the Guests group, but on a computer that was upgraded from Windows 2000 this account is a member of the Guests group. Therefore, it is important that you understand which accounts belong to any groups that you assign the Deny log on as a batch job user right.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeDenyBatchLogonRight", "ExpectedValue": "Guests", "RemediateValue": "Guests", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Network", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{3b993f8f-245d-4f4e-9e8b-f94cbc71c3f6}", "AZID": "CCE-36877-9", "Name": "Ensure 'Deny log on as a service' to include 'Guests'", "Description": "This security setting determines which service accounts are prevented from registering a process as a service. This policy setting supersedes the **Log on as a service** policy setting if an account is subject to both policies. The recommended state for this setting is to include: `Guests`. **Note:** This security setting does not apply to the System, Local Service, or Network Service accounts.", "Severity": "Critical", "Vulnerability": "Accounts that can log on as a service could be used to configure and start new unauthorized services, such as a keylogger or other malicious software. The benefit of the specified countermeasure is somewhat reduced by the fact that only users with administrative privileges can install and configure services, and an attacker who has already attained that level of access could configure the service to run with the System account.", "Impact": "If you assign the <PERSON><PERSON> log on as a service user right to specific accounts, services may not be able to start and a DoS condition could result.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeDenyServiceLogonRight", "ExpectedValue": "Guests", "RemediateValue": "Guests", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Network", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{b7432fc2-51ba-4ddf-83dd-ca7f92e670c1}", "AZID": "CCE-37146-8", "Name": "Ensure 'Deny log on locally' to include 'Guests'", "Description": "This security setting determines which users are prevented from logging on at the computer. This policy setting supersedes the **Allow log on locally** policy setting if an account is subject to both policies. **Important:** If you apply this security policy to the Everyone group, no one will be able to log on locally. The recommended state for this setting is to include: `Guests`.", "Severity": "Critical", "Vulnerability": "Any account with the ability to log on locally could be used to log on at the console of the computer. If this user right is not restricted to legitimate users who need to log on to the console of the computer, unauthorized users might download and run malicious software that elevates their privileges.", "Impact": "If you assign the Deny log on locally user right to additional accounts, you could limit the abilities of users who are assigned to specific roles in your environment. However, this user right should explicitly be assigned to the ASPNET account on computers that run IIS 6.0. You should confirm that delegated activities will not be adversely affected.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeDenyInteractiveLogonRight", "ExpectedValue": "Guests", "RemediateValue": "Guests", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["Identity Management", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{3ca87bf1-cb63-499a-b6cb-321a4d6cb11a}", "AZID": "CCE-36867-0", "Name": "Ensure 'Deny log on through Remote Desktop Services' to include 'Guests'", "Description": "This policy setting determines whether users can log on as Terminal Services clients. After the baseline member server is joined to a domain environment, there is no need to use local accounts to access the server from the network. Domain accounts can access the server for administration and end-user processing. The recommended state for this setting is to include: `Guests, Local account`. **Caution:** Configuring a standalone (non-domain-joined) server as described above may result in an inability to remotely administer the server.", "Severity": "Critical", "Vulnerability": "Any account with the right to log on through Terminal Services could be used to log on to the remote console of the computer. If this user right is not restricted to legitimate users who need to log on to the console of the computer, unauthorized users might download and run malicious software that elevates their privileges.", "Impact": "If you assign the Deny log on through Terminal Services user right to other groups, you could limit the abilities of users who are assigned to specific administrative roles in your environment. Accounts that have this user right will be unable to connect to the computer through either Terminal Services or Remote Assistance. You should confirm that delegated tasks will not be negatively impacted.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeDenyRemoteInteractiveLogonRight", "ExpectedValue": "Guests", "RemediateValue": "Guests", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: RDP", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{2bf6d8be-3941-4fad-9079-7d5018eda0ae}", "AZID": "CCE-37701-0", "Name": "Ensure 'Devices: Allowed to format and eject removable media' is set to 'Administrators'", "Description": "This policy setting determines who is allowed to format and eject removable NTFS media. You can use this policy setting to prevent unauthorized users from removing data on one computer to access it on another computer on which they have local administrator privileges. The recommended state for this setting is: `Administrators`.", "Severity": "Important", "Vulnerability": "Users may be able to move data on removable disks to a different computer where they have administrative privileges. The user could then take ownership of any file, grant themselves full control, and view or modify any file. The fact that most removable storage devices will eject media by pressing a mechanical button diminishes the advantage of this policy setting.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Winlogon\\AllocateDASD", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_SZ", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Devices", "Local Policies-Devices"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{5502808d-7049-4378-b9f7-038b70777483}", "AZID": "CCE-37942-0", "Name": "Ensure 'Devices: Prevent users from installing printer drivers' is set to 'Enabled'", "Description": "For a computer to print to a shared printer, the driver for that shared printer must be installed on the local computer. This security setting determines who is allowed to install a printer driver as part of connecting to a shared printer. The recommended state for this setting is: `Enabled`. **Note:** This setting does not affect the ability to add a local printer. This setting does not affect Administrators.", "Severity": "Important", "Vulnerability": "It may be appropriate in some organizations to allow users to install printer drivers on their own workstations. However, you should allow only Administrators, not users, to do so on servers, because printer driver installation on a server may unintentionally cause the computer to become less stable. A malicious user could install inappropriate printer drivers in a deliberate attempt to damage the computer, or a user might accidentally install malicious software that masquerades as a printer driver. It is feasible for an attacker to disguise a Trojan horse program as a printer driver. The program may appear to users as if they must use it to print, but such a program could unleash malicious code on your computer network.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Print\\Providers\\LanMan Print Services\\Servers\\AddPrinterDrivers", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Devices", "Local Policies-Devices"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{a6aeecbf-b3e8-48ed-a385-32c3ada14710}", "AZID": "CCE-37636-8", "Name": "Ensure 'Disallow Autoplay for non-volume devices' is set to 'Enabled'", "Description": "This policy setting disallows AutoPlay for MTP devices like cameras or phones. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "An attacker could use this feature to launch a program to damage a client computer or data on the computer.", "Impact": "AutoPlay will not be allowed for MTP devices like cameras or phones.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Explorer\\NoAutoplayfornonVolume", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Autoplay", "Administrative Templates (Computer)-AutoPlay Policies"], "Filter": ["OSVersion = [WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{34edb7eb-697c-4be9-8830-5aa5b031372e}", "AZID": "CCE-38318-2", "Name": "Ensure 'Disallow Digest authentication' is set to 'Enabled'", "Description": "This policy setting allows you to manage whether the Windows Remote Management (WinRM) client will not use Digest authentication. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "Digest authentication is less robust than other authentication methods available in WinRM, an attacker who is able to capture packets on the network where WinRM is running may be able to determine the credentials used for accessing remote hosts via WinRM.", "Impact": "The WinRM client will not use Digest authentication.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\WinRM\\Client\\AllowDigest", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: WinRM", "Administrative Templates (Computer)-WinRM Client"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{6e6cd31c-e045-4b04-9fad-475aef45dd15}", "AZID": "CCE-37616-0", "Name": "Ensure 'Audit Directory Service Changes' is set (DC only)", "Description": "This subcategory reports changes to objects in Active Directory Domain Services (AD DS). The types of changes that are reported are create, modify, move, and undelete operations that are performed on an object. DS Change auditing, where appropriate, indicates the old and new values of the changed properties of the objects that were changed. Only objects with SACLs cause audit events to be generated, and only when they are accessed in a manner that matches their SACL. Some objects and properties do not cause audit events to be generated due to settings on the object class in the schema. This subcategory applies only to domain controllers. Events for this subcategory include: - 5136 : A directory service object was modified. - 5137 : A directory service object was created. - 5138 : A directory service object was undeleted. - 5139 : A directory service object was moved. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE923C-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-DS Access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{7a20fa0b-afe4-4f3e-8d4e-850588519824}", "AZID": "CCE-36000-8", "Name": "Ensure 'Disallow WinRM from storing RunAs credentials' is set to 'Enabled'", "Description": "This policy setting allows you to manage whether the Windows Remote Management (WinRM) service will not allow RunAs credentials to be stored for any plug-ins. The recommended state for this setting is: `Enabled`. **Note:** If you enable and then disable this policy setting, any values that were previously configured for RunAsPassword will need to be reset.", "Severity": "Critical", "Vulnerability": "Although the ability to store RunAs credentials is a convenient feature it increases the risk of account compromise slightly. For example, if you forget to lock your desktop before leaving it unattended for a few minutes another person could access not only the desktop of your computer but also any hosts you manage via WinRM with cached RunAs credentials.", "Impact": "The WinRM service will not allow the RunAsUser or RunAsPassword configuration values to be set for any plug-ins. If a plug-in has already set the RunAsUser and RunAsPassword configuration values, the RunAsPassword configuration value will be erased from the credential store on the computer. If this setting is later Disabled again, any values that were previously configured for RunAsPassword will need to be reset.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\WinRM\\Service\\DisableRunAs", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: WinRM", "Administrative Templates (Computer)-WinRM Service"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{08b5f46b-8228-437c-8ed8-50c31b03423e}", "AZID": "CCE-36223-6", "Name": "Ensure 'Do not allow passwords to be saved' is set to 'Enabled'", "Description": "This policy setting helps prevent Remote Desktop Services - Terminal Services clients from saving passwords on a computer. The recommended state for this setting is: `Enabled`. **Note:** If this policy setting was previously configured as Disabled or Not configured, any previously saved passwords will be deleted the first time a Terminal Services client disconnects from any server.", "Severity": "Critical", "Vulnerability": "An attacker with physical access to the computer may be able to break the protection guarding saved passwords. An attacker who compromises a user's account and connects to their computer could use saved passwords to gain access to additional hosts.", "Impact": "The password saving checkbox will be disabled for Remote Desktop Services - Terminal Services clients and users will not be able to save passwords.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows NT\\Terminal Services\\DisablePasswordSaving", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: RDP", "Administrative Templates (Computer)-Remote Desktop Connection Client"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{3c4ba67d-f44e-4525-b7c4-f09fff1ff87d}", "AZID": "CCE-37946-1", "Name": "Ensure 'Do not delete temp folders upon exit' is set to 'Disabled'", "Description": "This policy setting specifies whether Remote Desktop Services retains a user's per-session temporary folders at logoff. The recommended state for this setting is: `Disabled`.", "Severity": "Important", "Vulnerability": "Sensitive information could be contained inside the temporary folders and shared with other administrators that log into the system.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows NT\\Terminal Services\\DeleteTempDirsOnExit", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: RDP", "Administrative Templates (Computer)-Temporary folders"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{72e173b5-f3d7-4998-8716-b77a42ceb67a}", "AZID": "CCE-38353-9", "Name": "Ensure 'Do not display network selection UI' is set to 'Enabled'", "Description": "This policy setting allows you to control whether anyone can interact with available networks UI on the logon screen. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "An unauthorized user could disconnect the PC from the network or can connect the PC to other available networks without signing into Windows.", "Impact": "The PC's network connectivity state cannot be changed without signing into Windows.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\System\\DontDisplayNetworkSelectionUI", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Network", "Administrative Templates (Computer)-Logon"], "Filter": ["OSVersion = [WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{43e06bee-4b84-44e9-afb6-6f7144d03aa2}", "AZID": "CCE-37534-5", "Name": "Ensure 'Do not display the password reveal button' is set to 'Enabled'", "Description": "This policy setting allows you to configure the display of the password reveal button in password entry user experiences. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "This is a useful feature when entering a long and complex password, especially when using a touchscreen. The potential risk is that someone else may see your password while surreptitiously observing your screen.", "Impact": "The password reveal button will not be displayed after a user types a password in the password entry text box.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\CredUI\\DisablePasswordReveal", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["Identity Management", "Administrative Templates (Computer)-Credential User Interface"], "Filter": ["OSVersion = [WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{6783beb7-7268-4e40-872a-31709ed81797}", "AZID": "AZ-WIN-00140", "Name": "Ensure 'Do not show feedback notifications' is set to 'Enabled'", "Description": "This policy setting allows an organization to prevent its devices from showing feedback questions from Microsoft. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "In an enterprise environment users should not be sending any feedback to 3rd party vendors.", "Impact": "Users will no longer see feedback notifications through the Windows Feedback app.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\DataCollection\\DoNotShowFeedbackNotifications", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Administrative Templates (Computer)-Data Collection and Preview Builds"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{3e9b0b4d-bb0c-4691-a9b2-0a7bc623925d}", "AZID": "CCE-38180-6", "Name": "Ensure 'Do not use temporary folders per session' is set to 'Disabled'", "Description": "By default, Remote Desktop Services creates a separate temporary folder on the RD Session Host server for each active session that a user maintains on the RD Session Host server. The temporary folder is created on the RD Session Host server in a Temp folder under the user's profile folder and is named with the \"sessionid.\" This temporary folder is used to store individual temporary files. To reclaim disk space, the temporary folder is deleted when the user logs off from a session. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "By Disabling this setting you are keeping the cached data independent for each session, both reducing the chance of problems from shared cached data between sessions, and keeping possibly sensitive data separate to each user session.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows NT\\Terminal Services\\PerSessionTempDir", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: RDP", "Administrative Templates (Computer)-Temporary folders"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{3b8a1eba-64e5-4117-b7bc-2cf5042de658}", "AZID": "CCE-36142-8", "Name": "Ensure 'Domain member: Digitally encrypt or sign secure channel data (always)' is set to 'Enabled'", "Description": "This policy setting determines whether all secure channel traffic that is initiated by the domain member must be signed or encrypted. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "When a computer joins a domain, a computer account is created. After it joins the domain, the computer uses the password for that account to create a secure channel with the domain controller for its domain every time that it restarts. Requests that are sent on the secure channel are authenticated—and sensitive information such as passwords are encrypted—but the channel is not integrity-checked, and not all information is encrypted. Digital encryption and signing of the secure channel is a good idea where it is supported. The secure channel protects domain credentials as they are sent to the domain controller.", "Impact": "None - this is the default configuration. However, only Windows NT 4.0 with Service Pack 6a (SP6a) and subsequent versions of the Windows operating system support digital encryption and signing of the secure channel. Windows 98 Second Edition clients do not support it unless they have the Dsclient installed. Therefore, you cannot enable the Domain member: Digitally encrypt or sign secure channel data (always) setting on domain controllers that support Windows 98 clients as members of the domain. Potential impacts can include the following: - The ability to create or delete trust relationships with clients running versions of Windows earlier than Windows NT 4.0 with SP6a will be disabled. - Logons from clients running versions of Windows earlier than Windows NT 4.0 with SP6a will be disabled. - The ability to authenticate other domains' users from a domain controller running a version of Windows earlier than Windows NT 4.0 with SP6a in a trusted domain will be disabled. You can enable this policy setting after you eliminate all Windows 9x clients from the domain and upgrade all Windows NT 4.0 servers and domain controllers from trusted-trusting domains to Windows NT 4.0 with SP6a.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\Netlogon\\Parameters\\RequireSignOrSeal", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Encryption", "Local Policies-Domain member"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{915714e9-c2ae-42af-a391-c289db580e08}", "AZID": "CCE-37130-2", "Name": "Ensure 'Domain member: Digitally encrypt secure channel data (when possible)' is set to 'Enabled'", "Description": "This policy setting determines whether a domain member should attempt to negotiate encryption for all secure channel traffic that it initiates. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "When a computer joins a domain, a computer account is created. After it joins the domain, the computer uses the password for that account to create a secure channel with the domain controller for its domain every time that it restarts. Requests that are sent on the secure channel are authenticated—and sensitive information such as passwords are encrypted—but the channel is not integrity-checked, and not all information is encrypted. Digital encryption and signing of the secure channel is a good idea where it is supported. The secure channel protects domain credentials as they are sent to the domain controller.", "Impact": "None - this is the default configuration. However, only Windows NT 4.0 Service Pack 6a (SP6a) and subsequent versions of the Windows operating system support digital encryption and signing of the secure channel. Windows 98 Second Edition clients do not support it unless they have the Dsclient installed.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\Netlogon\\Parameters\\SealSecureChannel", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Encryption", "Local Policies-Domain member"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{C9A0B1BF-D925-48D6-8BC6-FB137B5D8C3D}", "AZID": "CCE-36265-7", "Name": "Ensure 'Audit Distribution Group Management' is set to 'Success and Failure' (DC only)", "Description": "This subcategory reports each event of distribution group management, such as when a distribution group is created, changed, or deleted or when a member is added to or removed from a distribution group. If you enable this Audit policy setting, administrators can track events to detect malicious, accidental, and authorized creation of group accounts. Events for this subcategory include: - 4744: A security-disabled local group was created. - 4745: A security-disabled local group was changed. - 4746: A member was added to a security-disabled local group. - 4747: A member was removed from a security-disabled local group. - 4748: A security-disabled local group was deleted. - 4749: A security-disabled global group was created. - 4750: A security-disabled global group was changed. - 4751: A member was added to a security-disabled global group. - 4752: A member was removed from a security-disabled global group. - 4753: A security-disabled global group was deleted. - 4759: A security-disabled universal group was created. - 4760: A security-disabled universal group was changed. - 4761: A member was added to a security-disabled universal group. - 4762: A member was removed from a security-disabled universal group. - 4763: A security-disabled universal group was deleted. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing these events may provide an organization with insight when investigating an incident. For example, when a given unauthorized user was added to a sensitive distribution group.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9238-69AE-11D9-BED3-************}", "ExpectedValue": "Success and Failure", "RemediateValue": "Success and Failure", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Account Management"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{b399c529-eeec-48dd-92e5-f1b2e14f12c9}", "AZID": "CCE-37222-7", "Name": "Ensure 'Domain member: Digitally sign secure channel data (when possible)' is set to 'Enabled'", "Description": "This policy setting determines whether a domain member should attempt to negotiate whether all secure channel traffic that it initiates must be digitally signed. Digital signatures protect the traffic from being modified by anyone who captures the data as it traverses the network. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "When a computer joins a domain, a computer account is created. After it joins the domain, the computer uses the password for that account to create a secure channel with the domain controller for its domain every time that it restarts. Requests that are sent on the secure channel are authenticated—and sensitive information such as passwords are encrypted—but the channel is not integrity-checked, and not all information is encrypted. Digital encryption and signing of the secure channel is a good idea where it is supported. The secure channel protects domain credentials as they are sent to the domain controller.", "Impact": "None - this is the default configuration. However, only Windows NT 4.0 with Service Pack 6a (SP6a) and subsequent versions of the Windows operating system support digital encryption and signing of the secure channel. Windows 98 Second Edition clients do not support it unless they have the Dsclient installed.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\Netlogon\\Parameters\\SignSecureChannel", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Encryption", "Local Policies-Domain member"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{cb4110e4-23c8-46ab-9202-497a70efd077}", "AZID": "CCE-37508-9", "Name": "Ensure 'Domain member: Disable machine account password changes' is set to 'Disabled'", "Description": "This policy setting determines whether a domain member can periodically change its computer account password. Computers that cannot automatically change their account passwords are potentially vulnerable, because an attacker might be able to determine the password for the system's domain account. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "The default configuration for Windows Server 2003-based computers that belong to a domain is that they are automatically required to change the passwords for their accounts every 30 days. If you disable this policy setting, computers that run Windows Server 2003 will retain the same passwords as their computer accounts. Computers that are no longer able to automatically change their account password are at risk from an attacker who could determine the password for the computer's domain account.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\Netlogon\\Parameters\\DisablePasswordChange", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["Identity Management", "Local Policies-Domain member"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{e30d6758-fb3c-4e9d-8493-f717cd504cf4}", "AZID": "CCE-37431-4", "Name": "Ensure 'Domain member: Maximum machine account password age' is set to '30 or fewer days, but not 0'", "Description": "This policy setting determines the maximum allowable age for a computer account password. By default, domain members automatically change their domain passwords every 30 days. If you increase this interval significantly so that the computers no longer change their passwords, an attacker would have more time to undertake a brute force attack against one of the computer accounts. The recommended state for this setting is: `30 or fewer days, but not 0`. **Note:** A value of `0` does not conform to the benchmark as it disables maximum password age.", "Severity": "Critical", "Vulnerability": "In Active Directory-based domains, each computer has an account and password just like every user. By default, the domain members automatically change their domain password every 30 days. If you increase this interval significantly, or set it to 0 so that the computers no longer change their passwords, an attacker will have more time to undertake a brute force attack to guess the password of one or more computer accounts.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "System\\CurrentControlSet\\Services\\Netlogon\\Parameters\\MaximumPasswordAge", "ExpectedValue": "30", "RemediateValue": "30", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["Identity Management", "Local Policies-Domain member"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{ed9a6795-2803-4b77-9fc8-04f74aef49ed}", "AZID": "CCE-37614-5", "Name": "Ensure 'Domain member: <PERSON><PERSON> strong (Windows 2000 or later) session key' is set to 'Enabled'", "Description": "When this policy setting is enabled, a secure channel can only be established with domain controllers that are capable of encrypting secure channel data with a strong (128-bit) session key. To enable this policy setting, all domain controllers in the domain must be able to encrypt secure channel data with a strong key, which means all domain controllers must be running Microsoft Windows 2000 or later. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "Session keys that are used to establish secure channel communications between domain controllers and member computers are much stronger in Windows 2000 than they were in previous Microsoft operating systems. Whenever possible, you should take advantage of these stronger session keys to help protect secure channel communications from attacks that attempt to hijack network sessions and eavesdropping. (Eavesdropping is a form of hacking in which network data is read or altered in transit. The data can be modified to hide or change the sender, or be redirected.)", "Impact": "None - this is the default configuration. However, computers will not be able to join Windows NT 4.0 domains, and trusts between Active Directory domains and Windows NT-style domains may not work properly. Also, domain controllers with this setting configured will not allow older pre-Windows 2000 clients (that that do not support this policy setting) to join the domain.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\Netlogon\\Parameters\\RequireStrongKey", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Encryption", "Local Policies-Domain member"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{09ed81b2-8dba-4009-84f9-dcfd6009ed0d}", "AZID": "AZ-WIN-00141", "Name": "Ensure 'Enable insecure guest logons' is set to 'Disabled'", "Description": "This policy setting determines if the SMB client will allow insecure guest logons to an SMB server. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "Insecure guest logons are used by file servers to allow unauthenticated access to shared folders.", "Impact": "The SMB client will reject insecure guest logons.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\LanmanWorkstation\\AllowInsecureGuestAuth", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: SMB", "Administrative Templates (Computer)-Lanman Workstation"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{94f532e6-6a44-45aa-9051-dc786acf6ae0}", "AZID": "CCE-37346-4", "Name": "Ensure 'Enable RPC Endpoint Mapper Client Authentication' is set to 'Enabled' (MS only)", "Description": "This policy setting controls whether RPC clients authenticate with the Endpoint Mapper Service when the call they are making contains authentication information. The Endpoint Mapper Service on computers running Windows NT4 (all service packs) cannot process authentication information supplied in this manner. This policy setting can cause a specific issue with _1-way_ forest trusts if it is applied to the _trusting_ domain DCs (see Microsoft [KB3073942](https:--support.microsoft.com-en-us-kb-3073942)), so we do not recommend applying it to domain controllers. **Note:** This policy will not be applied until the system is rebooted. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "Anonymous access to RPC services could result in accidental disclosure of information to unauthenticated users.", "Impact": "RPC clients will authenticate to the Endpoint Mapper Service for calls that contain authentication information. Clients making such calls will not be able to communicate with the Windows NT4 Server Endpoint Mapper Service.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows NT\\Rpc\\EnableAuthEpResolution", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Network", "Administrative Templates (Computer)-Remote Procedure Call"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{e4f665ec-e8cc-49bb-9a69-58acf5a2e759}", "AZID": "CCE-37843-0", "Name": "Ensure 'Enable Windows NTP Client' is set to 'Enabled'", "Description": "This policy setting specifies whether the Windows NTP Client is enabled. Enabling the Windows NTP Client allows your computer to synchronize its computer clock with other NTP servers. You might want to disable this service if you decide to use a third-party time provider. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "A reliable and accurate account of time is important for a number of services and security requirements, including but not limited to distributed applications, authentication services, multi-user databases and logging services. The use of an NTP client (with secure operation) establishes functional accuracy and is a focal point when reviewing security relevant events", "Impact": "You can set the local computer clock to synchronize time with NTP servers.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\W32Time\\TimeProviders\\NtpClient\\Enabled", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["Time services", "Administrative Templates (Computer)-Time Providers"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{d6d90aed-2a42-45b6-8512-0241f731fdf7}", "AZID": "AZ-WIN-00026", "Name": "Ensure 'Audit Group Membership' is set to 'Success'", "Description": "This policy allows you to audit the group membership information in the user’s logon token. Events in this subcategory are generated on the computer on which a logon session is created. For an interactive logon, the security audit event is generated on the computer that the user logged on to. For a network logon, such as accessing a shared folder on the network, the security audit event is generated on the computer hosting the resource. The recommended state for this setting is: `Success`. **Note:** A Windows 10, Server 2016 or higher OS is required to access and set this value in Group Policy.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9249-69AE-11D9-BED3-************}", "ExpectedValue": "Success", "RemediateValue": "Success", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Logon/Logoff"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{5c532b76-16c0-4a8c-ac67-015b93f458dc}", "AZID": "CCE-37853-9", "Name": "Ensure 'Audit IPsec Driver' is set", "Description": "This subcategory reports on the activities of the Internet Protocol security (IPsec) driver. Events for this subcategory include: - 4960: IPsec dropped an inbound packet that failed an integrity check. If this problem persists, it could indicate a network issue or that packets are being modified in transit to this computer. Verify that the packets sent from the remote computer are the same as those received by this computer. This error might also indicate interoperability problems with other IPsec implementations. - 4961: IPsec dropped an inbound packet that failed a replay check. If this problem persists, it could indicate a replay attack against this computer. - 4962: IPsec dropped an inbound packet that failed a replay check. The inbound packet had too low a sequence number to ensure it was not a replay. - 4963: IPsec dropped an inbound clear text packet that should have been secured. This is usually due to the remote computer changing its IPsec policy without informing this computer. This could also be a spoofing attack attempt. - 4965: IPsec received a packet from a remote computer with an incorrect Security Parameter Index (SPI). This is usually caused by malfunctioning hardware that is corrupting packets. If these errors persist, verify that the packets sent from the remote computer are the same as those received by this computer. This error may also indicate interoperability problems with other IPsec implementations. In that case, if connectivity is not impeded, then these events can be ignored. - 5478: IPsec Services has started successfully. - 5479: IPsec Services has been shut down successfully. The shutdown of IPsec Services can put the computer at greater risk of network attack or expose the computer to potential security risks. - 5480: IPsec Services failed to get the complete list of network interfaces on the computer. This poses a potential security risk because some of the network interfaces may not get the protection provided by the applied IPsec filters. Use the IP Security Monitor snap-in to diagnose the problem. - 5483: IPsec Services failed to initialize RPC server. IPsec Services could not be started. - 5484: IPsec Services has experienced a critical failure and has been shut down. The shutdown of IPsec Services can put the computer at greater risk of network attack or expose the computer to potential security risks. - 5485: IPsec Services failed to process some IPsec filters on a plug-and-play event for network interfaces. This poses a potential security risk because some of the network interfaces may not get the protection provided by the applied IPsec filters. Use the IP Security Monitor snap-in to diagnose the problem. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9213-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-System"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{240c56fa-202b-4583-86d2-3f8fb4cc60fe}", "AZID": "CCE-37166-6", "Name": "Ensure 'Enforce password history' is set to '24 or more password(s)'", "Description": "This policy setting determines the number of renewed, unique passwords that have to be associated with a user account before you can reuse an old password. The value for this policy setting must be between 0 and 24 passwords. The default value for Windows Vista is 0 passwords, but the default setting in a domain is 24 passwords. To maintain the effectiveness of this policy setting, use the Minimum password age setting to prevent users from repeatedly changing their password. The recommended state for this setting is: `24 or more password(s)`.", "Severity": "Critical", "Vulnerability": "The longer a user uses the same password, the greater the chance that an attacker can determine the password through brute force attacks. Also, any accounts that may have been compromised will remain exploitable for as long as the password is left unchanged. If password changes are required but password reuse is not prevented, or if users continually reuse a small number of passwords, the effectiveness of a good password policy is greatly reduced. If you specify a low number for this policy setting, users will be able to use the same small number of passwords repeatedly. If you do not also configure the Minimum password age setting, users might repeatedly change their passwords until they can reuse their original password.", "Impact": "The major impact of this configuration is that users must create a new password every time they are required to change their old one. If users are required to change their passwords to new unique values, there is an increased risk of users who write their passwords somewhere so that they do not forget them. Another risk is that users may create passwords that change incrementally (for example, password01, password02, and so on) to facilitate memorization but make them easier to guess. Also, an excessively low value for the Minimum password age setting will likely increase administrative overhead, because users who forget their passwords might ask the help desk to reset them frequently.", "DataSourceType": "Policy", "DataSourceKey": "[System Access]PasswordHistorySize", "ExpectedValue": "24", "RemediateValue": "24", "Remediate": "false", "ValueType": "UINTEGER", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Identity Management", "Account Policies-Password Policy"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{e6eab28a-1dc8-4fb5-b88b-4e10f239e67c}", "AZID": "CCE-36512-2", "Name": "Ensure 'Enumerate administrator accounts on elevation' is set to 'Disabled'", "Description": "This policy setting controls whether administrator accounts are displayed when a user attempts to elevate a running application. The recommended state for this setting is: `Disabled`.", "Severity": "Important", "Vulnerability": "Users could see the list of administrator accounts, making it slightly easier for a malicious user who has logged onto a console session to try to crack the passwords of those accounts.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\CredUI\\EnumerateAdministrators", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["Identity Management", "Administrative Templates (Computer)-Credential User Interface"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{59a97e33-0055-4209-bdee-78e4510625b1}", "AZID": "CCE-35894-5", "Name": "Ensure 'Enumerate local users on domain-joined computers' is set to 'Disabled'", "Description": "This policy setting allows local users to be enumerated on domain-joined computers. The recommended state for this setting is: `Disabled`.", "Severity": "Informational", "Vulnerability": "A malicious user could use this feature to gather account names of other users, that information could then be used in conjunction with other types of attacks such as guessing passwords or social engineering. The value of this countermeasure is small because a user with domain credentials could gather the same account information using other methods.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\System\\EnumerateLocalUsers", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["Identity Management", "Administrative Templates (Computer)-Logon"], "Filter": ["OSVersion = [WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{3531261f-1644-4d10-9242-8e35ef386a83}", "AZID": "CCE-37877-8", "Name": "Ensure 'Force shutdown from a remote system' is set to 'Administrators'", "Description": "This policy setting allows users to shut down Windows Vista-based computers from remote locations on the network. Anyone who has been assigned this user right can cause a denial of service (DoS) condition, which would make the computer unavailable to service user requests. Therefore, it is recommended that only highly trusted administrators be assigned this user right. The recommended state for this setting is: `Administrators`.", "Severity": "Critical", "Vulnerability": "Any user who can shut down a computer could cause a DoS condition to occur. Therefore, this user right should be tightly restricted.", "Impact": "If you remove the Force shutdown from a remote system user right from the Server Operator group you could limit the abilities of users who are assigned to specific administrative roles in your environment. You should confirm that delegated activities will not be adversely affected.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeRemoteShutdownPrivilege", "ExpectedValue": "Administrators", "RemediateValue": "Administrators", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Network", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{46e66c68-266e-4bdc-9ebe-4c5164c0acfe}", "AZID": "CCE-37639-2", "Name": "Ensure 'Generate security audits' is set to 'LOCAL SERVICE, NETWORK SERVICE'", "Description": "This policy setting determines which users or processes can generate audit records in the Security log. The recommended state for this setting is: `LOCAL SERVICE, NETWORK SERVICE`. **Note:** A Member Server that holds the _Web Server (IIS)_ Role with _Web Server_ Role Service will require a special exception to this recommendation, to allow IIS application pool(s) to be granted this user right. **Note #2:** A Member Server that holds the _Active Directory Federation Services_ Role will require a special exception to this recommendation, to allow the `NT SERVICEADFSSrv` and `NT SERVICEDRS` services, as well as the associated Active Directory Federation Services service account, to be granted this user right.", "Severity": "Critical", "Vulnerability": "An attacker could use this capability to create a large number of audited events, which would make it more difficult for a system administrator to locate any illicit activity. Also, if the event log is configured to overwrite events as needed, any evidence of unauthorized activities could be overwritten by a large number of unrelated events.", "Impact": "On most computers, this is the default configuration and there will be no negative impact. However, if you have installed the _Web Server (IIS)_ Role with _Web Services_ Role Service, you will need to allow the IIS application pool(s) to be granted this User Right Assignment.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeAuditPrivilege", "ExpectedValue": "Local Service, Network Service, IIS APPPOOL\\DefaultAppPool", "RemediateValue": "Local Service, Network Service, IIS APPPOOL\\DefaultAppPool", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "LESSOREQUAL", "Categories": ["Logging & Auditing", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{98372fa4-c0dc-499a-a218-abc96fc04684}", "AZID": "CCE-38326-5", "Name": "Ensure 'Increase scheduling priority' is set to 'Administrators'", "Description": "This policy setting determines whether users can increase the base priority class of a process. (It is not a privileged operation to increase relative priority within a priority class.) This user right is not required by administrative tools that are supplied with the operating system but might be required by software development tools. The recommended state for this setting is: `Administrators`.", "Severity": "Important", "Vulnerability": "A user who is assigned this user right could increase the scheduling priority of a process to Real-Time, which would leave little processing time for all other processes and could lead to a DoS condition.", "Impact": "None - this is the default configuration.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeIncreaseBasePriorityPrivilege", "ExpectedValue": "Administrators", "RemediateValue": "Administrators", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{9e11215f-9b0b-4ca6-ad5b-d1a0c989af36}", "AZID": "CCE-36056-0", "Name": "Ensure 'Interactive logon: Do not display last user name' is set to 'Enabled'", "Description": "This policy setting determines whether the account name of the last user to log on to the client computers in your organization will be displayed in each computer's respective Windows logon screen. Enable this policy setting to prevent intruders from collecting account names visually from the screens of desktop or laptop computers in your organization. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "An attacker with access to the console (for example, someone with physical access or someone who is able to connect to the server through Terminal Services) could view the name of the last user who logged on to the server. The attacker could then try to guess the password, use a dictionary, or use a brute-force attack to try and log on.", "Impact": "The name of the last user to successfully log on is not be displayed in the Windows logon screen.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\DontDisplayLastUserName", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["Identity Management", "Local Policies-Interactive logon"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{c2e85522-5e4f-4295-8111-5b2ab815af32}", "AZID": "CCE-37637-6", "Name": "Ensure 'Interactive logon: Do not require CTRL+ALT+DEL' is set to 'Disabled'", "Description": "This policy setting determines whether users must press CTRL+ALT+DEL before they log on. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "Microsoft developed this feature to make it easier for users with certain types of physical impairments to log on to computers that run Windows. If users are not required to press CTRL+ALT+DEL, they are susceptible to attacks that attempt to intercept their passwords. If CTRL+ALT+DEL is required before logon, user passwords are communicated by means of a trusted path. An attacker could install a Trojan horse program that looks like the standard Windows logon dialog box and capture the user's password. The attacker would then be able to log on to the compromised account with whatever level of privilege that user has.", "Impact": "Users must press CTRL+ALT+DEL before they log on to Windows unless they use a smart card for Windows logon. A smart card is a tamper-proof device that stores security information.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\DisableCAD", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["Identity Management", "Local Policies-Interactive logon"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{50f4447d-0bdd-4e8c-ba06-2e0b22ec5d04}", "AZID": "CCE-36318-4", "Name": "Ensure 'Load and unload device drivers' is set to 'Administrators'", "Description": "This policy setting allows users to dynamically load a new device driver on a system. An attacker could potentially use this capability to install malicious code that appears to be a device driver. This user right is required for users to add local printers or printer drivers in Windows Vista. The recommended state for this setting is: `Administrators`.", "Severity": "Important", "Vulnerability": "Device drivers run as highly privileged code. A user who has the Load and unload device drivers user right could unintentionally install malicious code that masquerades as a device driver. Administrators should exercise greater care and install only drivers with verified digital signatures.", "Impact": "If you remove the Load and unload device drivers user right from the Print Operators group or other accounts you could limit the abilities of users who are assigned to specific administrative roles in your environment. You should ensure that delegated tasks will not be negatively affected.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeLoadDriverPrivilege", "ExpectedValue": "Administrators", "RemediateValue": "Administrators", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{6e635d8c-3496-4c66-b734-c46ebccc5d38}", "AZID": "CCE-36495-0", "Name": "Ensure 'Lock pages in memory' is set to 'No One'", "Description": "This policy setting allows a process to keep data in physical memory, which prevents the system from paging the data to virtual memory on disk. If this user right is assigned, significant degradation of system performance can occur. The recommended state for this setting is: `No One`.", "Severity": "Important", "Vulnerability": "Users with the Lock pages in memory user right could assign physical memory to several processes, which could leave little or no RAM for other processes and result in a DoS condition.", "Impact": "None - this is the default configuration.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeLockMemoryPrivilege", "ExpectedValue": "No One", "RemediateValue": "No One", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{ee122129-f77a-4f65-90c6-b6b0e445d5a4}", "AZID": "CCE-37167-4", "Name": "Ensure 'Maximum password age' is set to '70 or fewer days, but not 0'", "Description": "This policy setting defines how long a user can use their password before it expires. Values for this policy setting range from 0 to 999 days. If you set the value to 0, the password will never expire. Because attackers can crack passwords, the more frequently you change the password the less opportunity an attacker has to use a cracked password. However, the lower this value is set, the higher the potential for an increase in calls to help desk support due to users having to change their password or forgetting which password is current. The recommended state for this setting is `60 or fewer days, but not 0`.", "Severity": "Critical", "Vulnerability": "The longer a password exists the higher the likelihood that it will be compromised by a brute force attack, by an attacker gaining general knowledge about the user, or by the user sharing the password. Configuring the Maximum password age setting to 0 so that users are never required to change their passwords is a major security risk because that allows a compromised password to be used by the malicious user for as long as the valid user is authorized access.", "Impact": "If the Maximum password age setting is too low, users are required to change their passwords very often. Such a configuration can reduce security in the organization, because users might write their passwords in an insecure location or lose them. If the value for this policy setting is too high, the level of security within an organization is reduced because it allows potential attackers more time in which to discover user passwords or to use compromised accounts.", "DataSourceType": "Policy", "DataSourceKey": "[System Access]MaximumPasswordAge", "ExpectedValue": "1,70", "RemediateValue": "1,70", "Remediate": "false", "ValueType": "UINTEGER", "AnalyzeOperation": "RANGE", "Categories": ["Identity Management", "Account Policies-Password Policy"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{41a8be7d-69bd-48f4-ae77-9568cf7b15d1}", "AZID": "CCE-36325-9", "Name": "Ensure 'Microsoft network client: Digitally sign communications (always)' is set to 'Enabled'", "Description": "This policy setting determines whether packet signing is required by the SMB client component. **Note:** When Windows Vista-based computers have this policy setting enabled and they connect to file or print shares on remote servers, it is important that the setting is synchronized with its companion setting, **Microsoft network server: Digitally sign communications (always)**, on those servers. For more information about these settings, see the \"Microsoft network client and server: Digitally sign communications (four related settings)\" section in Chapter 5 of the Threats and Countermeasures guide. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "Session hijacking uses tools that allow attackers who have access to the same network as the client or server to interrupt, end, or steal a session in progress. Attackers can potentially intercept and modify unsigned SMB packets and then modify the traffic and forward it so that the server might perform undesirable actions. Alternatively, the attacker could pose as the server or client after legitimate authentication and gain unauthorized access to data. SMB is the resource sharing protocol that is supported by many Windows operating systems. It is the basis of NetBIOS and many other protocols. SMB signatures authenticate both users and the servers that host the data. If either side fails the authentication process, data transmission will not take place.", "Impact": "The Microsoft network client will not communicate with a Microsoft network server unless that server agrees to perform SMB packet signing. The Windows 2000 Server, Windows 2000 Professional, Windows Server 2003, Windows XP Professional and Windows Vista implementations of the SMB file and print sharing protocol support mutual authentication, which prevents session hijacking attacks and supports message authentication to prevent man-in-the-middle attacks. SMB signing provides this authentication by placing a digital signature into each SMB, which is then verified by both the client and the server. Implementation of SMB signing may negatively affect performance, because each packet needs to be signed and verified. If these settings are enabled on a server that is performing multiple roles, such as a small business server that is serving as a domain controller, file server, print server, and application server performance may be substantially slowed. Additionally, if you configure computers to ignore all unsigned SMB communications, older applications and operating systems will not be able to connect. However, if you completely disable all SMB signing, computers will be vulnerable to session hijacking attacks. When SMB signing policies are enabled on domain controllers running Windows Server 2003 and member computers running Windows Vista SP1 or Windows Server 2008 group policy processing will fail. A hotfix is available from Microsoft that resolves this issue; see Microsoft Knowledge Base article 950876 for more details: [Group Policy settings are not applied on member computers that are running Windows Server 2008 or Windows Vista SP1 when certain SMB signing policies are enabled](https:--support.microsoft.com-en-us-kb-950876).", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\LanmanWorkstation\\Parameters\\RequireSecuritySignature", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: SMB", "Local Policies-Microsoft network client"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{342046f5-c7d3-46b7-96db-7e4be82542d3}", "AZID": "CCE-36269-9", "Name": "Ensure 'Microsoft network client: Digitally sign communications (if server agrees)' is set to 'Enabled'", "Description": "This policy setting determines whether the SMB client will attempt to negotiate SMB packet signing. **Note:** Enabling this policy setting on SMB clients on your network makes them fully effective for packet signing with all clients and servers in your environment. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "Session hijacking uses tools that allow attackers who have access to the same network as the client or server to interrupt, end, or steal a session in progress. Attackers can potentially intercept and modify unsigned SMB packets and then modify the traffic and forward it so that the server might perform undesirable actions. Alternatively, the attacker could pose as the server or client after legitimate authentication and gain unauthorized access to data. SMB is the resource sharing protocol that is supported by many Windows operating systems. It is the basis of NetBIOS and many other protocols. SMB signatures authenticate both users and the servers that host the data. If either side fails the authentication process, data transmission will not take place.", "Impact": "None - this is the default behavior. The Windows 2000 Server, Windows 2000 Professional, Windows Server 2003, Windows XP Professional and Windows Vista implementations of the SMB file and print sharing protocol support mutual authentication, which prevents session hijacking attacks and supports message authentication to prevent man-in-the-middle attacks. SMB signing provides this authentication by placing a digital signature into each SMB, which is then verified by both the client and the server. Implementation of SMB signing may negatively affect performance, because each packet needs to be signed and verified. If these settings are enabled on a server that is performing multiple roles, such as a small business server that is serving as a domain controller, file server, print server, and application server performance may be substantially slowed. Additionally, if you configure computers to ignore all unsigned SMB communications, older applications and operating systems will not be able to connect. However, if you completely disable all SMB signing, computers will be vulnerable to session hijacking attacks. When SMB signing policies are enabled on domain controllers running Windows Server 2003 and member computers running Windows Vista SP1 or Windows Server 2008 group policy processing will fail. A hotfix is available from Microsoft that resolves this issue; see Microsoft Knowledge Base article 950876 for more details: [Group Policy settings are not applied on member computers that are running Windows Server 2008 or Windows Vista SP1 when certain SMB signing policies are enabled](https:--support.microsoft.com-en-us-kb-950876).", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\LanmanWorkstation\\Parameters\\EnableSecuritySignature", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: SMB", "Local Policies-Microsoft network client"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{a14a2808-588b-4233-b342-9dc1cecf2b0a}", "AZID": "CCE-37863-8", "Name": "Ensure 'Microsoft network client: Send unencrypted password to third-party SMB servers' is set to 'Disabled'", "Description": "This policy setting determines whether the SMB redirector will send plaintext passwords during authentication to third-party SMB servers that do not support password encryption. It is recommended that you disable this policy setting unless there is a strong business case to enable it. If this policy setting is enabled, unencrypted passwords will be allowed across the network. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "If you enable this policy setting, the server can transmit passwords in plaintext across the network to other computers that offer SMB services, which is a significant security risk. These other computers may not use any of the SMB security mechanisms that are included with Windows Server 2003.", "Impact": "None - this is the default configuration. Some very old applications and operating systems such as MS-DOS, Windows for Workgroups 3.11, and Windows 95a may not be able to communicate with the servers in your organization by means of the SMB protocol.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\LanmanWorkstation\\Parameters\\EnablePlainTextPassword", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: SMB", "Local Policies-Microsoft network client"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{e1174067-f117-4d7f-9584-fd93eedd566f}", "AZID": "CCE-38237-4", "Name": "Ensure '<PERSON><PERSON>' is set to 'Success'", "Description": "This subcategory reports when a user logs off from the system. These events occur on the accessed computer. For interactive logons, the generation of these events occurs on the computer that is logged on to. If a network logon takes place to access a share, these events generate on the computer that hosts the accessed resource. If you configure this setting to No auditing, it is difficult or impossible to determine which user has accessed or attempted to access organization computers. Events for this subcategory include: - 4634: An account was logged off. - 4647: <PERSON><PERSON> initiated logoff. The recommended state for this setting is: `Success`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9216-69AE-11D9-BED3-************}", "ExpectedValue": "Success", "RemediateValue": "Success", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Logon/Logoff"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{677006be-eb05-4e75-950d-c8e782e7cea2}", "AZID": "CCE-38046-9", "Name": "Ensure 'Microsoft network server: Amount of idle time required before suspending session' is set to '15 or fewer minute(s), but not 0'", "Description": "This policy setting allows you to specify the amount of continuous idle time that must pass in an SMB session before the session is suspended because of inactivity. Administrators can use this policy setting to control when a computer suspends an inactive SMB session. If client activity resumes, the session is automatically reestablished. A value of 0 appears to allow sessions to persist indefinitely. The maximum value is 99999, which is over 69 days; in effect, this value disables the setting. The recommended state for this setting is: `15 or fewer minute(s), but not 0`.", "Severity": "Critical", "Vulnerability": "Each SMB session consumes server resources, and numerous null sessions will slow the server or possibly cause it to fail. An attacker could repeatedly establish SMB sessions until the server's SMB services become slow or unresponsive.", "Impact": "There will be little impact because SMB sessions will be re-established automatically if the client resumes activity.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\LanManServer\\Parameters\\AutoDisconnect", "ExpectedValue": "1,15", "RemediateValue": "1,15", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "RANGE", "Categories": ["ASR: SMB", "Local Policies-Microsoft network server"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{032b5976-1c4b-4c68-bc5d-0c65e35306b2}", "AZID": "CCE-37864-6", "Name": "Ensure 'Microsoft network server: Digitally sign communications (always)' is set to 'Enabled'", "Description": "This policy setting determines whether packet signing is required by the SMB server component. Enable this policy setting in a mixed environment to prevent downstream clients from using the workstation as a network server. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "Session hijacking uses tools that allow attackers who have access to the same network as the client or server to interrupt, end, or steal a session in progress. Attackers can potentially intercept and modify unsigned SMB packets and then modify the traffic and forward it so that the server might perform undesirable actions. Alternatively, the attacker could pose as the server or client after legitimate authentication and gain unauthorized access to data. SMB is the resource sharing protocol that is supported by many Windows operating systems. It is the basis of NetBIOS and many other protocols. SMB signatures authenticate both users and the servers that host the data. If either side fails the authentication process, data transmission will not take place.", "Impact": "The Microsoft network server will not communicate with a Microsoft network client unless that client agrees to perform SMB packet signing. The Windows 2000 Server, Windows 2000 Professional, Windows Server 2003, Windows XP Professional and Windows Vista implementations of the SMB file and print sharing protocol support mutual authentication, which prevents session hijacking attacks and supports message authentication to prevent man-in-the-middle attacks. SMB signing provides this authentication by placing a digital signature into each SMB, which is then verified by both the client and the server. Implementation of SMB signing may negatively affect performance, because each packet needs to be signed and verified. If these settings are enabled on a server that is performing multiple roles, such as a small business server that is serving as a domain controller, file server, print server, and application server performance may be substantially slowed. Additionally, if you configure computers to ignore all unsigned SMB communications, older applications and operating systems will not be able to connect. However, if you completely disable all SMB signing, computers will be vulnerable to session hijacking attacks. When SMB signing policies are enabled on domain controllers running Windows Server 2003 and member computers running Windows Vista SP1 or Windows Server 2008 group policy processing will fail. A hotfix is available from Microsoft that resolves this issue; see Microsoft Knowledge Base article 950876 for more details: [Group Policy settings are not applied on member computers that are running Windows Server 2008 or Windows Vista SP1 when certain SMB signing policies are enabled](https:--support.microsoft.com-en-us-kb-950876).", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\LanManServer\\Parameters\\RequireSecuritySignature", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: SMB", "Local Policies-Microsoft network server"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{b625a003-d015-436e-89fb-fb2dfe71ae0f}", "AZID": "CCE-35988-5", "Name": "Ensure 'Microsoft network server: Digitally sign communications (if client agrees)' is set to 'Enabled'", "Description": "This policy setting determines whether the SMB server will negotiate SMB packet signing with clients that request it. If no signing request comes from the client, a connection will be allowed without a signature if the **Microsoft network server: Digitally sign communications (always)** setting is not enabled. **Note:** Enable this policy setting on SMB clients on your network to make them fully effective for packet signing with all clients and servers in your environment. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "Session hijacking uses tools that allow attackers who have access to the same network as the client or server to interrupt, end, or steal a session in progress. Attackers can potentially intercept and modify unsigned SMB packets and then modify the traffic and forward it so that the server might perform undesirable actions. Alternatively, the attacker could pose as the server or client after legitimate authentication and gain unauthorized access to data. SMB is the resource sharing protocol that is supported by many Windows operating systems. It is the basis of NetBIOS and many other protocols. SMB signatures authenticate both users and the servers that host the data. If either side fails the authentication process, data transmission will not take place.", "Impact": "The Microsoft network server will negotiate SMB packet signing as requested by the client. That is, if packet signing has been enabled on the client, packet signing will be negotiated. The Windows 2000 Server, Windows 2000 Professional, Windows Server 2003, Windows XP Professional and Windows Vista implementations of the SMB file and print sharing protocol support mutual authentication, which prevents session hijacking attacks and supports message authentication to prevent man-in-the-middle attacks. SMB signing provides this authentication by placing a digital signature into each SMB, which is then verified by both the client and the server. Implementation of SMB signing may negatively affect performance, because each packet needs to be signed and verified. If these settings are enabled on a server that is performing multiple roles, such as a small business server that is serving as a domain controller, file server, print server, and application server performance may be substantially slowed. Additionally, if you configure computers to ignore all unsigned SMB communications, older applications and operating systems will not be able to connect. However, if you completely disable all SMB signing, computers will be vulnerable to session hijacking attacks. When SMB signing policies are enabled on domain controllers running Windows Server 2003 and member computers running Windows Vista SP1 or Windows Server 2008 group policy processing will fail. A hotfix is available from Microsoft that resolves this issue; see Microsoft Knowledge Base article 950876 for more details: [Group Policy settings are not applied on member computers that are running Windows Server 2008 or Windows Vista SP1 when certain SMB signing policies are enabled](https:--support.microsoft.com-en-us-kb-950876).", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\LanManServer\\Parameters\\EnableSecuritySignature", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: SMB", "Local Policies-Microsoft network server"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{********-6b73-4cdd-906d-702e00bae698}", "AZID": "CCE-37972-7", "Name": "Ensure 'Microsoft network server: Disconnect clients when logon hours expire' is set to 'Enabled'", "Description": "This security setting determines whether to disconnect users who are connected to the local computer outside their user account's valid logon hours. This setting affects the Server Message Block (SMB) component. If you enable this policy setting you should also enable **Network security: Force logoff when logon hours expire** (Rule ********). If your organization configures logon hours for users, this policy setting is necessary to ensure they are effective. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "If your organization configures logon hours for users, then it makes sense to enable this policy setting. Otherwise, users who should not have access to network resources outside of their logon hours may actually be able to continue to use those resources with sessions that were established during allowed hours.", "Impact": "None - this is the default configuration. If logon hours are not used in your organization, this policy setting will have no impact. If logon hours are used, existing user sessions will be forcibly terminated when their logon hours expire.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\LanManServer\\Parameters\\EnableForcedLogoff", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: SMB", "Local Policies-Microsoft network server"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{3f452c5c-0447-4104-8d6f-6a01719a5d8e}", "AZID": "CCE-38338-0", "Name": "Ensure 'Minimize the number of simultaneous connections to the Internet or a Windows Domain' is set to 'Enabled'", "Description": "This policy setting prevents computers from connecting to both a domain based network and a non-domain based network at the same time. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "Blocking simultaneous connections can help prevent a user unknowingly allowing network traffic to flow between the Internet and the corporate network.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\WcmSvc\\GroupPolicy\\fMinimizeConnections", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Network", "Administrative Templates (Computer)-Windows Connection Manager"], "Filter": ["OSVersion = [WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{59c0b697-713b-47d8-aa67-83d22bd704d7}", "AZID": "CCE-37073-4", "Name": "Ensure 'Minimum password age' is set to '1 or more day(s)'", "Description": "This policy setting determines the number of days that you must use a password before you can change it. The range of values for this policy setting is between 1 and 999 days. (You may also set the value to 0 to allow immediate password changes.) The default value for this setting is 0 days. The recommended state for this setting is: `1 or more day(s)`.", "Severity": "Critical", "Vulnerability": "Users may have favorite passwords that they like to use because they are easy to remember and they believe that their password choice is secure from compromise. Unfortunately, passwords are compromised and if an attacker is targeting a specific individual user account, with foreknowledge of data about that user, reuse of old passwords can cause a security breach. To address password reuse a combination of security settings is required. Using this policy setting with the Enforce password history setting prevents the easy reuse of old passwords. For example, if you configure the Enforce password history setting to ensure that users cannot reuse any of their last 12 passwords, they could change their password 13 times in a few minutes and reuse the password they started with, unless you also configure the Minimum password age setting to a number that is greater than 0. You must configure this policy setting to a number that is greater than 0 for the Enforce password history setting to be effective.", "Impact": "If an administrator sets a password for a user but wants that user to change the password when the user first logs on, the administrator must select the User must change password at next logon check box, or the user will not be able to change the password until the next day.", "DataSourceType": "Policy", "DataSourceKey": "[System Access]MinimumPasswordAge", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "UINTEGER", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Identity Management", "Account Policies-Password Policy"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{87c21a63-faf5-4502-9cdf-7fac5660b018}", "AZID": "CCE-36534-6", "Name": "Ensure 'Minimum password length' is set to '14 or more character(s)'", "Description": "This policy setting determines the least number of characters that make up a password for a user account. There are many different theories about how to determine the best password length for an organization, but perhaps \"pass phrase\" is a better term than \"password.\" In Microsoft Windows 2000 or later, pass phrases can be quite long and can include spaces. Therefore, a phrase such as \"I want to drink a $5 milkshake\" is a valid pass phrase; it is a considerably stronger password than an 8 or 10 character string of random numbers and letters, and yet is easier to remember. Users must be educated about the proper selection and maintenance of passwords, especially with regard to password length. In enterprise environments, the ideal value for the Minimum password length setting is 14 characters, however you should adjust this value to meet your organization's business requirements. The recommended state for this setting is: `14 or more character(s)`.", "Severity": "Critical", "Vulnerability": "Types of password attacks include dictionary attacks (which attempt to use common words and phrases) and brute force attacks (which try every possible combination of characters). Also, attackers sometimes try to obtain the account database so they can use tools to discover the accounts and passwords.", "Impact": "Requirements for extremely long passwords can actually decrease the security of an organization, because users might leave the information in an insecure location or lose it. If very long passwords are required, mistyped passwords could cause account lockouts and increase the volume of help desk calls. If your organization has issues with forgotten passwords due to password length requirements, consider teaching your users about pass phrases, which are often easier to remember and, due to the larger number of character combinations, much harder to discover. **Note:** Older versions of Windows such as Windows 98 and Windows NT 4.0 do not support passwords that are longer than 14 characters. Computers that run these older operating systems are unable to authenticate with computers or domains that use accounts that require long passwords.", "DataSourceType": "Policy", "DataSourceKey": "[System Access]MinimumPasswordLength", "ExpectedValue": "14", "RemediateValue": "14", "Remediate": "false", "ValueType": "UINTEGER", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Identity Management", "Account Policies-Password Policy"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{25c07385-c03d-4f61-b4d2-13852635abb7}", "AZID": "CCE-36054-5", "Name": "Ensure 'Modify an object label' is set to 'No One'", "Description": "This privilege determines which user accounts can modify the integrity label of objects, such as files, registry keys, or processes owned by other users. Processes running under a user account can modify the label of an object owned by that user to a lower level without this privilege. The recommended state for this setting is: `No One`.", "Severity": "Important", "Vulnerability": "By modifying the integrity label of an object owned by another user a malicious user may cause them to execute code at a higher level of privilege than intended.", "Impact": "None - this is the default configuration.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeRelabelPrivilege", "ExpectedValue": "No One", "RemediateValue": "No One", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{910405d5-3ee9-427c-baf1-77c69c7c209d}", "AZID": "CCE-38113-7", "Name": "Ensure 'Modify firmware environment values' is set to 'Administrators'", "Description": "This policy setting allows users to configure the system-wide environment variables that affect hardware configuration. This information is typically stored in the Last Known Good Configuration. Modification of these values and could lead to a hardware failure that would result in a denial of service condition. The recommended state for this setting is: `Administrators`.", "Severity": "Important", "Vulnerability": "Anyone who is assigned the Modify firmware environment values user right could configure the settings of a hardware component to cause it to fail, which could lead to data corruption or a DoS condition.", "Impact": "None - this is the default configuration.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeSystemEnvironmentPrivilege", "ExpectedValue": "Administrators", "RemediateValue": "Administrators", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{9f9f321e-6dc6-4f44-ae42-6b77bf57ea2a}", "AZID": "CCE-36077-6", "Name": "Ensure 'Network access: Do not allow anonymous enumeration of SAM accounts and shares' is set to 'Enabled' (MS only)", "Description": "This policy setting controls the ability of anonymous users to enumerate SAM accounts as well as shares. If you enable this policy setting, anonymous users will not be able to enumerate domain account user names and network share names on the systems in your environment. The recommended state for this setting is: `Enabled`. **Note:** This policy has no effect on domain controllers.", "Severity": "Critical", "Vulnerability": "An unauthorized user could anonymously list account names and shared resources and use the information to attempt to guess passwords or perform social engineering attacks. (Social engineering attacks try to deceive users in some way to obtain passwords or some form of security information.)", "Impact": "It will be impossible to establish trusts with Windows NT 4.0-based domains. Also, client computers that run older versions of the Windows operating system such as Windows NT 3.51 and Windows 95 will experience problems when they try to use resources on the server. Users who access file and print servers anonymously will be unable to list the shared network resources on those servers; the users will have to authenticate before they can view the lists of shared folders and printers. However, even with this policy setting enabled, anonymous users will have access to resources with permissions that explicitly include the built-in group, `ANONYMOUS LOGON`.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Lsa\\RestrictAnonymous", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Network", "Local Policies-Network access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{19c3b4d2-3876-4ab6-ada3-b29896903b97}", "AZID": "CCE-36316-8", "Name": "Ensure 'Network access: Do not allow anonymous enumeration of SAM accounts' is set to 'Enabled' (MS only)", "Description": "This policy setting controls the ability of anonymous users to enumerate the accounts in the Security Accounts Manager (SAM). If you enable this policy setting, users with anonymous connections will not be able to enumerate domain account user names on the systems in your environment. This policy setting also allows additional restrictions on anonymous connections. The recommended state for this setting is: `Enabled`. **Note:** This policy has no effect on domain controllers.", "Severity": "Critical", "Vulnerability": "An unauthorized user could anonymously list account names and use the information to attempt to guess passwords or perform social engineering attacks. (Social engineering attacks try to deceive users in some way to obtain passwords or some form of security information.)", "Impact": "None - this is the default configuration. It will be impossible to establish trusts with Windows NT 4.0-based domains. Also, client computers that run older versions of the Windows operating system such as Windows NT 3.51 and Windows 95 will experience problems when they try to use resources on the server.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Lsa\\RestrictAnonymousSAM", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Network", "Local Policies-Network access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{f97fe90f-c009-4139-8562-9893e9c49b44}", "AZID": "CCE-36148-5", "Name": "Ensure 'Network access: Let Everyone permissions apply to anonymous users' is set to 'Disabled'", "Description": "This policy setting determines what additional permissions are assigned for anonymous connections to the computer. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "An unauthorized user could anonymously list account names and shared resources and use the information to attempt to guess passwords, perform social engineering attacks, or launch DoS attacks.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Lsa\\EveryoneIncludesAnonymous", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Network", "Local Policies-Network access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{f55109a7-2248-4c55-a7b0-bebdcb9530d5}", "AZID": "CCE-36021-4", "Name": "Ensure 'Network access: Restrict anonymous access to Named Pipes and Shares' is set to 'Enabled'", "Description": "When enabled, this policy setting restricts anonymous access to only those shares and pipes that are named in the `Network access: Named pipes that can be accessed anonymously` and `Network access: Shares that can be accessed anonymously` settings. This policy setting controls null session access to shares on your computers by adding `RestrictNullSessAccess` with the value `1` in the `HKEY_LOCAL_MACHINESystemCurrentControlSetServicesLanManServerParameters` registry key. This registry value toggles null session shares on or off to control whether the server service restricts unauthenticated clients' access to named resources. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "Null sessions are a weakness that can be exploited through shares (including the default shares) on computers in your environment.", "Impact": "None - this is the default configuration. If you choose to enable this setting and are supporting Windows NT 4.0 domains, you should check if any of the named pipes are required to maintain trust relationships between the domains, and then add the pipe to the **Network access: Named pipes that can be accessed anonymously** list: - COMNAP: SNA session access - COMNODE: SNA session access - SQLQUERY: SQL instance access - SPOOLSS: Spooler service - LLSRPC: License Logging service - NETLOGON: Net Logon service - LSARPC: LSA access - SAMR: Remote access to SAM objects - BROWSER: Computer Browser service Previous to the release of Windows Server 2003 with Service Pack 1 (SP1) these named pipes were allowed anonymous access by default, but with the increased hardening in Windows Server 2003 with SP1 these pipes must be explicitly added if needed.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\LanManServer\\Parameters\\RestrictNullSessAccess", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Network", "Local Policies-Network access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{055d1156-a0e1-471b-a704-77b473006879}", "AZID": "AZ-WIN-00142", "Name": "Ensure 'Network access: Restrict clients allowed to make remote calls to SAM' is set to 'Administrators: Remote Access: Allow' (MS only)", "Description": "This policy setting allows you to restrict remote RPC connections to SAM. The recommended state for this setting is: `Administrators: Remote Access: Allow`. **Note:** A Windows 10 R1607, Server 2016 or higher OS is required to access and set this value in Group Policy.", "Severity": "Critical", "Vulnerability": "To ensure that an unauthorized user cannot anonymously list local account names or groups and use the information to attempt to guess passwords or perform social engineering attacks. (Social engineering attacks try to deceive users in some way to obtain passwords or some form of security information.)", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Lsa\\RestrictRemoteSAM", "ExpectedValue": "O:BAG:BAD:(A;;RC;;;BA)", "RemediateValue": "O:BAG:BAD:(A;;RC;;;BA)", "Remediate": "false", "ValueType": "REG_SZ", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Network", "Local Policies-Network access"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{5a9eb0c4-37c8-44d4-9816-7476d1aaaa7d}", "AZID": "CCE-38095-6", "Name": "Ensure 'Network access: Shares that can be accessed anonymously' is set to 'None'", "Description": "This policy setting determines which network shares can be accessed by anonymous users. The default configuration for this policy setting has little effect because all users have to be authenticated before they can access shared resources on the server. The recommended state for this setting is: `` (i.e. None).", "Severity": "Critical", "Vulnerability": "It is very dangerous to allow any values in this setting. Any shares that are listed can be accessed by any network user, which could lead to the exposure or corruption of sensitive data.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\LanManServer\\Parameters\\NullSessionShares", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_MULTI_SZ", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Network", "Local Policies-Network access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{3e42b5fc-08b2-4a9a-ad80-dafe9033cbc3}", "AZID": "CCE-37623-6", "Name": "Ensure 'Network access: Sharing and security model for local accounts' is set to 'Classic - local users authenticate as themselves'", "Description": "This policy setting determines how network logons that use local accounts are authenticated. The Classic option allows precise control over access to resources, including the ability to assign different types of access to different users for the same resource. The Guest only option allows you to treat all users equally. In this context, all users authenticate as <PERSON> only to receive the same access level to a given resource. The recommended state for this setting is: `Classic - local users authenticate as themselves`. **Note:** This setting does not affect interactive logons that are performed remotely by using such services as Telnet or Remote Desktop Services (formerly called Terminal Services).", "Severity": "Critical", "Vulnerability": "With the Guest only model, any user who can authenticate to your computer over the network does so with guest privileges, which probably means that they will not have write access to shared resources on that computer. Although this restriction does increase security, it makes it more difficult for authorized users to access shared resources on those computers because ACLs on those resources must include access control entries (ACEs) for the Guest account. With the Classic model, local accounts should be password protected. Otherwise, if Guest access is enabled, anyone can use those user accounts to access shared system resources.", "Impact": "None - this is the default configuration for domain-joined computers.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Lsa\\ForceGuest", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Network", "Local Policies-Network access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{0a978f4b-7b3c-4360-9d19-d5c8e422344a}", "AZID": "CCE-38341-4", "Name": "Ensure 'Network security: Allow Local System to use computer identity for NTLM' is set to 'Enabled'", "Description": "This policy setting determines whether Local System services that use Negotiate when reverting to NTLM authentication can use the computer identity. This policy is supported on at least Windows 7 or Windows Server 2008 R2. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "When connecting to computers running versions of Windows earlier than Windows Vista or Windows Server 2008, services running as Local System and using SPNEGO (Negotiate) that revert to NTLM use the computer identity. In Windows 7, if you are connecting to a computer running Windows Server 2008 or Windows Vista, then a system service uses either the computer identity or a NULL session. When connecting with a NULL session, a system-generated session key is created, which provides no protection but allows applications to sign and encrypt data without errors. When connecting with the computer identity, both signing and encryption is supported in order to provide data protection.", "Impact": "Services running as Local System that use Negotiate when reverting to NTLM authentication will use the computer identity. This might cause some authentication requests between Windows operating systems to fail and log an error.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Lsa\\UseMachineId", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Network", "Local Policies-Network security"], "Filter": ["OSVersion = [WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{0b2803c7-33ac-4407-80f0-f09940bbe940}", "AZID": "CCE-37035-3", "Name": "Ensure 'Network security: Allow LocalSystem NULL session fallback' is set to 'Disabled'", "Description": "This policy setting determines whether NTLM is allowed to fall back to a NULL session when used with LocalSystem. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "NULL sessions are less secure because by definition they are unauthenticated.", "Impact": "Any applications that require NULL sessions for LocalSystem will not work as designed.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Lsa\\MSV1_0\\AllowNullSessionFallback", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Network", "Local Policies-Network security"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{8ad78d25-6140-4899-9565-e053ce7d9a66}", "AZID": "CCE-38047-7", "Name": "Ensure 'Network Security: Allow PKU2U authentication requests to this computer to use online identities' is set to 'Disabled'", "Description": "This setting determines if online identities are able to authenticate to this computer. The Public Key Cryptography Based User-to-User (PKU2U) protocol introduced in Windows 7 and Windows Server 2008 R2 is implemented as a security support provider (SSP). The SSP enables peer-to-peer authentication, particularly through the Windows 7 media and file sharing feature called Homegroup, which permits sharing between computers that are not members of a domain. With PKU2U, a new extension was introduced to the Negotiate authentication package, `Spnego.dll`. In previous versions of Windows, Negotiate decided whether to use Kerberos or NTLM for authentication. The extension SSP for Negotiate, `Negoexts.dll`, which is treated as an authentication protocol by Windows, supports Microsoft SSPs including PKU2U. When computers are configured to accept authentication requests by using online IDs, `Negoexts.dll` calls the PKU2U SSP on the computer that is used to log on. The PKU2U SSP obtains a local certificate and exchanges the policy between the peer computers. When validated on the peer computer, the certificate within the metadata is sent to the logon peer for validation and associates the user's certificate to a security token and the logon process completes. The recommended state for this setting is: `Disabled`.", "Severity": "Important", "Vulnerability": "The PKU2U protocol is a peer-to-peer authentication protocol - authentication should be managed centrally in most managed networks.", "Impact": "None - this is the default configuration for domain-joined computers.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Lsa\\pku2u\\AllowOnlineID", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Encryption", "Local Policies-Network security"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{f587b673-8a98-44d1-bd1d-0171290e8234}", "AZID": "CCE-37755-6", "Name": "Ensure 'Network Security: Configure encryption types allowed for Kerberos' is set to 'RC4_HMAC_MD5, AES128_HMAC_SHA1, AES256_HMAC_SHA1, Future encryption types'", "Description": "This policy setting allows you to set the encryption types that <PERSON><PERSON><PERSON> is allowed to use. The recommended state for this setting is: `RC4_HMAC_MD5, AES128_HMAC_SHA1, AES256_HMAC_SHA1, Future encryption types`.", "Severity": "Critical", "Vulnerability": "The strength of each encryption algorithm varies from one to the next, choosing stronger algorithms will reduce the risk of compromise however doing so may cause issues when the computer attempts to authenticate with systems that do not support them.", "Impact": "None - this is the default configuration. If not selected, the encryption type will not be allowed. This setting may affect compatibility with client computers or services and applications. Multiple selections are permitted. **Note:** Windows Server 2008 (non-R2) and below allow DES for Kerberos by default, but later OS versions do not.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\Kerberos\\Parameters\\SupportedEncryptionTypes", "ExpectedValue": "2147483644", "RemediateValue": "2147483644", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Encryption", "Local Policies-Network security"], "Filter": ["OSVersion = [WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{9170cd13-5ab9-4c68-8904-a88756b36c6e}", "AZID": "CCE-36326-7", "Name": "Ensure 'Network security: Do not store LAN Manager hash value on next password change' is set to 'Enabled'", "Description": "This policy setting determines whether the LAN Manager (LM) hash value for the new password is stored when the password is changed. The LM hash is relatively weak and prone to attack compared to the cryptographically stronger Microsoft Windows NT hash. Since LM hashes are stored on the local computer in the security database, passwords can then be easily compromised if the database is attacked. **Note:** Older operating systems and some third-party applications may fail when this policy setting is enabled. Also, note that the password will need to be changed on all accounts after you enable this setting to gain the proper benefit. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "The SAM file can be targeted by attackers who seek access to username and password hashes. Such attacks use special tools to crack passwords, which can then be used to impersonate users and gain access to resources on your network. These types of attacks will not be prevented if you enable this policy setting, but it will be much more difficult for these types of attacks to succeed.", "Impact": "None - this is the default configuration. Earlier operating systems such as Windows 95, Windows 98, and Windows ME as well as some third-party applications will fail.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Lsa\\NoLMHash", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Encryption", "Local Policies-Network security"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{09a6fe67-45c1-4606-bc4f-a3f41deb398d}", "AZID": "CCE-36173-3", "Name": "Ensure 'Network security: LAN Manager authentication level' is set to 'Send NTLMv2 response only. Refuse LM & NTLM'", "Description": "LAN Manager (LM) was a family of early Microsoft client-server software (predating Windows NT) that allowed users to link personal computers together on a single network. LM network capabilities included transparent file and print sharing, user security features, and network administration tools. In Active Directory domains, the Kerberos protocol is the default authentication protocol. However, if the Kerberos protocol is not negotiated for some reason, Active Directory will use LM, NTLM, or NTLMv2. LAN Manager authentication includes the LM, NTLM, and NTLM version 2 (NTLMv2) variants, and is the protocol that is used to authenticate all Windows clients when they perform the following operations: - Join a domain - Authenticate between Active Directory forests - Authenticate to down-level domains - Authenticate to computers that do not run Windows 2000, Windows Server 2003, or Windows XP - Authenticate to computers that are not in the domain The Network security: LAN Manager authentication level setting determines which challenge-response authentication protocol is used for network logons. This choice affects the level of authentication protocol used by clients, the level of session security negotiated, and the level of authentication accepted by servers. The recommended state for this setting is: `Send NTLMv2 response only. Refuse LM & NTLM`.", "Severity": "Critical", "Vulnerability": "Windows 2000 and Windows XP clients were configured by default to send LM and NTLM authentication responses (Windows 95-based and Windows 98-based clients only send LM). The default settings in OSes predating Windows Vista - Windows Server 2008 (non-R2) allowed all clients to authenticate with servers and use their resources. However, this meant that LM responses - the weakest form of authentication response - were sent over the network, and it was potentially possible for attackers to sniff that traffic to more easily reproduce the user's password. The Windows 95, Windows 98, and Windows NT operating systems cannot use the Kerberos version 5 protocol for authentication. For this reason, in a Windows Server 2003 domain, these computers authenticate by default with both the LM and NTLM protocols for network authentication. You can enforce a more secure authentication protocol for Windows 95, Windows 98, and Windows NT by using NTLMv2. For the logon process, NTLMv2 uses a secure channel to protect the authentication process. Even if you use NTLMv2 for earlier clients and servers, Windows-based clients and servers that are members of the domain will use the Kerberos authentication protocol to authenticate with Windows Server 2003 or higher domain controllers. For these reasons, it is strongly preferred to restrict the use of LM & NTLM (non-v2) as much as possible.", "Impact": "Clients use NTLMv2 authentication only and use NTLMv2 session security if the server supports it; domain controllers refuse LM and NTLM (accept only NTLMv2 authentication). Clients that do not support NTLMv2 authentication will not be able to authenticate in the domain and access domain resources by using LM and NTLM. **Note:** For information about a hotfix to ensure that this setting works in networks that include Windows NT 4.0-based computers along with Windows 2000, Windows XP, and Windows Server 2003-based computers, see Microsoft Knowledge Base article 305379: [Authentication Problems in Windows 2000 with NTLM 2 Levels Above 2 in a Windows NT 4.0 Domain](https:--support.microsoft.com-en-us-kb-305379).", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Lsa\\LmCompatibilityLevel", "ExpectedValue": "5", "RemediateValue": "5", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Encryption", "Local Policies-Network security"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{4ff2ed85-48d7-4e38-bdb8-6c7df3286882}", "AZID": "CCE-36858-9", "Name": "Ensure 'Network security: LDAP client signing requirements' is set to 'Negotiate signing' or higher", "Description": "This policy setting determines the level of data signing that is requested on behalf of clients that issue LDAP BIND requests. **Note:** This policy setting does not have any impact on LDAP simple bind (`ldap_simple_bind`) or LDAP simple bind through SSL (`ldap_simple_bind_s`). No Microsoft LDAP clients that are included with Windows XP Professional use ldap_simple_bind or ldap_simple_bind_s to communicate with a domain controller. The recommended state for this setting is: `Negotiate signing`. Configuring this setting to `Require signing` also conforms with the benchmark.", "Severity": "Critical", "Vulnerability": "Unsigned network traffic is susceptible to man-in-the-middle attacks in which an intruder captures the packets between the client and server, modifies them, and then forwards them to the server. For an LDAP server, this susceptibility means that an attacker could cause a server to make decisions that are based on false or altered data from the LDAP queries. To lower this risk in your network, you can implement strong physical security measures to protect the network infrastructure. Also, you can make all types of man-in-the-middle attacks extremely difficult if you require digital signatures on all network packets by means of IPsec authentication headers.", "Impact": "None - this is the default configuration. However, if you choose instead to configure the server to _require_ LDAP signatures then you must also configure the client. If you do not configure the client it will not be able to communicate with the server, which could cause many features to fail, including user authentication, Group Policy, and logon scripts, because the caller will be told that the LDAP BIND command request failed.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\LDAP\\LDAPClientIntegrity", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Encryption", "Local Policies-Network security"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{12719aeb-714f-474d-a22b-4c7e1dd1c7f6}", "AZID": "CCE-37553-5", "Name": "Ensure 'Network security: Minimum session security for NTLM SSP based (including secure RPC) clients' is set to 'Require NTLMv2 session security, Require 128-bit encryption'", "Description": "This policy setting determines which behaviors are allowed by clients for applications using the NTLM Security Support Provider (SSP). The SSP Interface (SSPI) is used by applications that need authentication services. The setting does not modify how the authentication sequence works but instead require certain behaviors in applications that use the SSPI. The recommended state for this setting is: `Require NTLMv2 session security, Require 128-bit encryption`. **Note:** These values are dependent on the _Network security: LAN Manager Authentication Level_ security setting value.", "Severity": "Critical", "Vulnerability": "You can enable both options for this policy setting to help protect network traffic that uses the NTLM Security Support Provider (NTLM SSP) from being exposed or tampered with by an attacker who has gained access to the same network. In other words, these options help protect against man-in-the-middle attacks.", "Impact": "NTLM connections will fail if NTLMv2 protocol and strong encryption (128-bit) are not **both** negotiated. Client applications that are enforcing these settings will be unable to communicate with older servers that do not support them. This setting could impact Windows Clustering when applied to servers running Windows Server 2003, see Microsoft Knowledge Base articles 891597: [How to apply more restrictive security settings on a Windows Server 2003-based cluster server](https:--support.microsoft.com-en-us-kb-891597) and 890761: [You receive an \"Error 0x8007042b\" error message when you add or join a node to a cluster if you use NTLM version 2 in Windows Server 2003](https:--support.microsoft.com-en-us-kb-890761) for more information on possible issues and how to resolve them.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Lsa\\MSV1_0\\NTLMMinClientSec", "ExpectedValue": "537395200", "RemediateValue": "537395200", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Encryption", "Local Policies-Network security"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{1ec9eec0-b2e3-45f2-9f4f-e2903412eabe}", "AZID": "CCE-37835-6", "Name": "Ensure 'Network security: Minimum session security for NTLM SSP based (including secure RPC) servers' is set to 'Require NTLMv2 session security, Require 128-bit encryption'", "Description": "This policy setting determines which behaviors are allowed by servers for applications using the NTLM Security Support Provider (SSP). The SSP Interface (SSPI) is used by applications that need authentication services. The setting does not modify how the authentication sequence works but instead require certain behaviors in applications that use the SSPI. The recommended state for this setting is: `Require NTLMv2 session security, Require 128-bit encryption`. **Note:** These values are dependent on the _Network security: LAN Manager Authentication Level_ security setting value.", "Severity": "Critical", "Vulnerability": "You can enable all of the options for this policy setting to help protect network traffic that uses the NTLM Security Support Provider (NTLM SSP) from being exposed or tampered with by an attacker who has gained access to the same network. That is, these options help protect against man-in-the-middle attacks.", "Impact": "NTLM connections will fail if NTLMv2 protocol and strong encryption (128-bit) are not **both** negotiated. Server applications that are enforcing these settings will be unable to communicate with older servers that do not support them. This setting could impact Windows Clustering when applied to servers running Windows Server 2003, see Microsoft Knowledge Base articles 891597: [How to apply more restrictive security settings on a Windows Server 2003-based cluster server](https:--support.microsoft.com-en-us-kb-891597) and 890761: [You receive an \"Error 0x8007042b\" error message when you add or join a node to a cluster if you use NTLM version 2 in Windows Server 2003](https:--support.microsoft.com-en-us-kb-890761) for more information on possible issues and how to resolve them.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Lsa\\MSV1_0\\NTLMMinServerSec", "ExpectedValue": "537395200", "RemediateValue": "537395200", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Encryption", "Local Policies-Network security"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{299d1595-5ab2-4ef5-b287-6477c0df5178}", "AZID": "CCE-37063-5", "Name": "Ensure 'Password must meet complexity requirements' is set to 'Enabled'", "Description": "This policy setting checks all new passwords to ensure that they meet basic requirements for strong passwords. When this policy is enabled, passwords must meet the following minimum requirements: - Not contain the user's account name or parts of the user's full name that exceed two consecutive characters - Be at least six characters in length - Contain characters from three of the following four categories: - English uppercase characters (A through Z) - English lowercase characters (a through z) - Base 10 digits (0 through 9) - Non-alphabetic characters (for example, !, $, #, %) - A catch-all category of any Unicode character that does not fall under the previous four categories. This fifth category can be regionally specific. Each additional character in a password increases its complexity exponentially. For instance, a seven-character, all lower-case alphabetic password would have 267 (approximately 8 x 109 or 8 billion) possible combinations. At 1,000,000 attempts per second (a capability of many password-cracking utilities), it would only take 133 minutes to crack. A seven-character alphabetic password with case sensitivity has 527 combinations. A seven-character case-sensitive alphanumeric password without punctuation has 627 combinations. An eight-character password has 268 (or 2 x 1011) possible combinations. Although this might seem to be a large number, at 1,000,000 attempts per second it would take only 59 hours to try all possible passwords. Remember, these times will significantly increase for passwords that use ALT characters and other special keyboard characters such as \"!\" or \"@\". Proper use of the password settings can help make it difficult to mount a brute force attack. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "Passwords that contain only alphanumeric characters are extremely easy to discover with several publicly available tools.", "Impact": "If the default password complexity configuration is retained, additional help desk calls for locked-out accounts could occur because users might not be accustomed to passwords that contain non-alphabetic characters. However, all users should be able to comply with the complexity requirement with minimal difficulty. If your organization has more stringent security requirements, you can create a custom version of the Passfilt.dll file that allows the use of arbitrarily complex password strength rules. For example, a custom password filter might require the use of non-upper row characters. (Upper row characters are those that require you to hold down the SHIFT key and press any of the digits between 1 and 0.) A custom password filter might also perform a dictionary check to verify that the proposed password does not contain common dictionary words or fragments. Also, the use of ALT key character combinations can greatly enhance the complexity of a password. However, such stringent password requirements can result in unhappy users and an extremely busy help desk. Alternatively, your organization could consider a requirement for all administrator passwords to use ALT characters in the ******** range. (ALT characters outside of this range can represent standard alphanumeric characters that would not add additional complexity to the password.)", "DataSourceType": "Policy", "DataSourceKey": "[System Access]PasswordComplexity", "ExpectedValue": "true", "RemediateValue": "true", "Remediate": "false", "ValueType": "BOOLEAN", "AnalyzeOperation": "EQUALS", "Categories": ["Identity Management", "Account Policies-Password Policy"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{506fa45a-f043-46b0-bca9-da87e2f2618b}", "AZID": "CCE-36143-6", "Name": "Ensure 'Perform volume maintenance tasks' is set to 'Administrators'", "Description": "This policy setting allows users to manage the system's volume or disk configuration, which could allow a user to delete a volume and cause data loss as well as a denial-of-service condition. The recommended state for this setting is: `Administrators`.", "Severity": "Important", "Vulnerability": "A user who is assigned the Perform volume maintenance tasks user right could delete a volume, which could result in the loss of data or a DoS condition.", "Impact": "None - this is the default configuration.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeManageVolumePrivilege", "ExpectedValue": "Administrators", "RemediateValue": "Administrators", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{5b5ac074-b108-4acf-aeca-5baabc276538}", "AZID": "CCE-38036-0", "Name": "Ensure 'Audit Logon' is set to 'Success and Failure'", "Description": "This subcategory reports when a user attempts to log on to the system. These events occur on the accessed computer. For interactive logons, the generation of these events occurs on the computer that is logged on to. If a network logon takes place to access a share, these events generate on the computer that hosts the accessed resource. If you configure this setting to No auditing, it is difficult or impossible to determine which user has accessed or attempted to access organization computers. Events for this subcategory include: - 4624: An account was successfully logged on. - 4625: An account failed to log on. - 4648: A logon was attempted using explicit credentials. - 4675: SIDs were filtered. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9215-69AE-11D9-BED3-************}", "ExpectedValue": "Success and Failure", "RemediateValue": "Success and Failure", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Logon/Logoff"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{904dd87b-780c-4ec8-aa33-1ba3a250d356}", "AZID": "CCE-37855-4", "Name": "Ensure 'Audit Other Account Management Events' is set to 'Success'", "Description": "This subcategory reports other account management events. Events for this subcategory include: - 4782: The password hash an account was accessed. - 4793: The Password Policy Checking API was called. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE923A-69AE-11D9-BED3-************}", "ExpectedValue": "Success", "RemediateValue": "Success", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Account Management"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{1eec091b-04ee-45bb-993a-0a7f930c069f}", "AZID": "CCE-36322-6", "Name": "Ensure 'Audit Other Logon/Logoff Events' is set to 'Success and Failure'", "Description": "This subcategory reports other logon-logoff-related events, such as Terminal Services session disconnects and reconnects, using RunAs to run processes under a different account, and locking and unlocking a workstation. Events for this subcategory include: - 4649: A replay attack was detected. - 4778: A session was reconnected to a Window Station. - 4779: A session was disconnected from a Window Station. - 4800: The workstation was locked. - 4801: The workstation was unlocked. - 4802: The screen saver was invoked. - 4803: The screen saver was dismissed. - 5378: The requested credentials delegation was disallowed by policy. - 5632: A request was made to authenticate to a wireless network. - 5633: A request was made to authenticate to a wired network. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE921C-69AE-11D9-BED3-************}", "ExpectedValue": "Success and Failure", "RemediateValue": "Success and Failure", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Logon/Logoff"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{3f78e74e-1601-4bcc-b2c0-5408642d4b81}", "AZID": "CCE-38030-3", "Name": "Ensure 'Audit Other System Events' is set", "Description": "This subcategory reports on other system events. Events for this subcategory include: - 5024 : The Windows Firewall Service has started successfully. - 5025 : The Windows Firewall Service has been stopped. - 5027 : The Windows Firewall Service was unable to retrieve the security policy from the local storage. The service will continue enforcing the current policy. - 5028 : The Windows Firewall Service was unable to parse the new security policy. The service will continue with currently enforced policy. - 5029: The Windows Firewall Service failed to initialize the driver. The service will continue to enforce the current policy. - 5030: The Windows Firewall Service failed to start. - 5032: Windows Firewall was unable to notify the user that it blocked an application from accepting incoming connections on the network. - 5033 : The Windows Firewall Driver has started successfully. - 5034 : The Windows Firewall Driver has been stopped. - 5035 : The Windows Firewall Driver failed to start. - 5037 : The Windows Firewall Driver detected critical runtime error. Terminating. - 5058: Key file operation. - 5059: Key migration operation. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Capturing these audit events may be useful for identifying when the Windows Firewall is not performing as expected.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9214-69AE-11D9-BED3-************}", "ExpectedValue": "No Auditing", "RemediateValue": "No Auditing", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-System"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{5046d960-670d-4fef-973a-cf242a97147e}", "AZID": "AZ-WIN-00135", "Name": "Ensure 'Audit PNP Activity' is set to 'Success'", "Description": "This policy setting allows you to audit when plug and play detects an external device. The recommended state for this setting is: `Success`. **Note:** A Windows 10, Server 2016 or higher OS is required to access and set this value in Group Policy.", "Severity": "Critical", "Vulnerability": "Enabling this setting will allow a user to audit events when a device is plugged into a system. This can help alert IT staff if unapproved devices are plugged in.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9248-69AE-11D9-BED3-************}", "ExpectedValue": "Success", "RemediateValue": "Success", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Detailed Tracking"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{62a20aad-13ee-4c03-b845-4cc68161a1d4}", "AZID": "CCE-38028-7", "Name": "Ensure 'Audit Policy Change' is set to 'Success'", "Description": "This subcategory reports changes in audit policy including SACL changes. Events for this subcategory include: - 4715: The audit policy (SACL) on an object was changed. - 4719: System audit policy was changed. - 4902: The Per-user audit policy table was created. - 4904: An attempt was made to register a security event source. - 4905: An attempt was made to unregister a security event source. - 4906: The CrashOnAuditFail value has changed. - 4907: Auditing settings on object were changed. - 4908: Special Groups Logon table modified. - 4912: Per User Audit Policy was changed. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE922F-69AE-11D9-BED3-************}", "ExpectedValue": "Success", "RemediateValue": "Success", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Policy Change"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{6b3dc518-61f4-4a47-920c-0411674596a0}", "AZID": "CCE-36059-4", "Name": "Ensure 'Audit Process Creation' is set to 'Success and Failure'", "Description": "This subcategory reports the creation of a process and the name of the program or user that created it. Events for this subcategory include: - 4688: A new process has been created. - 4696: A primary token was assigned to process. Refer to Microsoft Knowledge Base article 947226: [Description of security events in Windows Vista and in Windows Server 2008](https:--support.microsoft.com-en-us-kb-947226) for the most recent information about this setting. The recommended state for this setting is: `Success`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE922B-69AE-11D9-BED3-************}", "ExpectedValue": "Success and Failure", "RemediateValue": "Success and Failure", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Detailed Tracking"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{b88b1d85-5f3c-4235-91ab-6d8b5e767311}", "AZID": "CCE-37617-8", "Name": "Ensure 'Audit Removable Storage' is set to 'Success and Failure'", "Description": "This policy setting allows you to audit user attempts to access file system objects on a removable storage device. A security audit event is generated only for all objects for all types of access requested. If you configure this policy setting, an audit event is generated each time an account accesses a file system object on a removable storage. Success audits record successful attempts and Failure audits record unsuccessful attempts. If you do not configure this policy setting, no audit event is generated when an account accesses a file system object on a removable storage. The recommended state for this setting is: `Success and Failure`. **Note:** A Windows 8, Server 2012 (non-R2) or higher OS is required to access and set this value in Group Policy.", "Severity": "Critical", "Vulnerability": "Auditing removable storage may be useful when investigating an incident. For example, if an individual is suspected of copying sensitive information onto a USB drive.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9245-69AE-11D9-BED3-************}", "ExpectedValue": "Success and Failure", "RemediateValue": "Success and Failure", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Object Access"], "Filter": ["OSVersion = [WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{b8f375f0-6b63-45e7-825e-a48c10de18ce}", "AZID": "CCE-38034-5", "Name": "Ensure 'Audit Security Group Management' is set to 'Success'", "Description": "This subcategory reports each event of security group management, such as when a security group is created, changed, or deleted or when a member is added to or removed from a security group. If you enable this Audit policy setting, administrators can track events to detect malicious, accidental, and authorized creation of security group accounts. Events for this subcategory include: - 4727: A security-enabled global group was created. - 4728: A member was added to a security-enabled global group. - 4729: A member was removed from a security-enabled global group. - 4730: A security-enabled global group was deleted. - 4731: A security-enabled local group was created. - 4732: A member was added to a security-enabled local group. - 4733: A member was removed from a security-enabled local group. - 4734: A security-enabled local group was deleted. - 4735: A security-enabled local group was changed. - 4737: A security-enabled global group was changed. - 4754: A security-enabled universal group was created. - 4755: A security-enabled universal group was changed. - 4756: A member was added to a security-enabled universal group. - 4757: A member was removed from a security-enabled universal group. - 4758: A security-enabled universal group was deleted. - 4764: A group's type was changed. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9237-69AE-11D9-BED3-************}", "ExpectedValue": "Success", "RemediateValue": "Success", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Account Management"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{5fea1bb4-57f4-45bf-a237-8caef8d84533}", "AZID": "CCE-38114-5", "Name": "Ensure 'Audit Security State Change' is set to 'Success'", "Description": "This subcategory reports changes in security state of the system, such as when the security subsystem starts and stops. Events for this subcategory include: - 4608: Windows is starting up. - 4609: Windows is shutting down. - 4616: The system time was changed. - 4621: Administrator recovered system from CrashOnAuditFail. Users who are not administrators will now be allowed to log on. Some auditable activity might not have been recorded. The recommended state for this setting is: `Success`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9210-69AE-11D9-BED3-************}", "ExpectedValue": "Success", "RemediateValue": "Success", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-System"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{a25c1cd3-6b94-4639-befe-26ead1d4c4ed}", "AZID": "CCE-36144-4", "Name": "Ensure 'Audit Security System Extension' is set to 'Success'", "Description": "This subcategory reports the loading of extension code such as authentication packages by the security subsystem. Events for this subcategory include: - 4610: An authentication package has been loaded by the Local Security Authority. - 4611: A trusted logon process has been registered with the Local Security Authority. - 4614: A notification package has been loaded by the Security Account Manager. - 4622: A security package has been loaded by the Local Security Authority. - 4697: A service was installed in the system. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9211-69AE-11D9-BED3-************}", "ExpectedValue": "Success", "RemediateValue": "Success", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-System"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{********-9793-4f86-a442-a506d0fe36ba}", "AZID": "CCE-36267-3", "Name": "Ensure 'Audit Sensitive Privilege Use' is set to 'Success and Failure'", "Description": "This subcategory reports when a user account or service uses a sensitive privilege. A sensitive privilege includes the following user rights: Act as part of the operating system, Back up files and directories, Create a token object, Debug programs, Enable computer and user accounts to be trusted for delegation, Generate security audits, Impersonate a client after authentication, Load and unload device drivers, Manage auditing and security log, Modify firmware environment values, Replace a process-level token, Restore files and directories, and Take ownership of files or other objects. Auditing this subcategory will create a high volume of events. Events for this subcategory include: - 4672: Special privileges assigned to new logon. - 4673: A privileged service was called. - 4674: An operation was attempted on a privileged object. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9228-69AE-11D9-BED3-************}", "ExpectedValue": "Success and Failure", "RemediateValue": "Success and Failure", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Privilege Use"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{8ee0776b-3b84-47bf-9594-e14e29fcc8ff}", "AZID": "CCE-36266-5", "Name": "Ensure 'Audit Special Logon' is set to 'Success'", "Description": "This subcategory reports when a special logon is used. A special logon is a logon that has administrator-equivalent privileges and can be used to elevate a process to a higher level. Events for this subcategory include: - 4964 : Special groups have been assigned to a new logon. The recommended state for this setting is: `Success`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE921B-69AE-11D9-BED3-************}", "ExpectedValue": "Success", "RemediateValue": "Success", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Logon/Logoff"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{915518dc-1954-4531-9204-e80926e2e201}", "AZID": "CCE-37132-8", "Name": "Ensure 'Audit System Integrity' is set to 'Success'", "Description": "This subcategory reports on violations of integrity of the security subsystem. Events for this subcategory include: - 4612 : Internal resources allocated for the queuing of audit messages have been exhausted, leading to the loss of some audits. - 4615 : Invalid use of LPC port. - 4618 : A monitored security event pattern has occurred. - 4816 : RPC detected an integrity violation while decrypting an incoming message. - 5038 : Code integrity determined that the image hash of a file is not valid. The file could be corrupt due to unauthorized modification or the invalid hash could indicate a potential disk device error. - 5056: A cryptographic self test was performed. - 5057: A cryptographic primitive operation failed. - 5060: Verification operation failed. - 5061: Cryptographic operation. - 5062: A kernel-mode cryptographic self test was performed. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9212-69AE-11D9-BED3-************}", "ExpectedValue": "Success", "RemediateValue": "Success", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-System"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{7e4d9fe1-eb3f-49ac-bb5b-d417df7e6d6c}", "AZID": "CCE-37856-2", "Name": "Ensure 'Audit User Account Management' is set to 'Success and Failure'", "Description": "This subcategory reports each event of user account management, such as when a user account is created, changed, or deleted; a user account is renamed, disabled, or enabled; or a password is set or changed. If you enable this Audit policy setting, administrators can track events to detect malicious, accidental, and authorized creation of user accounts. Events for this subcategory include: - 4720: A user account was created. - 4722: A user account was enabled. - 4723: An attempt was made to change an account's password. - 4724: An attempt was made to reset an account's password. - 4725: A user account was disabled. - 4726: A user account was deleted. - 4738: A user account was changed. - 4740: A user account was locked out. - 4765: SID History was added to an account. - 4766: An attempt to add SID History to an account failed. - 4767: A user account was unlocked. - 4780: The ACL was set on accounts which are members of administrators groups. - 4781: The name of an account was changed: - 4794: An attempt was made to set the Directory Services Restore Mode. - 5376: Credential Manager credentials were backed up. - 5377: Credential Manager credentials were restored from a backup. The recommended state for this setting is: `Success and Failure`.", "Severity": "Critical", "Vulnerability": "Auditing these events may be useful when investigating a security incident.", "Impact": "If no audit settings are configured, or if audit settings are too lax on the computers in your organization, security incidents might not be detected or not enough evidence will be available for network forensic analysis after security incidents occur. However, if audit settings are too severe, critically important entries in the Security log may be obscured by all of the meaningless entries and computer performance and the available amount of data storage may be seriously affected. Companies that operate in certain regulated industries may have legal obligations to log certain events or activities.", "DataSourceType": "Audit", "DataSourceKey": "{0CCE9235-69AE-11D9-BED3-************}", "ExpectedValue": "Success and Failure", "RemediateValue": "Success and Failure", "Remediate": "false", "ValueType": "AUDITPOLICY", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Advanced Audit Policy Configuration-Account Management"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{c8375bd2-4161-40d5-a3e5-96ca715e0ece}", "AZID": "CCE-37126-0", "Name": "Ensure 'Prevent downloading of enclosures' is set to 'Enabled'", "Description": "This policy setting prevents the user from having enclosures (file attachments) downloaded from a feed to the user's computer. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "Allowing attachments to be downloaded through the RSS feed can introduce files that could have malicious intent.", "Impact": "Users cannot set the Feed Sync Engine to download an enclosure through the Feed property page. Developers cannot change the download setting through feed APIs.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Internet Explorer\\Feeds\\DisableEnclosureDownload", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Software", "Administrative Templates (Computer)-RSS Feeds"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{e2c34f75-5111-412a-a7a6-c4e74c475833}", "AZID": "CCE-38347-1", "Name": "Ensure 'Prevent enabling lock screen camera' is set to 'Enabled'", "Description": "Disables the lock screen camera toggle switch in PC Settings and prevents a camera from being invoked on the lock screen. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "Disabling the lock screen camera extends the protection afforded by the lock screen to camera features.", "Impact": "If you enable this setting, users will no longer be able to enable or disable lock screen camera access in PC Settings, and the camera cannot be invoked on the lock screen.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Personalization\\NoLockScreenCamera", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Unnecessary software & services", "Administrative Templates (Computer)-Personalization"], "Filter": ["OSVersion = [WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{8a3693ab-3a35-45c0-ab90-0797c5ebb036}", "AZID": "CCE-38348-9", "Name": "Ensure 'Prevent enabling lock screen slide show' is set to 'Enabled'", "Description": "Disables the lock screen slide show settings in PC Settings and prevents a slide show from playing on the lock screen. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "Disabling the lock screen slide show extends the protection afforded by the lock screen to slide show contents.", "Impact": "If you enable this setting, users will no longer be able to modify slide show settings in PC Settings, and no slide show will ever start.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Personalization\\NoLockScreenSlideshow", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Unnecessary software & services", "Administrative Templates (Computer)-Personalization"], "Filter": ["OSVersion = [WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{aec3dc3b-3625-47ea-8e11-fef4b1be8adb}", "AZID": "CCE-37131-0", "Name": "Ensure 'Profile single process' is set to 'Administrators'", "Description": "This policy setting determines which users can use tools to monitor the performance of non-system processes. Typically, you do not need to configure this user right to use the Microsoft Management Console (MMC) Performance snap-in. However, you do need this user right if System Monitor is configured to collect data using Windows Management Instrumentation (WMI). Restricting the Profile single process user right prevents intruders from gaining additional information that could be used to mount an attack on the system. The recommended state for this setting is: `Administrators`.", "Severity": "Important", "Vulnerability": "The Profile single process user right presents a moderate vulnerability. An attacker with this user right could monitor a computer's performance to help identify critical processes that they might wish to attack directly. The attacker may also be able to determine what processes run on the computer so that they could identify countermeasures that they may need to avoid, such as antivirus software, an intrusion-detection system, or which other users are logged on to a computer.", "Impact": "If you remove the Profile single process user right from the Power Users group or other accounts, you could limit the abilities of users who are assigned to specific administrative roles in your environment. You should ensure that delegated tasks will not be negatively affected.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeProfileSingleProcessPrivilege", "ExpectedValue": "Administrators", "RemediateValue": "Administrators", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{e61c2d81-389a-4e59-bf19-2a6db7a0dc0b}", "AZID": "CCE-36052-9", "Name": "Ensure 'Profile system performance' is set to 'Administrators, NT SERVICE\\WdiServiceHost'", "Description": "This policy setting allows users to use tools to view the performance of different system processes, which could be abused to allow attackers to determine a system's active processes and provide insight into the potential attack surface of the computer. The recommended state for this setting is: `Administrators, NT SERVICEWdiServiceHost`.", "Severity": "Important", "Vulnerability": "The Profile system performance user right poses a moderate vulnerability. Attackers with this user right could monitor a computer's performance to help identify critical processes that they might wish to attack directly. Attackers may also be able to determine what processes are active on the computer so that they could identify countermeasures that they may need to avoid, such as antivirus software or an intrusion detection system.", "Impact": "None - this is the default configuration.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeSystemProfilePrivilege", "ExpectedValue": "Administrators, NT SERVICE\\WdiServiceHost", "RemediateValue": "Administrators, NT SERVICE\\WdiServiceHost", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "LESSOREQUAL", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{357272d2-2018-455e-935c-8777473661dd}", "AZID": "CCE-38002-2", "Name": "Ensure 'Prohibit installation and configuration of Network Bridge on your DNS domain network' is set to 'Enabled'", "Description": "You can use this procedure to controls user's ability to install and configure a network bridge. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "The Network Bridge setting, if enabled, allows users to create a Layer 2 Media Access Control (MAC) bridge, enabling them to connect two or more physical network segments together. A network bridge thus allows a computer that has connections to two different networks to share data between those networks. In an enterprise environment, where there is a need to control network traffic to only authorized paths, allowing users to create a network bridge increases the risk and attack surface from the bridged network.", "Impact": "Users cannot create or configure a network bridge.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Network Connections\\NC_AllowNetBridge_NLA", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Network", "Administrative Templates (Computer)-Network Connections"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{4b2ea54f-7c16-4490-8687-cc52c3135b7e}", "AZID": "AZ-WIN-00143", "Name": "Ensure 'Prohibit use of Internet Connection Sharing on your DNS domain network' is set to 'Enabled'", "Description": "Although this \"legacy\" setting traditionally applied to the use of Internet Connection Sharing (ICS) in Windows 2000, Windows XP & Server 2003, this setting now freshly applies to the Mobile Hotspot feature in Windows 10 & Server 2016. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "Non-administrators should not be able to turn on the Mobile Hotspot feature and open their Internet connectivity up to nearby mobile devices.", "Impact": "Mobile Hotspot cannot be enabled or configured by Administrators and non-Administrators alike.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Network Connections\\NC_PersonalFirewallConfig", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Network", "Administrative Templates (Computer)-Network Connections"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{08a4b141-c737-404e-8617-9830268e8bfa}", "AZID": "CCE-37430-6", "Name": "Ensure 'Replace a process level token' is set to 'LOCAL SERVICE, NETWORK SERVICE'", "Description": "This policy setting allows one process or service to start another service or process with a different security access token, which can be used to modify the security access token of that sub-process and result in the escalation of privileges. The recommended state for this setting is: `LOCAL SERVICE, NETWORK SERVICE`. **Note:** A Member Server that holds the _Web Server (IIS)_ Role with _Web Server_ Role Service will require a special exception to this recommendation, to allow IIS application pool(s) to be granted this user right. **Note #2:** A Member Server with Microsoft SQL Server installed will require a special exception to this recommendation for additional SQL-generated entries to be granted this user right.", "Severity": "Important", "Vulnerability": "User with the Replace a process level token privilege are able to start processes as other users whose credentials they know. They could use this method to hide their unauthorized actions on the computer. (On Windows 2000-based computers, use of the Replace a process level token user right also requires the user to have the Adjust memory quotas for a process user right that is discussed earlier in this section.)", "Impact": "On most computers, this is the default configuration and there will be no negative impact. However, if you have installed the _Web Server (IIS)_ Role with _Web Services_ Role Service, you will need to allow the IIS application pool(s) to be granted this User Right Assignment.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeAssignPrimaryTokenPrivilege", "ExpectedValue": "LOCAL SERVICE, NETWORK SERVICE", "RemediateValue": "LOCAL SERVICE, NETWORK SERVICE", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "LESSOREQUAL", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{95aaff93-8581-4f12-becb-3cf1f42153f9}", "AZID": "CCE-37567-5", "Name": "Ensure 'Require secure RPC communication' is set to 'Enabled'", "Description": "This policy setting allows you to specify whether a terminal server requires secure remote procedure call (RPC) communication with all clients or allows unsecured communication. You can use this policy setting to strengthen the security of RPC communication with clients by allowing only authenticated and encrypted requests. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "Allowing unsecure RPC communication can exposes the server to man in the middle attacks and data disclosure attacks.", "Impact": "Remote Desktop Services accepts requests from RPC clients that support secure requests, and does not allow unsecured communication with untrusted clients.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows NT\\Terminal Services\\fEncryptRPCTraffic", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: RDP", "Administrative Templates (Computer)-Security"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{e371ff9f-d769-4f2e-92ec-84430421a6f9}", "AZID": "CCE-37613-7", "Name": "Ensure 'Restore files and directories' is set to 'Administrators, Backup Operators'", "Description": "This policy setting determines which users can bypass file, directory, registry, and other persistent object permissions when restoring backed up files and directories on computers that run Windows Vista in your environment. This user right also determines which users can set valid security principals as object owners; it is similar to the Back up files and directories user right. The recommended state for this setting is: `Administrators`.", "Severity": "Important", "Vulnerability": "An attacker with the Restore files and directories user right could restore sensitive data to a computer and overwrite data that is more recent, which could lead to loss of important data, data corruption, or a denial of service. Attackers could overwrite executable files that are used by legitimate administrators or system services with versions that include malicious software to grant themselves elevated privileges, compromise data, or install backdoors for continued access to the computer. **Note:** Even if the following countermeasure is configured, an attacker could still restore data to a computer in a domain that is controlled by the attacker. Therefore, it is critical that organizations carefully protect the media that are used to back up data.", "Impact": "If you remove the Restore files and directories user right from the Backup Operators group and other accounts you could make it impossible for users who have been delegated specific tasks to perform those tasks. You should verify that this change won't negatively affect the ability of your organization's personnel to do their jobs.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeRestorePrivilege", "ExpectedValue": "Administrators, Backup Operators", "RemediateValue": "Administrators, Backup Operators", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "LESSOREQUAL", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{def76644-9c74-47ed-950e-466f9c192d92}", "AZID": "CCE-37145-0", "Name": "Ensure 'Security: Control Event Log behavior when the log file reaches its maximum size' is set to 'Disabled'", "Description": "This policy setting controls Event Log behavior when the log file reaches its maximum size. The recommended state for this setting is: `Disabled`. **Note:** Old events may or may not be retained according to the \"Backup log automatically when full\" policy setting.", "Severity": "Critical", "Vulnerability": "If new events are not recorded it may be difficult or impossible to determine the root cause of system problems or the unauthorized activities of malicious users.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\EventLog\\Security\\Retention", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_SZ", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["Logging & Auditing", "Administrative Templates (Computer)-Security"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{f0940a97-7321-4fa1-b56a-bcc5506aa303}", "AZID": "CCE-37695-4", "Name": "Ensure 'Security: Specify the maximum log file size (KB)' is set to 'Enabled: 196,608 or greater'", "Description": "This policy setting specifies the maximum size of the log file in kilobytes. The maximum log file size can be configured between 1 megabyte (1,024 kilobytes) and 2 terabytes (2,147,483,647 kilobytes) in kilobyte increments. The recommended state for this setting is: `Enabled: 196,608 or greater`.", "Severity": "Critical", "Vulnerability": "If events are not recorded it may be difficult or impossible to determine the root cause of system problems or the unauthorized activities of malicious users.", "Impact": "When event logs fill to capacity, they will stop recording information unless the retention method for each is set so that the computer will overwrite the oldest entries with the most recent ones. To mitigate the risk of loss of recent data, you can configure the retention method so that older events are overwritten as needed. The consequence of this configuration is that older events will be removed from the logs. Attackers can take advantage of such a configuration, because they can generate a large number of extraneous events to overwrite any evidence of their attack. These risks can be somewhat reduced if you automate the archival and backup of event log data. Ideally, all specifically monitored events should be sent to a server that uses Microsoft System Center Operations Manager (SCOM) or some other automated monitoring tool. Such a configuration is particularly important because an attacker who successfully compromises a server could clear the Security log. If all events are sent to a monitoring server, then you will be able to gather forensic information about the attacker's activities.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\EventLog\\Security\\MaxSize", "ExpectedValue": "196608", "RemediateValue": "196608", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Administrative Templates (Computer)-Security"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{82406d81-ad37-4953-b4b6-830b10c8ad44}", "AZID": "CCE-36627-8", "Name": "Ensure 'Set client connection encryption level' is set to 'Enabled: High Level'", "Description": "This policy setting specifies whether to require the use of a specific encryption level to secure communications between client computers and RD Session Host servers during Remote Desktop Protocol (RDP) connections. This policy only applies when you are using native RDP encryption. However, native RDP encryption (as opposed to SSL encryption) is not recommended. This policy does not apply to SSL encryption. The recommended state for this setting is: `Enabled: High Level`.", "Severity": "Critical", "Vulnerability": "If Terminal Server client connections are allowed that use low level encryption, it is more likely that an attacker will be able to decrypt any captured Terminal Services network traffic.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows NT\\Terminal Services\\MinEncryptionLevel", "ExpectedValue": "3", "RemediateValue": "3", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: RDP", "Administrative Templates (Computer)-Security"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{b1442e72-e644-4305-ab47-ec674cec599a}", "AZID": "CCE-38217-6", "Name": "Ensure 'Set the default behavior for AutoRun' is set to 'Enabled: Do not execute any autorun commands'", "Description": "This policy setting sets the default behavior for Autorun commands. Autorun commands are generally stored in autorun.inf files. They often launch the installation program or other routines. The recommended state for this setting is: `Enabled: Do not execute any autorun commands`.", "Severity": "Critical", "Vulnerability": "Prior to Windows Vista, when media containing an autorun command is inserted, the system will automatically execute the program without user intervention. This creates a major security concern as code may be executed without user's knowledge. The default behavior starting with Windows Vista is to prompt the user whether autorun command is to be run. The autorun command is represented as a handler in the Autoplay dialog.", "Impact": "AutoRun commands will be completely disabled.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\Explorer\\NoAutorun", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Autoplay", "Administrative Templates (Computer)-AutoPlay Policies"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{b4b5eacc-5296-4712-8193-f811a894a23c}", "AZID": "CCE-38276-2", "Name": "Ensure 'Setup: Control Event Log behavior when the log file reaches its maximum size' is set to 'Disabled'", "Description": "This policy setting controls Event Log behavior when the log file reaches its maximum size. The recommended state for this setting is: `Disabled`. **Note:** Old events may or may not be retained according to the \"Backup log automatically when full\" policy setting.", "Severity": "Critical", "Vulnerability": "If new events are not recorded it may be difficult or impossible to determine the root cause of system problems or the unauthorized activities of malicious users.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\EventLog\\Setup\\Retention", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_SZ", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["Logging & Auditing", "Administrative Templates (Computer)-Setup"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{5edbf9b2-171d-4b8f-bd74-bc518e1bf146}", "AZID": "CCE-37526-1", "Name": "Ensure 'Setup: Specify the maximum log file size (KB)' is set to 'Enabled: 32,768 or greater'", "Description": "This policy setting specifies the maximum size of the log file in kilobytes. The maximum log file size can be configured between 1 megabyte (1,024 kilobytes) and 2 terabytes (2,147,483,647 kilobytes) in kilobyte increments. The recommended state for this setting is: `Enabled: 32,768 or greater`.", "Severity": "Critical", "Vulnerability": "If events are not recorded it may be difficult or impossible to determine the root cause of system problems or the unauthorized activities of malicious users", "Impact": "When event logs fill to capacity, they will stop recording information unless the retention method for each is set so that the computer will overwrite the oldest entries with the most recent ones. To mitigate the risk of loss of recent data, you can configure the retention method so that older events are overwritten as needed. The consequence of this configuration is that older events will be removed from the logs. Attackers can take advantage of such a configuration, because they can generate a large number of extraneous events to overwrite any evidence of their attack. These risks can be somewhat reduced if you automate the archival and backup of event log data. Ideally, all specifically monitored events should be sent to a server that uses Microsoft System Center Operations Manager (SCOM) or some other automated monitoring tool. Such a configuration is particularly important because an attacker who successfully compromises a server could clear the Security log. If all events are sent to a monitoring server, then you will be able to gather forensic information about the attacker's activities.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\EventLog\\Setup\\MaxSize", "ExpectedValue": "32768", "RemediateValue": "32768", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Administrative Templates (Computer)-Setup"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{ef0eefbb-e845-47f3-af9a-3409296d3264}", "AZID": "CCE-38328-1", "Name": "Ensure 'Shut down the system' is set to 'Administrators'", "Description": "This policy setting determines which users who are logged on locally to the computers in your environment can shut down the operating system with the Shut Down command. Misuse of this user right can result in a denial of service condition. The recommended state for this setting is: `Administrators`.", "Severity": "Important", "Vulnerability": "The ability to shut down domain controllers and member servers should be limited to a very small number of trusted administrators. Although the **Shut down the system** user right requires the ability to log on to the server, you should be very careful about which accounts and groups you allow to shut down a domain controller or member server. When a domain controller is shut down, it is no longer available to process logons, serve Group Policy, and answer Lightweight Directory Access Protocol (LDAP) queries. If you shut down domain controllers that possess Flexible Single Master Operations (FSMO) roles, you can disable key domain functionality, such as processing logons for new passwords—the Primary Domain Controller (PDC) Emulator role.", "Impact": "The impact of removing these default groups from the Shut down the system user right could limit the delegated abilities of assigned roles in your environment. You should confirm that delegated activities will not be adversely affected.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeShutdownPrivilege", "ExpectedValue": "Administrators", "RemediateValue": "Administrators", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{fa4d7c0b-987e-47f6-bf8b-f38f49e7c00b}", "AZID": "CCE-36788-8", "Name": "Ensure 'Shutdown: Allow system to be shut down without having to log on' is set to 'Disabled'", "Description": "This policy setting determines whether a computer can be shut down when a user is not logged on. If this policy setting is enabled, the shutdown command is available on the Windows logon screen. It is recommended to disable this policy setting to restrict the ability to shut down the computer to users with credentials on the system. The recommended state for this setting is: `Disabled`. **Note:** In Server 2008 R2 and older versions, this setting had no impact on Remote Desktop (RDP) - Terminal Services sessions - it only affected the local console. However, Microsoft changed the behavior in Windows Server 2012 (non-R2) and above, where if set to Enabled, RDP sessions are also allowed to shut down or restart the server.", "Severity": "Important", "Vulnerability": "Users who can access the console locally could shut down the computer. Attackers could also walk to the local console and restart the server, which would cause a temporary DoS condition. Attackers could also shut down the server and leave all of its applications and services unavailable. As noted in the Description above, the Denial of Service (DoS) risk of enabling this setting dramatically increases in Windows Server 2012 (non-R2) and above, as even remote users can shut down or restart the server.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\ShutdownWithoutLogon", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Devices", "Local Policies-Shutdown"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{b784a87e-4aa2-4f61-8b3f-38abff6dac22}", "AZID": "CCE-36977-7", "Name": "Ensure 'Sign-in last interactive user automatically after a system-initiated restart' is set to 'Disabled'", "Description": "This policy setting controls whether a device will automatically sign-in the last interactive user after Windows Update restarts the system. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "Disabling this feature will prevent the caching of user's credentials and unauthorized use of the device, and also ensure the user is aware of the restart.", "Impact": "The device does not store the user's credentials for automatic sign-in after a Windows Update restart. The users' lock screen apps are not restarted after the system restarts. The user is required to present the logon credentials in order to proceed after restart.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\DisableAutomaticRestartSignOn", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["User Rights", "Administrative Templates (Computer)-Windows Logon Options"], "Filter": ["OSVersion = [WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{adb052b7-c17e-4b8c-86b8-d81b6a89af20}", "AZID": "CCE-36286-3", "Name": "Ensure 'Store passwords using reversible encryption' is set to 'Disabled'", "Description": "This policy setting determines whether the operating system stores passwords in a way that uses reversible encryption, which provides support for application protocols that require knowledge of the user's password for authentication purposes. Passwords that are stored with reversible encryption are essentially the same as plaintext versions of the passwords. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "Enabling this policy setting allows the operating system to store passwords in a weaker format that is much more susceptible to compromise and weakens your system security.", "Impact": "If your organization uses either the CHAP authentication protocol through remote access or IAS services or Digest Authentication in IIS, you must configure this policy setting to Enabled. This setting is extremely dangerous to apply through Group Policy on a user-by-user basis, because it requires the appropriate user account object to be opened in Active Directory Users and Computers.", "DataSourceType": "Policy", "DataSourceKey": "[System Access]ClearTextPassword", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "UINTEGER", "AnalyzeOperation": "EQUALS", "Categories": ["Identity Management", "Account Policies-Password Policy"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{0be33574-5e6c-4cfe-8b84-18819338eb6e}", "AZID": "CCE-37885-1", "Name": "Ensure 'System objects: Require case insensitivity for non-Windows subsystems' is set to 'Enabled'", "Description": "This policy setting determines whether case insensitivity is enforced for all subsystems. The Microsoft Win32 subsystem is case insensitive. However, the kernel supports case sensitivity for other subsystems, such as the Portable Operating System Interface for UNIX (POSIX). Because Windows is case insensitive (but the POSIX subsystem will support case sensitivity), failure to enforce this policy setting makes it possible for a user of the POSIX subsystem to create a file with the same name as another file by using mixed case to label it. Such a situation can block access to these files by another user who uses typical Win32 tools, because only one of the files will be available. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "Because Windows is case-insensitive but the POSIX subsystem will support case sensitivity, failure to enable this policy setting would make it possible for a user of that subsystem to create a file with the same name as another file but with a different mix of upper and lower case letters. Such a situation could potentially confuse users when they try to access such files from normal Win32 tools because only one of the files will be available.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "System\\CurrentControlSet\\Control\\Session Manager\\Kernel\\ObCaseInsensitive", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["System", "Local Policies-System objects"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{dbc34c58-a26a-4007-8584-533377970464}", "AZID": "CCE-37644-2", "Name": "Ensure 'System objects: Strengthen default permissions of internal system objects (e.g. Symbolic Links)' is set to 'Enabled'", "Description": "This policy setting determines the strength of the default discretionary access control list (DACL) for objects. Active Directory maintains a global list of shared system resources, such as DOS device names, mutexes, and semaphores. In this way, objects can be located and shared among processes. Each type of object is created with a default DACL that specifies who can access the objects and what permissions are granted. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "This setting determines the strength of the default DACL for objects. Windows maintains a global list of shared computer resources so that objects can be located and shared among processes. Each type of object is created with a default DACL that specifies who can access the objects and with what permissions.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Session Manager\\ProtectionMode", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["Data protection", "Local Policies-System objects"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{855a1842-83b0-4d45-8950-7e9c1f92ee3c}", "AZID": "CCE-35921-6", "Name": "Ensure 'System settings: Optional subsystems' is set to 'Defined: (blank)'", "Description": "This security setting determines which subsystems can optionally be started up to support your applications. With this security setting, you can specify as many subsystems to support your applications as your environment demands. The recommended state for this setting is:` Defined:(blank)`", "Severity": "Informational", "Vulnerability": "POSIX is included with Windows and enabled by default. If you don't need it, leaving it enabled could introduce an additional attack surface to your environment.", "Impact": "Removes POSIX compatibility.", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Control\\Session Manager\\SubSYSTEMs\\Optional", "ExpectedValue": "", "RemediateValue": "", "Remediate": "false", "ValueType": "REG_MULTI_SZ", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Remove unnecessary subsystems", "Local Policies-System settings"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{c738d4e5-cd9a-403f-a667-4e507a7c996c}", "AZID": "CCE-36160-0", "Name": "Ensure 'System: Control Event Log behavior when the log file reaches its maximum size' is set to 'Disabled'", "Description": "This policy setting controls Event Log behavior when the log file reaches its maximum size. The recommended state for this setting is: `Disabled`. **Note:** Old events may or may not be retained according to the \"Backup log automatically when full\" policy setting.", "Severity": "Critical", "Vulnerability": "If new events are not recorded it may be difficult or impossible to determine the root cause of system problems or the unauthorized activities of malicious users.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\EventLog\\System\\Retention", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_SZ", "AnalyzeOperation": "EQUALS", "Categories": ["Logging & Auditing", "Administrative Templates (Computer)-System"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{d639a7a0-cb57-41c0-943c-ddb65b68664f}", "AZID": "CCE-36092-5", "Name": "Ensure 'System: Specify the maximum log file size (KB)' is set to 'Enabled: 32,768 or greater'", "Description": "This policy setting specifies the maximum size of the log file in kilobytes. The maximum log file size can be configured between 1 megabyte (1,024 kilobytes) and 2 terabytes (2,147,483,647 kilobytes) in kilobyte increments. The recommended state for this setting is: `Enabled: 32,768 or greater`.", "Severity": "Critical", "Vulnerability": "If events are not recorded it may be difficult or impossible to determine the root cause of system problems or the unauthorized activities of malicious users", "Impact": "When event logs fill to capacity, they will stop recording information unless the retention method for each is set so that the computer will overwrite the oldest entries with the most recent ones. To mitigate the risk of loss of recent data, you can configure the retention method so that older events are overwritten as needed. The consequence of this configuration is that older events will be removed from the logs. Attackers can take advantage of such a configuration, because they can generate a large number of extraneous events to overwrite any evidence of their attack. These risks can be somewhat reduced if you automate the archival and backup of event log data. Ideally, all specifically monitored events should be sent to a server that uses Microsoft System Center Operations Manager (SCOM) or some other automated monitoring tool. Such a configuration is particularly important because an attacker who successfully compromises a server could clear the Security log. If all events are sent to a monitoring server, then you will be able to gather forensic information about the attacker's activities.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\EventLog\\System\\MaxSize", "ExpectedValue": "32768", "RemediateValue": "32768", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "GREATEROREQUAL", "Categories": ["Logging & Auditing", "Administrative Templates (Computer)-System"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{b8841a6a-97b1-485b-9f3c-e5ccef30d2e6}", "AZID": "CCE-38325-7", "Name": "Ensure 'Take ownership of files or other objects' is set to 'Administrators'", "Description": "This policy setting allows users to take ownership of files, folders, registry keys, processes, or threads. This user right bypasses any permissions that are in place to protect objects to give ownership to the specified user. The recommended state for this setting is: `Administrators`.", "Severity": "Critical", "Vulnerability": "Any users with the Take ownership of files or other objects user right can take control of any object, regardless of the permissions on that object, and then make any changes they wish to that object. Such changes could result in exposure of data, corruption of data, or a DoS condition.", "Impact": "None - this is the default configuration.", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeTakeOwnershipPrivilege", "ExpectedValue": "Administrators", "RemediateValue": "Administrators", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "EQUALS", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{677cecca-2482-423b-907e-53388406fa12}", "AZID": "CCE-35893-7", "Name": "Ensure 'Turn off app notifications on the lock screen' is set to 'Enabled'", "Description": "This policy setting allows you to prevent app notifications from appearing on the lock screen. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "App notifications might display sensitive business or personal data.", "Impact": "No app notifications are displayed on the lock screen.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\System\\DisableLockScreenAppNotifications", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["App Security", "Administrative Templates (Computer)-Logon"], "Filter": ["OSVersion = [WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{925d7a22-4763-491c-9af1-874ea79bf0c7}", "AZID": "CCE-36875-3", "Name": "Ensure 'Turn off Autoplay' is set to 'Enabled: All drives'", "Description": "Autoplay starts to read from a drive as soon as you insert media in the drive, which causes the setup file for programs or audio media to start immediately. An attacker could use this feature to launch a program to damage the computer or data on the computer. Autoplay is disabled by default on some removable drive types, such as floppy disk and network drives, but not on CD-ROM drives. **Note:** You cannot use this policy setting to enable Autoplay on computer drives in which it is disabled by default, such as floppy disk and network drives. The recommended state for this setting is: `Enabled: All drives`.", "Severity": "Critical", "Vulnerability": "An attacker could use this feature to launch a program to damage a client computer or data on the computer.", "Impact": "Autoplay will be disabled - users will have to manually launch setup or installation programs that are provided on removable media.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\Explorer\\NoDriveTypeAutoRun", "ExpectedValue": "255", "RemediateValue": "255", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Autoplay", "Administrative Templates (Computer)-AutoPlay Policies"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{b2538b69-4020-4d50-9f63-581b673a014c}", "AZID": "CCE-37809-1", "Name": "Ensure 'Turn off Data Execution Prevention for Explorer' is set to 'Disabled'", "Description": "Disabling data execution prevention can allow certain legacy plug-in applications to function without terminating Explorer. The recommended state for this setting is: `Disabled`. **Note:** Some legacy plug-in applications and other software may not function with Data Execution Prevention and will require an exception to be defined for that specific plug-in-software.", "Severity": "Critical", "Vulnerability": "Data Execution Prevention is an important security feature supported by Explorer that helps to limit the impact of certain types of malware.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Explorer\\NoDataExecutionPrevention", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Software", "Administrative Templates (Computer)-File Explorer"], "Filter": ["OSVersion = [WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{e5eefc4a-5ba9-404f-ad8d-6a488fed94a8}", "AZID": "CCE-37339-9", "Name": "Ensure 'Turn off desktop gadgets' is set to 'Enabled'", "Description": "This policy setting allows you to turn off desktop gadgets. Gadgets are small applets that display information or utilities on the desktop. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "Allowing gadgets could allow users to install custom gadgets that could be malicious.", "Impact": "Desktop gadgets will be turned off.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\Windows\\Sidebar\\TurnOffSidebar", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Software", "Administrative Templates (Computer)-Desktop Gadgets"], "Filter": ["OSVersion = [WS2008, WS2008R2]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{56c1cb3d-3ead-42fe-a57e-360ff5dbcff0}", "AZID": "CCE-36625-2", "Name": "Ensure 'Turn off downloading of print drivers over HTTP' is set to 'Enabled'", "Description": "This policy setting controls whether the computer can download print driver packages over HTTP. To set up HTTP printing, printer drivers that are not available in the standard operating system installation might need to be downloaded over HTTP. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "Users might download drivers that include malicious code.", "Impact": "Print drivers cannot be downloaded over HTTP. **Note:** This policy setting does not prevent the client computer from printing to printers on the intranet or the Internet over HTTP. It only prohibits downloading drivers that are not already installed locally.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows NT\\Printers\\DisableWebPnPDownload", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Network", "Administrative Templates (Computer)-Internet Communication settings"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{a76d6552-cd22-4a2c-adc1-50f8705cad17}", "AZID": "CCE-36660-9", "Name": "Ensure 'Turn off heap termination on corruption' is set to 'Disabled'", "Description": "Without heap termination on corruption, legacy plug-in applications may continue to function when a File Explorer session has become corrupt. Ensuring that heap termination on corruption is active will prevent this. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "Allowing an application to function after its session has become corrupt increases the risk posture to the system.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Explorer\\NoHeapTerminationOnCorruption", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Software", "Administrative Templates (Computer)-File Explorer"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{1f8a6f9e-4a14-4c2e-a889-6d04d460b16e}", "AZID": "CCE-37163-3", "Name": "Ensure 'Turn off Internet Connection Wizard if URL connection is referring to Microsoft.com' is set to 'Enabled'", "Description": "This policy setting specifies whether the Internet Connection Wizard can connect to Microsoft to download a list of Internet Service Providers (ISPs). The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "In an Enterprise environment we want to lower the risk of a user unknowingly exposing sensitive data.", "Impact": "The \"Choose a list of Internet Service Providers\" path in the Internet Connection Wizard causes the wizard to exit. This prevents users from retrieving the list of ISPs, which resides on Microsoft servers.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\Internet Connection Wizard\\ExitOnMSICW", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Network", "Administrative Templates (Computer)-Internet Communication settings"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{bbfadcd6-010b-49f0-b864-7b1a418aeb1b}", "AZID": "AZ-WIN-00144", "Name": "Ensure 'Turn off Microsoft consumer experiences' is set to 'Enabled'", "Description": "This policy setting turns off experiences that help consumers make the most of their devices and Microsoft account. The recommended state for this setting is: `Enabled`. **Note:** [Per Microsoft TechNet](https:--technet.microsoft.com-en-us-itpro-windows-manage-group-policies-for-enterprise-and-education-editions), this policy setting only applies to Windows 10 Enterprise and Windows 10 Education.", "Severity": "Important", "Vulnerability": "Having apps silently installed in an environment is not good security practice - especially if the apps send data back to a 3rd party.", "Impact": "Users will no longer see personalized recommendations from Microsoft and notifications about their Microsoft account.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\CloudContent\\DisableWindowsConsumerFeatures", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Unnecessary software & services", "Administrative Templates (Computer)-Cloud Content"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{03c71e91-d450-4d69-a808-4de0425d7030}", "AZID": "AZ-WIN-00145", "Name": "Ensure 'Turn off multicast name resolution' is set to 'Enabled' (MS Only)", "Description": "LLMNR is a secondary name resolution protocol. With LLMNR, queries are sent using multicast over a local network link on a single subnet from a client computer to another client computer on the same subnet that also has LLMNR enabled. LLMNR does not require a DNS server or DNS client configuration, and provides name resolution in scenarios in which conventional DNS name resolution is not possible. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "An attacker can listen on a network for these LLMNR (UDP-5355) or NBT-NS (UDP-137) broadcasts and respond to them, It can trick the host into thinking that it knows the location of the requested system. **Note:** To completely mitigate local name resolution poisoning, in addition to this setting, the properties of each installed NIC should also be set to `Disable NetBIOS over TCP-IP` (on the WINS tab in the NIC properties). Unfortunately, there is no global setting to achieve this that automatically applies to all NICs - it is a per NIC setting that varies with different NIC hardware installations.", "Impact": "In the event DNS is unavailable a system will be unable to request it from other systems on the same subnet.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows NT\\DNSClient\\EnableMulticast", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Network", "Administrative Templates (Computer)-DNS Client"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{94cc076f-0e88-4398-ac29-d0dc7170303f}", "AZID": "CCE-36809-2", "Name": "Ensure 'Turn off shell protocol protected mode' is set to 'Disabled'", "Description": "This policy setting allows you to configure the amount of functionality that the shell protocol can have. When using the full functionality of this protocol applications can open folders and launch files. The protected mode reduces the functionality of this protocol allowing applications to only open a limited set of folders. Applications are not able to open files with this protocol when it is in the protected mode. It is recommended to leave this protocol in the protected mode to increase the security of Windows. The recommended state for this setting is: `Disabled`.", "Severity": "Important", "Vulnerability": "Limiting the opening of files and folders to a limited set reduces the attack surface of the system.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\Explorer\\PreXPSP2ShellProtocolBehavior", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Software", "Administrative Templates (Computer)-File Explorer"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{1bbbf411-071c-428c-8e1d-84079534cd16}", "AZID": "CCE-36258-2", "Name": "Ensure 'Turn Off user-installed desktop gadgets' is set to 'Enabled'", "Description": "This policy setting allows you to turn off desktop gadgets that have been installed by the user. The recommended state for this setting is: `Enabled`.", "Severity": "Important", "Vulnerability": "Allowing gadgets could allow users to install custom gadgets that could be malicious.", "Impact": "Windows will not run any user-installed gadgets.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\Windows\\Sidebar\\TurnOffUserInstalledGadgets", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Software", "Administrative Templates (Computer)-Desktop Gadgets"], "Filter": ["OSVersion = [WS2008, WS2008R2]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{37e5e1d9-b9d2-454b-bf3f-124682309155}", "AZID": "CCE-37528-7", "Name": "Ensure 'Turn on convenience PIN sign-in' is set to 'Disabled'", "Description": "This policy setting allows you to control whether a domain user can sign in using a convenience PIN. In Windows 10, convenience PIN was replaced with Passport, which has stronger security properties. To configure Passport for domain users, use the policies under Computer configurationAdministrative TemplatesWindows ComponentsMicrosoft Passport for Work. **Note:** The user's domain password will be cached in the system vault when using this feature. The recommended state for this setting is: `Disabled`.", "Severity": "Important", "Vulnerability": "A PIN is created from a much smaller selection of characters than a password, so in most cases a PIN will be much less robust than a password.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows\\System\\AllowDomainPINLogon", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["Identity Management", "Administrative Templates (Computer)-Logon"], "Filter": ["OSVersion = [WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{967531f7-69cd-4a38-a517-3ebf4e5284cd}", "AZID": "CCE-36494-3", "Name": "Ensure 'User Account Control: Admin Approval Mode for the Built-in Administrator account' is set to 'Enabled'", "Description": "This policy setting controls the behavior of Admin Approval Mode for the built-in Administrator account. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "One of the risks that the User Account Control feature introduced with Windows Vista is trying to mitigate is that of malicious software running under elevated credentials without the user or administrator being aware of its activity. An attack vector for these programs was to discover the password of the account named \"Administrator\" because that user account was created for all installations of Windows. To address this risk, in Windows Vista and newer, the built-in Administrator account is now disabled by default. In a default installation of a new computer, accounts with administrative control over the computer are initially set up in one of two ways: - If the computer is not joined to a domain, the first user account you create has the equivalent permissions as a local administrator. - If the computer is joined to a domain, no local administrator accounts are created. The Enterprise or Domain Administrator must log on to the computer and create one if a local administrator account is warranted. Once Windows is installed, the built-in Administrator account may be manually enabled, but we strongly recommend that this account remain disabled.", "Impact": "The built-in Administrator account uses Admin Approval Mode. Users that log on using the local Administrator account will be prompted for consent whenever a program requests an elevation in privilege, just like any other user would.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\FilterAdministratorToken", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["User account control", "Local Policies-User Account Control"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{467c29d0-b1be-4113-937c-65583cedf2f0}", "AZID": "CCE-36863-9", "Name": "Ensure 'User Account Control: Allow UIAccess applications to prompt for elevation without using the secure desktop' is set to 'Disabled'", "Description": "This policy setting controls whether User Interface Accessibility (UIAccess or UIA) programs can automatically disable the secure desktop for elevation prompts used by a standard user. The recommended state for this setting is: `Disabled`.", "Severity": "Critical", "Vulnerability": "One of the risks that the UAC feature introduced with Windows Vista is trying to mitigate is that of malicious software running under elevated credentials without the user or administrator being aware of its activity. This setting allows the administrator to perform operations that require elevated privileges while connected via Remote Assistance. This increases security in that organizations can use UAC even when end user support is provided remotely. However, it also reduces security by adding the risk that an administrator might allow an unprivileged user to share elevated privileges for an application that the administrator needs to use during the Remote Desktop session.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\EnableUIADesktopToggle", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["User account control", "Local Policies-User Account Control"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{fc8a4401-ff7a-4a6d-add4-758acce6b76c}", "AZID": "CCE-37029-6", "Name": "Ensure 'User Account Control: Behavior of the elevation prompt for administrators in Admin Approval Mode' is set to 'Prompt for consent on the secure desktop'", "Description": "This policy setting controls the behavior of the elevation prompt for administrators. The recommended state for this setting is: `Prompt for consent on the secure desktop`.", "Severity": "Critical", "Vulnerability": "One of the risks that the UAC feature introduced with Windows Vista is trying to mitigate is that of malicious software running under elevated credentials without the user or administrator being aware of its activity. This setting raises awareness to the administrator of elevated privilege operations and permits the administrator to prevent a malicious program from elevating its privilege when the program attempts to do so.", "Impact": "When an operation (including execution of a Windows binary) requires elevation of privilege, the user is prompted on the secure desktop to select either Permit or Deny. If the user selects Permit, the operation continues with the user's highest available privilege.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\ConsentPromptBehaviorAdmin", "ExpectedValue": "5", "RemediateValue": "5", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["User account control", "Local Policies-User Account Control"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{ea132d56-9c29-4d2a-bc92-fc81f616e540}", "AZID": "CCE-36864-7", "Name": "Ensure 'User Account Control: Behavior of the elevation prompt for standard users' is set to 'Automatically deny elevation requests'", "Description": "This policy setting controls the behavior of the elevation prompt for standard users. The recommended state for this setting is: `Automatically deny elevation requests`.", "Severity": "Critical", "Vulnerability": "One of the risks that the User Account Control feature introduced with Windows Vista is trying to mitigate is that of malicious programs running under elevated credentials without the user or administrator being aware of their activity. This setting raises awareness to the user that a program requires the use of elevated privilege operations and requires that the user be able to supply administrative credentials in order for the program to run.", "Impact": "When an operation requires elevation of privilege, a configurable access denied error message is displayed. An enterprise that is running desktops as standard user may choose this setting to reduce help desk calls. **Note:** With this setting configured as recommended, the default error message displayed when a user attempts to perform an operation or run a program requiring privilege elevation (without Administrator rights) is \"_This program will not run. This program is blocked by group policy. For more information, contact your system administrator._\" Some users who are not used to seeing this message may believe that the operation or program they attempted is specifically blocked by group policy, as that is what the message seems to imply. This message may therefore result in user questions as to why that specific operation-program is blocked, when in fact, the problem is that they need to perform the operation or run the program with an Administrative account (or \"Run as Administrator\" if it _is_ already an Administrator account), and they are not doing that.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\ConsentPromptBehaviorUser", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["User account control", "Local Policies-User Account Control"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{19a185ff-1009-4079-937a-dace5e3c2f50}", "AZID": "CCE-36533-8", "Name": "Ensure 'User Account Control: Detect application installations and prompt for elevation' is set to 'Enabled'", "Description": "This policy setting controls the behavior of application installation detection for the computer. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "Some malicious software will attempt to install itself after being given permission to run. For example, malicious software with a trusted application shell. The user may have given permission for the program to run because the program is trusted, but if they are then prompted for installation of an unknown component this provides another way of trapping the software before it can do damage", "Impact": "When an application installation package is detected that requires elevation of privilege, the user is prompted to enter an administrative user name and password. If the user enters valid credentials, the operation continues with the applicable privilege.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\EnableInstallerDetection", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["User account control", "Local Policies-User Account Control"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{600ea254-773b-43b5-be89-ca8221e96279}", "AZID": "CCE-37057-7", "Name": "Ensure 'User Account Control: Only elevate UIAccess applications that are installed in secure locations' is set to 'Enabled'", "Description": "This policy setting controls whether applications that request to run with a User Interface Accessibility (UIAccess) integrity level must reside in a secure location in the file system. Secure locations are limited to the following: - `…Program Files`, including subfolders - `…Windowssystem32` - `…Program Files (x86)`, including subfolders for 64-bit versions of Windows **Note:** Windows enforces a public key infrastructure (PKI) signature check on any interactive application that requests to run with a UIAccess integrity level regardless of the state of this security setting. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "UIAccess Integrity allows an application to bypass User Interface Privilege Isolation (UIPI) restrictions when an application is elevated in privilege from a standard user to an administrator. This is required to support accessibility features such as screen readers that are transmitting user interfaces to alternative forms. A process that is started with UIAccess rights has the following abilities: - To set the foreground window. - To drive any application window using SendInput function. - To use read input for all integrity levels using low-level hooks, raw input, GetKeyState, GetAsyncKeyState, and GetKeyboardInput. - To set journal hooks. - To uses AttachThreadInput to attach a thread to a higher integrity input queue.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\EnableSecureUIAPaths", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["User account control", "Local Policies-User Account Control"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{1d099cbe-a327-42cd-9562-9896389c4263}", "AZID": "CCE-36869-6", "Name": "Ensure 'User Account Control: Run all administrators in Admin Approval Mode' is set to 'Enabled'", "Description": "This policy setting controls the behavior of all User Account Control (UAC) policy settings for the computer. If you change this policy setting, you must restart your computer. The recommended state for this setting is: `Enabled`. **Note:** If this policy setting is disabled, the Security Center notifies you that the overall security of the operating system has been reduced.", "Severity": "Critical", "Vulnerability": "This is the setting that turns on or off UAC. If this setting is disabled, UAC will not be used and any security benefits and risk mitigations that are dependent on UAC will not be present on the system.", "Impact": "None - this is the default configuration. Users and administrators will need to learn to work with UAC prompts and adjust their work habits to use least privilege operations.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\EnableLUA", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["User account control", "Local Policies-User Account Control"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{21a9a771-ef63-419c-bee4-8619f19a77ff}", "AZID": "CCE-36866-2", "Name": "Ensure 'User Account Control: Switch to the secure desktop when prompting for elevation' is set to 'Enabled'", "Description": "This policy setting controls whether the elevation request prompt is displayed on the interactive user's desktop or the secure desktop. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "Standard elevation prompt dialog boxes can be spoofed, which may cause users to disclose their passwords to malicious software. The secure desktop presents a very distinct appearance when prompting for elevation, where the user desktop dims, and the elevation prompt UI is more prominent. This increases the likelihood that users who become accustomed to the secure desktop will recognize a spoofed elevation prompt dialog box and not fall for the trick.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\PromptOnSecureDesktop", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["User account control", "Local Policies-User Account Control"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{61f7469c-c76a-4265-b84f-d838adb06436}", "AZID": "CCE-37064-3", "Name": "Ensure 'User Account Control: Virtualize file and registry write failures to per-user locations' is set to 'Enabled'", "Description": "This policy setting controls whether application write failures are redirected to defined registry and file system locations. This policy setting mitigates applications that run as administrator and write run-time application data to: - `%ProgramFiles%`, - `%Windir%`, - `%Windir%system32`, or - `HKEY_LOCAL_MACHINESoftware`. The recommended state for this setting is: `Enabled`.", "Severity": "Critical", "Vulnerability": "This setting reduces vulnerabilities by ensuring that legacy applications only write data to permitted locations.", "Impact": "None - this is the default configuration.", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\\EnableVirtualization", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["User account control", "Local Policies-User Account Control"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{23d0f843-e7bf-40e9-82cb-6299b35e52ab}", "AZID": "AZ-WIN-00147", "Name": "Increase a process working set", "Description": "This privilege determines which user accounts can increase or decrease the size of a process’s working set. The working set of a process is the set of memory pages currently visible to the process in physical RAM memory. These pages are resident and available for an application to use without triggering a page fault. The minimum and maximum working set sizes affect the virtual memory paging behavior of a process.\n\n\nWhen configuring a user right in the SCM enter a comma delimited list of accounts. Accounts can be either local or located in Active Directory, they can be groups, users, or computers.", "Severity": "Important", "Vulnerability": "", "Impact": "", "DataSourceType": "Policy", "DataSourceKey": "[Privilege Rights]SeIncreaseWorkingSetPrivilege", "ExpectedValue": "Administrators, Local Service", "RemediateValue": "Administrators, Local Service", "Remediate": "false", "ValueType": "USERRIGHTS", "AnalyzeOperation": "LESSOREQUAL", "Categories": ["User Rights", "Local Policies-User Rights Assignment"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{f930f193-62e5-42f8-95b7-3bcda57a2d77}", "AZID": "AZ-WIN-00148", "Name": "Recovery console: Allow floppy copy and access to all drives and all folders", "Description": "This policy setting makes the Recovery Console SET command available, which allows you to set the following recovery console environment variables:\n• AllowWildCards. Enables wildcard support for some commands (such as the DEL command).\n• AllowAllPaths. Allows access to all files and folders on the computer.\n• AllowRemovableMedia. Allows files to be copied to removable media, such as a floppy disk.\n• NoCopyPrompt. Does not prompt when overwriting an existing file.", "Severity": "Important", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "Software\\Microsoft\\Windows NT\\CurrentVersion\\Setup\\RecoveryConsole\\setcommand", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: Devices", "Local Policies-Recovery console"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{8768017b-9dcb-42a3-990c-03005d475145}", "AZID": "AZ-WIN-00149", "Name": "Require user authentication for remote connections by using Network Level Authentication", "Description": "Require user authentication for remote connections by using Network Level Authentication", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Windows NT\\Terminal Services\\UserAuthentication", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: RDP", "Administrative Templates (Computer)-Security"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{21ce3152-2257-4fa8-a9f9-93c19a91fe2c}", "AZID": "AZ-WIN-00150", "Name": "Set SMB v1 client (MRxSMB10) to disabled", "Description": "Configures the SMB v1 client driver's start type.\n\nTo disable client-side processing of the SMBv1 protocol, select the \"Enabled\" radio button, then select \"Disable driver\" from the dropdown.\nWARNING: DO NOT SELECT THE \"DISABLED\" RADIO BUTTON UNDER ANY CIRCUMSTANCES!\n\nTo restore default SMBv1 client-side behavior, select \"Enabled\" and choose \"Manual start\" for Win7-2008R2, or \"Automatic start\" for Win8.1-2012R2-newer.\n\nFor Win7-2008R2, you must also configure the \"Configure SMB v1 client (extra setting needed for Win7-2008R2)\" setting.\n\nChanges to this setting require a reboot to take effect.\n\nFor more information, see https:--support.microsoft.com-kb-2696547\n  ", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "SYSTEM\\CurrentControlSet\\Services\\mrxsmb10\\Start", "ExpectedValue": "4", "RemediateValue": "4", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["ASR: SMB", "Local Policies-Network access"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{7470f80e-a3d3-4ca9-84e8-7a97a317b2e1}", "AZID": "AZ-WIN-00151", "Name": "Shutdown: Clear virtual memory pagefile", "Description": "This policy setting determines whether the virtual memory pagefile is cleared when the system is shut down. When this policy setting is enabled, the system pagefile is cleared each time that the system shuts down properly. If you enable this security setting, the hibernation file (Hiberfil.sys) is zeroed out when hibernation is disabled on a portable computer system. It will take longer to shut down and restart the computer, and will be especially noticeable on computers with large paging files.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "System\\CurrentControlSet\\Control\\Session Manager\\Memory Management\\ClearPageFileAtShutdown", "ExpectedValue": "0", "RemediateValue": "0", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALSORNOTEXISTS", "Categories": ["Data protection", "Local Policies-Shutdown"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{ea844d61-d0ae-412d-bcd9-558776e708f0}", "AZID": "AZ-WIN-00152", "Name": "Specify the interval to check for definition updates", "Description": "This policy setting allows you to specify an interval at which to check for definition updates. The time value is represented as the number of hours between update checks. Valid values range from 1 (every hour) to 24 (once per day).\n\nIf you enable this setting, checks for definition updates will occur at the interval specified.\n\nIf you disable or do not configure this setting, checks for definition updates will occur at the default interval.", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Microsoft Antimalware\\Signature Updates\\SignatureUpdateInterval", "ExpectedValue": "8", "RemediateValue": "8", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Antimalware", "Administrative Templates (Computer)-Windows Defender"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{51173F6A-ECFA-49B3-9C4B-A1C51EC90CD4}", "AZID": "AZ-WIN-00153", "Name": "SSL Cipher Suite Order", "Description": "This policy setting determines the cipher suites used by the Secure Socket Layer (SSL).\n\n          If you enable this policy setting, SSL cipher suites are prioritized in the order specified.\n\n          If you disable or do not configure this policy setting, the factory default cipher suite order is used.\n", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Cryptography\\Configuration\\SSL\\00010002\\Functions", "ExpectedValue": "Intentionally_notequal", "RemediateValue": "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384_P384,TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256_P256,TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384_P384,TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256_P256,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384_P384,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256_P256,TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA_P384,TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA_P256,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA_P384,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA_P256,TLS_RSA_WITH_AES_256_GCM_SHA384,TLS_RSA_WITH_AES_128_GCM_SHA256,TLS_RSA_WITH_AES_256_CBC_SHA256,TLS_RSA_WITH_AES_128_CBC_SHA256,TLS_RSA_WITH_AES_256_CBC_SHA,TLS_RSA_WITH_AES_128_CBC_SHA,TLS_RSA_WITH_3DES_EDE_CBC_SHA", "Remediate": "false", "ValueType": "REG_SZ", "AnalyzeOperation": "NOTEQUAL", "Categories": ["ASR: Encryption", "Local Policies-System cryptography"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{98207C53-1EDB-454F-8E84-B4FD45022E8D}", "AZID": "AZ-WIN-00154", "Name": "SSL Cipher Suite Order", "Description": "This policy setting determines the cipher suites used by the Secure Socket Layer (SSL).\n\n          If you enable this policy setting, SSL cipher suites are prioritized in the order specified.\n\n          If you disable or do not configure this policy setting, the factory default cipher suite order is used.\n", "Severity": "Critical", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "SOFTWARE\\Policies\\Microsoft\\Cryptography\\Configuration\\SSL\\00010002\\Functions", "ExpectedValue": "Intentionally_notequal", "RemediateValue": "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384,TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA,TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,TLS_RSA_WITH_AES_256_GCM_SHA384,TLS_RSA_WITH_AES_128_GCM_SHA256,TLS_RSA_WITH_AES_256_CBC_SHA256,TLS_RSA_WITH_AES_128_CBC_SHA256,TLS_RSA_WITH_AES_256_CBC_SHA,TLS_RSA_WITH_AES_128_CBC_SHA,TLS_RSA_WITH_3DES_EDE_CBC_SHA", "Remediate": "false", "ValueType": "REG_SZ", "AnalyzeOperation": "NOTEQUAL", "Categories": ["ASR: Encryption", "Local Policies-System cryptography"], "Filter": ["OSVersion = [WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "true"}, {"RuleId": "{0360374a-c842-40c2-9f1a-8149cd273c84}", "AZID": "AZ-WIN-00155", "Name": "System settings: Use Certificate Rules on Windows Executables for Software Restriction Policies", "Description": "This policy setting determines whether digital certificates are processed when software restriction policies are enabled and a user or process attempts to run software with an .exe file name extension. It enables or disables certificate rules (a type of software restriction policies rule). With software restriction policies, you can create a certificate rule that will allow or disallow the execution of Authenticode®-signed software, based on the digital certificate that is associated with the software. For certificate rules to take effect in software restriction policies, you must enable this policy setting.", "Severity": "Important", "Vulnerability": "", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "Software\\Policies\\Microsoft\\Windows\\Safer\\CodeIdentifiers\\AuthenticodeEnabled", "ExpectedValue": "1", "RemediateValue": "1", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: Encryption", "Local Policies-System settings"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}, {"RuleId": "{67118875-937A-4775-9333-6092552859FA}", "AZID": "AZ-WIN-00156", "Name": "Detect change from default RDP port", "Description": "This setting reports FAIL if the Remote Desktop Connection (RDC) port has been manually changed from 3389.", "Severity": "Critical", "Vulnerability": "Changing the default RDC port may be an attempt to evade security controls.", "Impact": "", "DataSourceType": "Registry", "DataSourceKey": "System\\CurrentControlSet\\Control\\Terminal Server\\WinStations\\RDP-Tcp\\PortNumber", "ExpectedValue": "3389", "RemediateValue": "3389", "Remediate": "false", "ValueType": "REG_DWORD", "AnalyzeOperation": "EQUALS", "Categories": ["ASR: RDP", "Administrative Templates (Computer)-Security"], "Filter": ["OSVersion = [WS2008, WS2008R2, WS2012, WS2012R2, WS2016]", "ServerType = [Domain Controller, Domain Member, Workgroup Member]"], "Enabled": "true", "Alertable": "false"}]}