<?xml version="1.0" encoding="utf-8"?>

<!-- AsmInventoryConfiguration indicates this is a Inventory schema [Required] -->
<AsmExtendedScannerConfiguration>
  <!-- Each Item is a EventDriven Scan Scenario; lists the details of the Inventory to be collected-->
  <!--
		<ExtendedScannerInfo librarypath = <Path to the Native Dll to be loaded that exports the function to be called.>
				   functionname = <Name of the function that returns the packed inventory data>
		</ExtendedScannerInfo>
	-->
  
  <ExtendedScannerInfo scenarioname="SystemTimeChange"
            librarypath = "SystemTimeEvents.dll"
						queryfunctionname="getSystemTimeEventQuery"
            callbackfunctionname="processSystemTimeEvent" >
  </ExtendedScannerInfo>
  <!--Dll for Testing purposes-->
  <!--<ExtendedScannerInfo scenarioname="Testing"
            librarypath = "EventDriven-Test.dll"
						queryfunctionname="getTestEventQuery"
            callbackfunctionname="processTestEvents" >
  </ExtendedScannerInfo>-->
</AsmExtendedScannerConfiguration>