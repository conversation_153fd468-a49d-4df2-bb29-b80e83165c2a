﻿<?xml version='1.0' encoding='utf-8' standalone='yes'?>
<instrumentationManifest xmlns="http://schemas.microsoft.com/win/2004/08/events">
  <instrumentation
      xmlns:win="http://manifests.microsoft.com/win/2004/08/windows/events"
      xmlns:xs="http://www.w3.org/2001/XMLSchema"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      >
    <events xmlns="http://schemas.microsoft.com/win/2004/08/events">
      <provider
          guid="{648f8286-**************-86da03c4e4ef}"
          message="$(string.providermessage)"
          messageFileName="%systemroot%\Windows.Azure.Auditing.Logging.ETWLogging.dll"
          name="WindowsAzure-ETWProvider"
          resourceFileName="%systemroot%\Windows.Azure.Auditing.Logging.ETWLogging.dll"
          symbol="WindowsAzureETWProvider"
          >
        <channels>
          <importChannel
              chid="C1"
              name="Application"
              />
          <!--Direct channels - type Analytic or Debug-->
          <channel
              chid="C2"
              enabled="true"
              isolation="Application"
              message="$(string.DebugChannelMessage)"
              name="WindowsAzure-ETWProvider/Debug"
              symbol="Debug_CHANNEL"
              type="Debug"
              >
            <logging>
              <!-- max size is 1 GB.  Log will be rolled over after that.-->
              <retention>false</retention>
              <maxSize>**********</maxSize>
            </logging>
            <publishing>
              <latency>0</latency>
            </publishing>
          </channel>
        </channels>
        <templates>
          <template tid="t1">
            <data
              inType="win:UnicodeString"
              name="AlertId"
            />    
            <data
                inType="win:UnicodeString"
                name="FreetextMessage"
                />
            <data
              inType="win:UnicodeString"
              name="SearchableNameValuePairs"
                />
            <data
              inType="win:UnicodeString"
              name="PIISearchableNameValuePairs"
                />
          </template>
          <template tid="t2">
            <data
              inType="win:UnicodeString"
              name="AlertId"
            />
            <data
                inType="win:UnicodeString"
                name="data"
                />
          </template>
        </templates>
        <events>
          <event
              channel="C1"
              level="win:Error"        
              message="$(string.event)"
              symbol="Identity_Event_Log_Error"              
              template="t2"
              value="101"
              />
          <event
              channel="C1"
              level="win:Informational"
              message="$(string.event2)"
              symbol="Identity_Event_Log_Informational"
              template="t2"
              value="102"
              />
          <event              
              level="win:Verbose"
              channel="C2"              
              symbol="Identity_Debug_Log_Verbose"
              template="t1"
              value="001"
              />
          <event
              channel="C2"
              level="win:Informational"              
              symbol="Identity_Debug_Log_Informational"
              template="t1"
              value="002"
              />
          <event
              channel="C2"
              level="win:Error"
              symbol="Identity_Debug_Log_Error"
              template="t1"
              value="003"
              />
        </events>
      </provider>
    </events>
  </instrumentation>
  <localization>
    <resources culture="en-US">
      <stringTable>
        <string
          id="providermessage"
          value="Windows Azure Auditing"
          />
        <string
          id="DebugChannelMessage"
          value="Windows Azure Auditing"
          />
        <string
            id="event"
            value="%1"
            />
        <string
          id="event2"
          value="%1"
            />
        <string
          id="event3"
          value="Message:%1. Data: %2"
            />
      </stringTable>
    </resources>
  </localization>
</instrumentationManifest>
