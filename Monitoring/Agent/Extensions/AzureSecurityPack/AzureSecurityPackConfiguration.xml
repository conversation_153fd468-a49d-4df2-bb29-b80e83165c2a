<?xml version="1.0" encoding="utf-8"?>
<AzureSecurityPack xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" service="AzSecPackDefault" timestamp="2018-11-22T05:37:43.7070884Z" version="********">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Features>
    <Feature name="Audit" enabled="true">
      <StartupCommand isScript="true">AuditInstaller.cmd</StartupCommand>
    </Feature>
    <Feature name="LegacyAudit" enabled="false">
      <StartupCommand isScript="true">LegacyAuditInstaller.cmd</StartupCommand>
    </Feature>
    <Feature name="Antimalware" enabled="true">
      <StartupCommand>AntimalwareInstall.cmd</StartupCommand>
    </Feature>
    <Feature name="AsmScan" enabled="true">
      <StartupCommand>AsmScan.bat</StartupCommand>
    </Feature>
    <Feature name="SystemSecurityLog" enabled="true">
      <StartupCommand isScript="true">SlamAuditPolicy.cmd</StartupCommand>
    </Feature>
    <Feature name="AppLocker" enabled="true">
      <StartupCommand isScript="true">EnableAppLocker.cmd</StartupCommand>
      <DisableCommand isScript="true">DisableAppLocker.cmd</DisableCommand>
    </Feature>
    <Feature name="CodeIntegrity" enabled="true">
      <StartupCommand isScript="true">EnableCodeIntegrity.cmd</StartupCommand>
      <DisableCommand isScript="true">DisableCodeIntegrity.cmd</DisableCommand>
    </Feature>
    <Feature name="WDATP" enabled="true" isPilot="true">
      <StartupCommand isScript="true">WDATPPilot.cmd</StartupCommand>
    </Feature>
  </Features>
</AzureSecurityPack>