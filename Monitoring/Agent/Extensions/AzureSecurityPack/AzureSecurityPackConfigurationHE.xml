<?xml version="1.0" encoding="utf-8"?>
<AzureSecurityPack xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" service="HostAgent" timestamp="2018-11-22T05:37:43.7070884Z" version="********">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Features>
    <Feature name="AsmScan" enabled="true">
      <StartupCommand>AsmScan.bat</StartupCommand>
    </Feature>
    <Feature name="SystemSecurityLog" enabled="true">
      <StartupCommand isScript="true">SlamAuditPolicy.cmd</StartupCommand>
    </Feature>
    <Feature name="AppLocker" enabled="true">
      <StartupCommand isScript="true">EnableAppLockerHE.cmd</StartupCommand>
    </Feature>
    <Feature name="Audit" enabled="true">
      <StartupCommand isScript="true">AuditInstaller.cmd</StartupCommand>
    </Feature>
  </Features>
</AzureSecurityPack>