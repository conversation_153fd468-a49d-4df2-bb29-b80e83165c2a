AzureSecurityPack Version 4.6.3.12
Component package versions:
<?xml version="1.0" encoding="utf-8"?>
<packages>
<package id="Microsoft.WindowsAzure.EngSys.Internal.rd_security_n.Auditing_Redist" version="17.2.12.6" targetFramework="net45" />
<package id="AsmSlamAzSecPack" version="4.2.0.31" targetFramework="net45" />
<package id="AsmAntimalwareInternal" version="1.5.5.11" targetFramework="net45" />
<package id="AsmScannersInternal" version="4.6.3.6" targetFramework="net45" />
<package id="Microsoft.EngSys.SystemLockdown.AppLocker" version="1.0.0.39" targetFramework="net45" />
<package id="Microsoft.EngSys.SystemLockdown.CodeIntegrity" version="1.0.0.29" targetFramework="net45" />
<package id="NetIsoScanner" version="2018.11.13.1" targetFramework="native"/>
<package id="Microsoft.Wcd.Sense.MsSenseS" version="10.3720.16299.1008" targetFramework="native"/>
<package id="ProcessInvestigator.Native.AzSecPack" version="1.18.909.1" targetFramework="native"/>
<package id="ShaVsa.OffNode.AzSecPack" version="1.2.4.44" targetFramework="native"/>
</packages>
