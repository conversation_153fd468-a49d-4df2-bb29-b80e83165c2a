{"UsersMapping": [{"productName": "Microsoft Azure Service Fabric", "usernamePattern": "^WF-.*|^P_FSSUserffffffff|^S_FSSUserffffffff"}, {"productName": "Microsoft Dynamics NAV", "usernamePattern": "^ClickOnceUser"}, {"productName": "Microsoft Network Monitor 3.4", "usernamePattern": "^Netmon\\sUsers"}], "GroupsMapping": [{"productName": "Microsoft Azure Service Fabric", "groupnamePattern": "WF-App-*|^FSSGroupffffffff"}, {"productName": "Microsoft Azure VMAccess Extension", "groupnamePattern": "^Microsoft\\sAzure\\sRemote\\sAccess\\sUsers"}, {"productName": "Microsoft Dynamics CRM Server", "groupnamePattern": "^SQLAccessGroup*"}, {"productName": "Microsoft Visual Studio Team Foundation Server", "groupnamePattern": "^VSTS_AgentService*"}, {"productName": "Microsoft StreamInsight 1.2 (x64) (en)", "groupnamePattern": "^StreamInsightUsers"}, {"productName": "Microsoft Network Monitor 3.4", "groupnamePattern": "^Netmon\\sUsers|^NETMON"}, {"productName": "Microsoft System Center 2016  DPM Protection Agent", "groupnamePattern": "^DPMRADmTrustedMachines|^DPMRADCOMTrustedMachines|^DPMRATrustedDPMRAs"}, {"productName": "Forefront Identity Manager Synchronization Service", "groupnamePattern": "^FIMSyncOperators$|^FIMSyncJoiners$|^FIMSyncBrowse$|^FIMSyncPasswordSet$"}], "DefaultSecurityGroups": [{"groupName": "Access Control Assistance Operators"}, {"groupName": "Account Operators"}, {"groupName": "Administrators"}, {"groupName": "Allowed RODC Password Replication Group"}, {"groupName": "Backup Operators"}, {"groupName": "Certificate Service DCOM Access"}, {"groupName": "Cert Publishers"}, {"groupName": "Cloneable Domain Controllers"}, {"groupName": "Cryptographic Operators"}, {"groupName": "Denied RODC Password Replication Group"}, {"groupName": "Distributed COM Users"}, {"groupName": "DnsUpdateProxy"}, {"groupName": "DnsAdmins"}, {"groupName": "Domain Admins"}, {"groupName": "Domain Computers"}, {"groupName": "Domain Controllers"}, {"groupName": "Domain Guests"}, {"groupName": "Domain Users"}, {"groupName": "Enterprise Admins"}, {"groupName": "Enterprise Read-Only Domain Controllers"}, {"groupName": "Event Log Readers"}, {"groupName": "Group Policy Creators Owners"}, {"groupName": "Guests"}, {"groupName": "Hyper-V Administrators"}, {"groupName": "IIS_IUSRS"}, {"groupName": "Incoming Forest Trust Builders"}, {"groupName": "Network Configuration Operators"}, {"groupName": "Performance Log Users"}, {"groupName": "Performance Monitor Users"}, {"groupName": "Pre–Windows 2000 Compatible Access"}, {"groupName": "Print Operators"}, {"groupName": "Protected Users"}, {"groupName": "RAS and IAS Servers"}, {"groupName": "RDS Endpoint Servers"}, {"groupName": "RDS Management Servers"}, {"groupName": "RDS Remote Access Servers"}, {"groupName": "Read-Only Domain Controllers"}, {"groupName": "Remote Desktop Users"}, {"groupName": "Remote Management Users"}, {"groupName": "Replicator"}, {"groupName": "<PERSON><PERSON><PERSON>"}, {"groupName": "Server Operators"}, {"groupName": "Terminal Server License Servers"}, {"groupName": "Users"}, {"groupName": "Windows Authorization Access Group"}, {"groupName": "WinRMRemoteWMIUsers_"}]}