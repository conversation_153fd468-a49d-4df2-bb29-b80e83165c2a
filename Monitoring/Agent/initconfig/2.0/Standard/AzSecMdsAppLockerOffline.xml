<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <!--
    All AppLocker event logs are collected.
    
    All MDS tables declared in this file use one of the following MDS account aliases that must be declared in the service's main MDS configuration.
    This enables uploading different classes of data to different storage accounts, but is not required. The aliases provide a logical storage 
    account mapping that can all write to a single account or to separate accounts depending on how the services chooses to secure its data.
    The aliases here are:
        AuditStore - associated with tables that may include events with PII
        AzSecurityStore - associated with all other security monitoring tables that do not contain PII
    
    Revision 9/11/2017 Payalr - updated as per unified schema for AzSecPack Auto update
    Revision 3/7/2017 Payalr - Added filters to only query AppIDsvc status and filter out compliance events from Alerts table
    -->
  <Events>
    <WindowsEventLogSubscriptions>
      <!-- 
            Collects everything from AppLocker EXE and DLL channels.
            -->
      <Subscription eventName="LocalALEXE" query="Microsoft-Windows-AppLocker/EXE and DLL!*" storeType="Local">
        <Column name="EventProvider" defaultAssignment="">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType" defaultAssignment="0">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated" defaultAssignment="">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/</Value>
        </Column>
      </Subscription>
      <!-- 
            Collects everything from AppLocker MSI and SCRIPT channels. Events here are routed to audit store
            -->
      <Subscription eventName="LocalALSCR" query="Microsoft-Windows-AppLocker/MSI and Script!*[System[(EventID=8005) or (EventID=8006) or (EventID=8007) ]]" storeType="Local">
        <Column name="EventProvider" defaultAssignment="">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType" defaultAssignment="0">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated" defaultAssignment="">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/</Value>
        </Column>
      </Subscription>
      <!-- Collects everything from AppLocker Packaged app deployment channels. -->
      <Subscription eventName="LocalALAPPDEP" query="Microsoft-Windows-AppLocker/Packaged app-Deployment!*" storeType="Local">
        <Column name="EventProvider" defaultAssignment="">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType" defaultAssignment="0">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated" defaultAssignment="">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/</Value>
        </Column>
      </Subscription>
      <!-- Collects everything from AppLocker Packaged app execution channels.-->
      <Subscription eventName="LocalALAPPEXE" query="Microsoft-Windows-AppLocker/Packaged app-Execution!*" storeType="Local">
        <Column name="EventProvider" defaultAssignment="">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType" defaultAssignment="0">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated" defaultAssignment="">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/</Value>
        </Column>
      </Subscription>
      <Subscription eventName="LocalALSvcMgr" query="System!*[System[Provider[@Name='Service Control Manager'] and (EventID=7036)]] and *[EventData[Data[@Name='param1'] and (Data='Application Identity')]and EventData[Data[@Name='param2'] and (Data='stopped')]]" storeType="Local">
        <Column name="EventProvider" defaultAssignment="">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType" defaultAssignment="0">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated" defaultAssignment="">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/</Value>
        </Column>
      </Subscription>
      <Subscription eventName="LocalALDel" query="System!*[System[Provider[@Name='Microsoft-Windows-Eventlog'] and (EventID=104)] and UserData[LogFileCleared[(Channel='Microsoft-Windows-AppLocker/EXE and DLL') or (Channel='Microsoft-Windows-AppLocker/MSI and Script') or (Channel='Microsoft-Windows-AppLocker/Packaged app-Deployment') or (Channel='Microsoft-Windows-AppLocker/Packaged app-Execution')]]]" storeType="Local">
        <Column name="EventProvider" defaultAssignment="">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType" defaultAssignment="0">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated" defaultAssignment="">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/</Value>
        </Column>
      </Subscription>
    </WindowsEventLogSubscriptions>
    <DerivedEvents>
      <!-- Aggregated Events from Exe/Dll channel -->
      <DerivedEvent source="LocalALEXE" duration="PT5M" eventName="AsmSLALExe" priority="Low" retryTimeout="PT10080M" account="AzSecurityStore" storeType="CentralBond" whereToRun="Local">
        <Query><![CDATA[
                      let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")
                      let AssetIdentity = GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
                      let NodeIdentity=""
                      let NodeType=""
                      let PlatformType="NonHE"
                      let Version="4.5"
                      Select ReportingIdentity,
                          AssetIdentity,
                          NodeIdentity, 
                          NodeType,
                          EventProvider,
                          EventType, 
                          TimeCreated,
                          EventPayload,
                          PlatformType,
                          Version
                    ]]></Query>
      </DerivedEvent>
      <!-- Aggregated Events from Msi/Script channel -->
      <DerivedEvent source="LocalALSCR" duration="PT5M" eventName="AsmSLALScr" priority="Low" retryTimeout="PT10080M" account="AzSecurityStore" storeType="CentralBond" whereToRun="Local">
        <Query><![CDATA[
                      let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")
                      let AssetIdentity = GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
                      let NodeIdentity=""
                      let NodeType=""
                      let PlatformType="NonHE"
                      let Version="4.5"
                      Select ReportingIdentity,
                          AssetIdentity,
                          NodeIdentity, 
                          NodeType,
                          EventProvider,
                          EventType, 
                          TimeCreated,
                          EventPayload,
                          PlatformType,
                          Version
                     ]]></Query>
      </DerivedEvent>
      <!-- Aggregated Events from AppLocker Packaged app deploy and exe channels -->
      <DerivedEvent sourceRegex="LocalALAPP.+" duration="PT5M" eventName="AsmSLALApps" priority="Low" retryTimeout="PT10080M" account="AzSecurityStore" storeType="CentralBond" whereToRun="Local">
        <Query><![CDATA[
                      let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")
                      let AssetIdentity = GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
                      let NodeIdentity=""
                      let NodeType=""
                      let PlatformType="NonHE"
                      let Version="4.5"
                      Select ReportingIdentity,
                          AssetIdentity,
                          NodeIdentity, 
                          NodeType,
                          EventProvider,
                          EventType, 
                          TimeCreated,
                          EventPayload,
                          PlatformType,
                          Version
                ]]></Query>
      </DerivedEvent>
      <!-- Alerts for stopped Application Identity Service,AppLocker log clear event and AppLocker warnings or errors from all AppLocker channels -->
      <DerivedEvent sourceRegex="LocalAL.+" duration="PT5M" eventName="AsmSLALData" priority="Low" retryTimeout="PT10080M" account="AzSecurityStore" storeType="CentralBond" whereToRun="Local">
        <Query><![CDATA[
                      let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")
                      let AssetIdentity = GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
                      let NodeIdentity=""
                      let NodeType=""
                      let PlatformType="NonHE" 
                      let Version="4.5"
                          Select ReportingIdentity,
                          AssetIdentity,
                          NodeIdentity, 
                          NodeType,
                          EventProvider,
                          EventType, 
                          TimeCreated,
                          EventPayload,
                          PlatformType,
                          Version
                      where EventType == "8003" || EventType == "8004" || EventType == "8006" || EventType == "8007" || EventType == "7036" || EventType == "104"
                     ]]></Query>
      </DerivedEvent>
    </DerivedEvents>
  </Events>
</MonitoringManagement>