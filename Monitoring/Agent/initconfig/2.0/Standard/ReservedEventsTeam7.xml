<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2017-11-01T22:18:05.3320241Z">
  <Imports>
    <Import file="ReservedEventsTeam7Offline.xml" forceLocal="true" />
  </Imports>
  
  <Events>
	  <EventDeclarations storeType="CentralBond">
			<Declaration eventName="AsmSec7Data" account="AzSecurityStore"/>
			<Declaration eventName="AsmSec7Diag" account="AzSecurityStore"/>
			<Declaration eventName="AsmSec7Alert" account="AzSecurityStore"/>
			<Declaration eventName="AsmSec7Inv" account="AzSecurityStore"/>
			<Declaration eventName="AsmSec7AdtInv" account="AuditStore"/>
			<Declaration eventName="AsmSec7AdtData" account="AuditStore"/>
	  </EventDeclarations>
   
    <FileMonitors>
      <FileWatchItem eventName="AsmSec7FM" account="AzSecurityStore" storeType="CentralBond" container="azsecasmfmevent" />
    </FileMonitors>

  </Events>
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSec7Data$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSec7Inv$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>