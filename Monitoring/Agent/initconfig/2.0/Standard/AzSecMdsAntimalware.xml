<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Imports>
    <Import file="AzSecMdsAntimalwareOffline.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <!-- Events that are already known to the team -->
      <Declaration eventName="AsmSysAV" account="AzSecurityStore" />
      <!-- Create a few more entries for reserved columns-->
      <Declaration eventName="AsmSysAVInv" account="AzSecurityStore" />
      <Declaration eventName="AsmSysAVAL" account="AzSecurityStore" />
    </EventDeclarations>
    <FileMonitors>
      <FileWatchItem eventName="AsmSysAvFMEvent" account="AzSecurityStore" storeType="CentralBond" container="azsecsysavfm" />
    </FileMonitors>
  </Events>
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSysAV$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysAVInv$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>