<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Imports>
    <Import file="AzSecMdsAsmScanOfflineHE.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <!-- Events that are already known to the team -->
      <Declaration eventName="AsmSpDiag" account="MdsCHostShared" />
      <Declaration eventName="AsmSpAlert" account="MdsCHostShared" />
      <Declaration eventName="AsmSpCfgBase" account="MdsCHostShared" />
      <Declaration eventName="AsmSpPatch" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvPrdt" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvCfg" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvCert" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvKey" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvNet" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvRPC" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvDrv" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvSrvc" account="MdsCHostShared" />
      <Declaration eventName="AsmSpRegistry" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvSysLoc" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvUG" account="MdsCHostShared" />
      <Declaration eventName="AsmSpVer" account="MdsCHostShared" />
      <!-- Create a few more entries for reserved columns-->
      <Declaration eventName="AsmSpAlRes1" account="MdsCHostShared" />
      <Declaration eventName="AsmSpAlRes2" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvRes1" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvRes2" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvRes3" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvRes4" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvRes5" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvRes6" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvRes7" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvRes8" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvRes9" account="MdsCHostShared" />
      <Declaration eventName="AsmSpInvRes10" account="MdsCHostShared" />
      <Declaration eventName="AsmSpDataRes1" account="MdsCHostShared" />
      <Declaration eventName="AsmSpDataRes2" account="MdsCHostShared" />
      <Declaration eventName="AsmSpDataRes3" account="MdsCHostShared" />
      <Declaration eventName="AsmSpDataRes4" account="MdsCHostShared" />
      <Declaration eventName="AsmSpDataRes5" account="MdsCHostShared" />
      <Declaration eventName="AsmSpDataRes6" account="MdsCHostShared" />
    </EventDeclarations>
    <FileMonitors>
      <FileWatchItem eventName="AsmSpFMEvent" account="MdsCHostShared" storeType="CentralBond" container="azsecasmfmevent" />
    </FileMonitors>
  </Events>
  <!--Enable Cosmos data upload for Inventory Data specific tables.-->
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSpCfgBase$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpPatch$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvPrdt$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvCfg$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvCert$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvNet$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvRPC$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvDrv$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvSrvc$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpRegistry$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvSysLoc$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvUG$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpVer$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvRes1$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvRes2$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvRes3$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvRes4$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvRes5$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpDataRes1$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpDataRes2$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpDataRes3$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>