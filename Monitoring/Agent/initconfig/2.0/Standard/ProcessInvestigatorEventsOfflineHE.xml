<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=********-nia1709261429 -->
  <Events>
    <DerivedEvents>
      <!-- Process Investigator scanner -->
      <DerivedEvent source="AsmDiagnostics" duration="PT5M" eventName="AsmPiDiag" account="MdsCHostShared" priority="Normal" deadline="PT15M" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
            where EventProvider = "PILauncher" && (EventType = "Error" || EventType = "Warning" || EventPayload.contains("Analyis Results"))
            select ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                   EventProvider, EventType, EventPayload, Truncated,
                   TotalChunks, ChunkId,
                   ChunkReference            ]]></Query>
      </DerivedEvent>
      <DerivedEvent source="AsmAlertsData" duration="PT1M" eventName="AsmPiAlert" account="MdsCHostShared" priority="Normal" deadline="PT5M" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
            where EventProvider = "PILauncher"
            select ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                   EventProvider, EventType, EventPayload, Truncated,
                   TotalChunks, ChunkId,
                   ChunkReference            ]]></Query>
      </DerivedEvent>
    </DerivedEvents>
  </Events>
</MonitoringManagement>