<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <!--
    Standard security events generated via AuditAPI

    All Geneva Monitoring eventnames declared in this file use one of the following MDS account aliases that must be declared in the service's main MDS configuration.
    This enables uploading different classes of data to different storage accounts, but is not required. The aliases provide a logical storage
    account mapping that can all write to a single account or to separate accounts depending on how the services chooses to secure its data.
    The aliases here are:
        MdsCHostShared - used for all IFxAudit eventnames.
        MdsCHostShared - no longer used by IfxAudit. This is used by ASM Security Pack eventnames
    -->
  <Events>
    <EtwProviders>
      <EtwProvider guid="648f8286-**************-86da03c4e4ef" format="Manifest" storeType="CentralBond" manifest="Extensions\AzureSecurityPack\AuditETWProvider.man" account="MdsCHostShared" priority="Normal">
        <!-- Declare Trace events generated via use of the common audit API -->
        <Event id="0" eventName="AsmIfxAuditDiag" />
        <Event id="1" eventName="AsmIfxAuditDiag" />
        <Event id="3" eventName="AsmIfxAuditDiag" />
      </EtwProvider>
    </EtwProviders>
    <WindowsEventLogSubscriptions>
      <!-- Collect IFxAudit logs based on the audit events instrumented by the developer using the IFx library. IFxAzApp events are for application events. -->
      <Subscription eventName="AsmIfxAuditAppLocal" query="Security!*[System[Provider[@Name='WindowsAzureIfxApplicationAudits']]]" storeType="Local">
        <Column name="EventProvider">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/Event/EventData/Data | /Event/UserData/*/*</Value>
        </Column>
      </Subscription>
      <!-- Collect IFxAudit logs based on the audit events instrumented by the developer using the IFx library. IFxAzMgmt is intended for management operations for a service. -->
      <Subscription eventName="AsmIfxAuditMgmtLocal" query="Security!*[System[Provider[@Name='WindowsAzureIfxManagementAudits']]]" storeType="Local">
        <Column name="EventProvider">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/Event/EventData/Data | /Event/UserData/*/*</Value>
        </Column>
      </Subscription>
    </WindowsEventLogSubscriptions>
    <!--Enable consumption of Audit Failure IFx -->
    <IfxEvents storeType="CentralBond" account="MdsCHostShared">
      <Event id="Ifx.PartASchema/Ifx.AuditFailureSchema" eventName="AsmIFxAudit1" />
    </IfxEvents>
    <!-- Derived Events to tag the IFxAudit events with Azure Identity -->
    <DerivedEvents>
      <!--  Documentation for event fields can be found here:
            https://jarvis-west.dc.ad.msft.net/?page=documents&section=9c95f4eb-8689-4c9f-81bf-82d688e860fd&id=ac0084ad-5065-4b16-8f7d-0a5193143378#/
        -->
      <!-- Filter out HB here to prevent double uploading -->
      <DerivedEvent source="AsmIfxAuditAppLocal" eventName="AsmIfxAuditApp" storeType="CentralBond" priority="High" duration="PT1M" retryTimeout="PT10080M" account="MdsCHostShared" retentionInDays="180">
        <Query><![CDATA[
            let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")
            let AssetIdentity=GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
            let NodeIdentity=""
            let NodeType=""

            where !EventPayload.contains("IFXHeartBeatOperationIFX")

            select
              ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
              EventProvider, EventType, TimeCreated, EventPayload
          ]]></Query>
      </DerivedEvent>
      <DerivedEvent source="AsmIfxAuditMgmtLocal" eventName="AsmIfxAuditMgmt" storeType="CentralBond" priority="High" duration="PT1M" retryTimeout="PT10080M" account="MdsCHostShared" retentionInDays="180">
        <Query><![CDATA[
            let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")
            let AssetIdentity=GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
            let NodeIdentity=""
            let NodeType=""

            select
              ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
              EventProvider, EventType, TimeCreated, EventPayload
          ]]></Query>
      </DerivedEvent>
      <!-- Channels Heartbeat logs from AsmIfxAuditAppLocal to common ASM table -->
      <DerivedEvent source="AsmIfxAuditAppLocal" eventName="AsmSpVer" storeType="CentralBond" priority="Normal" duration="PT1M" deadline="PT5M" retryTimeout="PT10080M" account="MdsCHostShared" retentionInDays="30">
        <Query><![CDATA[
            let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")
            let AssetIdentity=GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
            let NodeIdentity=""
            let NodeType=""
            let EventProvider="WindowsAzureIfxApplicationAudits"
            let EventType="Heartbeat"

            where contains( EventPayload , "IFXHeartBeatOperationIFX")

            select
              ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
              EventProvider, EventType, TimeCreated, EventPayload
          ]]></Query>
      </DerivedEvent>
    </DerivedEvents>
  </Events>
</MonitoringManagement>