<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Imports>
    <Import file="AzSecMdsNetIsoScanOffline.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <!-- legacy event (azsecpack <= v4.3) -->
      <Declaration eventName="AsmInvFwRl" account="AzSecurityStore" />
      <!-- standard events (azsecpack >= v4.4) -->
      <Declaration eventName="AsmNetIsoAl" account="AzSecurityStore" />
      <Declaration eventName="AsmNetIsoData" account="AzSecurityStore" />
      <Declaration eventName="AsmNetIsoDiag" account="AzSecurityStore" />
      <Declaration eventName="AsmNetIsoInv" account="AzSecurityStore" />
    </EventDeclarations>
    <FileMonitors>
      <FileWatchItem eventName="AsmNetIsoFM" account="AzSecurityStore" storeType="CentralBond" container="azsecnetisofm" />
    </FileMonitors>
  </Events>
  <EventStreamingAnnotations>
    <!-- legacy event (azsecpack <= v4.3) -->
    <EventStreamingAnnotation name="^AsmInvFwRl$">
      <Cosmos>
        <Content><![CDATA[<Config/>]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <!-- standard events (azsecpack >= v4.4) -->
    <EventStreamingAnnotation name="^AsmNetIsoAl$">
      <Cosmos>
        <Content><![CDATA[<Config/>]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmNetIsoData$">
      <Cosmos>
        <Content><![CDATA[<Config/>]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmNetIsoDiag$">
      <Cosmos>
        <Content><![CDATA[<Config/>]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmNetIsoFM$">
      <Cosmos>
        <Content><![CDATA[<Config/>]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmNetIsoInv$">
      <Cosmos>
        <Content><![CDATA[<Config/>]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>