<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Imports>
    <Import file="ReservedEventsTeam4OfflineHE.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <Declaration eventName="AsmSec4Data" account="MdsCHostShared" />
      <Declaration eventName="AsmSec4Diag" account="MdsCHostShared" />
      <Declaration eventName="AsmSec4Alert" account="MdsCHostShared" />
      <Declaration eventName="AsmSec4Inv" account="MdsCHostShared" />
      <Declaration eventName="AsmSec4AdtInv" account="MdsCHostShared" />
      <Declaration eventName="AsmSec4AdtData" account="MdsCHostShared" />
    </EventDeclarations>
    <FileMonitors>
      <FileWatchItem eventName="AsmSec4FM" account="MdsCHostShared" storeType="CentralBond" container="azsecasmfmevent" />
    </FileMonitors>
  </Events>
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSec4Data$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSec4Inv$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>