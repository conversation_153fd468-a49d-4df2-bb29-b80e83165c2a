<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Imports>
    <Import file="IFxAuditMdsEventsOfflineHE.xml" forceLocal="true" />
  </Imports>
  <!--
 Standard security events generated via AuditAPI

    All Geneva Monitoring eventnames declared in this file use one of the following MDS account aliases that must be declared in the service's main MDS configuration.
    This enables uploading different classes of data to different storage accounts, but is not required. The aliases provide a logical storage
    account mapping that can all write to a single account or to separate accounts depending on how the services chooses to secure its data.
    The aliases here are:
        MdsCHostShared - used for all IFxAudit eventnames.
        MdsCHostShared - no longer used by IfxAudit. This is used by ASM Security Pack eventnames
    -->
  <Events>
    <EventDeclarations storeType="CentralBond">
      <!-- Events that are already known to the team -->
      <Declaration eventName="AsmIfxAuditApp" account="MdsCHostShared" />
      <Declaration eventName="AsmIfxAuditMgmt" account="MdsCHostShared" />
      <Declaration eventName="AsmIfxAuditDiag" account="MdsCHostShared" />
      <!-- AsmIfxAuditHB is not used as of 4.4.2 -->
      <Declaration eventName="AsmIfxAuditHB" account="MdsCHostShared" />
      <!-- Used for IFxAudit Failure Events-->
      <Declaration eventName="AsmIFxAudit1" account="MdsCHostShared" />
      <!-- Create a few more entries for IFxAudit reserved columns-->
      <Declaration eventName="AsmIFxAudit2" account="MdsCHostShared" />
      <Declaration eventName="AsmIFxAudit3" account="MdsCHostShared" />
      <!-- Create a few more entries for IFxAudit reserved columns - these will NOT go to Cosmos -->
      <Declaration eventName="AsmIFxAuditAdt1" account="MdsCHostShared" />
      <Declaration eventName="AsmIFxAuditAdt2" account="MdsCHostShared" />
    </EventDeclarations>
  </Events>
  <!--Enable IFxAudit events upload to Cosmos from eventhub for mgmt events-->
  <EventStreamingAnnotations>
    <!--IFxAudit eventstreaming for errors and managment events. It's not possible to know if AsmIfxAuditApp or AsmIfxAuditFail has EII so those are not automatically configured for eventstreaming-->
    <EventStreamingAnnotation name="^AsmIfxAuditMgmt$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <!--IFxAudit eventstreaming for errors and managment events. It's not possible to know if AzApp has EII so those are not automatically configured for eventstreaming-->
    <EventStreamingAnnotation name="^AsmIfxAuditHB$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <!--Reserved IFxAudit eventstreaming not currently used-->
    <EventStreamingAnnotation name="^AsmIFxAudit1$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmIFxAudit2$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmIFxAudit3$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>