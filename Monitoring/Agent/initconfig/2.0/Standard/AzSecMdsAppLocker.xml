<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <!--
    All AppLocker event logs are collected.
    
    All MDS tables declared in this file use one of the following MDS account aliases that must be declared in the service's main MDS configuration.
    This enables uploading different classes of data to different storage accounts, but is not required. The aliases provide a logical storage 
    account mapping that can all write to a single account or to separate accounts depending on how the services chooses to secure its data.
    The aliases here are:
        AuditStore - associated with tables that may include events with PII
        AzSecurityStore - associated with all other security monitoring tables that do not contain PII
    
    Revision 11/14/2017 Payalr - updated as per unified schema for AzSecPack Auto update
    -->
  <Imports>
    <Import file="AzSecMdsAppLockerOffline.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <!-- Events that are already known to the team -->
      <Declaration eventName="AsmSLALExe" account="AzSecurityStore" />
      <Declaration eventName="AsmSLALScr" account="AzSecurityStore" />
      <Declaration eventName="AsmSLALApps" account="AzSecurityStore" />
      <Declaration eventName="AsmSLALAlerts" account="AzSecurityStore" />
      <Declaration eventName="AsmSLALData" account="AzSecurityStore" />
      <!-- Few more entries for reserved columns-->
      <Declaration eventName="AsmSLALDiag" account="AzSecurityStore" />
      <Declaration eventName="AsmSLALInv" account="AzSecurityStore" />
      <Declaration eventName="AsmSLALAl" account="AzSecurityStore" />
      <Declaration eventName="AsmSLALRes" account="AzSecurityStore" />
    </EventDeclarations>
  </Events>
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSLALExe$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLALScr$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLALApps$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLALAlerts$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLALInv$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLALAl$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLALData$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLALRes$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>