<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2017-11-01T22:18:05.3320241Z">
  <Imports>
    <Import file="ReservedEventsTeam4Offline.xml" forceLocal="true" />
  </Imports>
  
  <Events>
	  <EventDeclarations storeType="CentralBond">
			<Declaration eventName="AsmSec4Data" account="AzSecurityStore"/>
			<Declaration eventName="AsmSec4Diag" account="AzSecurityStore"/>
			<Declaration eventName="AsmSec4Alert" account="AzSecurityStore"/>
			<Declaration eventName="AsmSec4Inv" account="AzSecurityStore"/>
			<Declaration eventName="AsmSec4AdtInv" account="AuditStore"/>
			<Declaration eventName="AsmSec4AdtData" account="AuditStore"/>
	  </EventDeclarations>
   
    <FileMonitors>
      <FileWatchItem eventName="AsmSec4FM" account="AzSecurityStore" storeType="CentralBond" container="azsecasmfmevent" />
    </FileMonitors>

  </Events>
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSec4Data$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSec4Inv$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>