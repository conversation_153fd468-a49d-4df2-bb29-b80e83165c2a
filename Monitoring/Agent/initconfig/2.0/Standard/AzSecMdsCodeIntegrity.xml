<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <!--
    All Code Integrity event logs are collected.
    
    All MDS tables declared in this file use one of the following MDS account aliases that must be declared in the service's main MDS configuration.
    This enables uploading different classes of data to different storage accounts, but is not required. The aliases provide a logical storage 
    account mapping that can all write to a single account or to separate accounts depending on how the services chooses to secure its data.
    The aliases here are:
        AuditStore - associated with tables that may include events with PII
        AzSecurityStore - associated with all other security monitoring tables that do not contain PII
        
    Revision 11/14/2017 Payalr - updated as per unified schema for AzSecPack Auto update
    -->
  <Imports>
    <Import file="AzSecMdsCodeIntegrityOffline.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <!-- Events that are already known to the team -->
      <Declaration eventName="AsmSLCI" account="AzSecurityStore" />
      <Declaration eventName="AsmSLCIAl" account="AzSecurityStore" />
      <!-- Create a few more entries for reserved columns-->
      <Declaration eventName="AsmSLCIInv" account="AzSecurityStore" />
      <Declaration eventName="AsmSLCIData" account="AzSecurityStore" />
      <Declaration eventName="AsmSLCIRes" account="AzSecurityStore" />
    </EventDeclarations>
  </Events>
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSLCI$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLCIInv$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLCIAl$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLCIData$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLCIRes$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>