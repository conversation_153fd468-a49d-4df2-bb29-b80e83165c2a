<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=********-nia1709261429 -->
  <Imports>
    <Import file="ProcessInvestigatorEventsOfflineHE.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <!-- Events that are already known to the team -->
      <Declaration eventName="AsmPiDiag" account="MdsCHostShared" />
      <Declaration eventName="AsmPiAlert" account="MdsCHostShared" />
      <!-- For future use in PI -->
      <Declaration eventName="AsmPiInv" account="MdsCHostShared" />
      <Declaration eventName="AsmPiData" account="MdsCHostShared" />
      <!-- For future use in CIS -->
      <Declaration eventName="AsmCisDiag" account="MdsCHostShared" />
      <Declaration eventName="AsmCisAl" account="MdsCHostShared" />
      <Declaration eventName="AsmCisInv" account="MdsCHostShared" />
      <Declaration eventName="AsmCisData" account="MdsCHostShared" />
    </EventDeclarations>
  </Events>
</MonitoringManagement>