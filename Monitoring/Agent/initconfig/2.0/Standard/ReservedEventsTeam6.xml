<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2017-11-01T22:18:05.3320241Z">
  <Imports>
    <Import file="ReservedEventsTeam5Offline.xml" forceLocal="true" />
  </Imports>
  
  <Events>
	  <EventDeclarations storeType="CentralBond">
			<Declaration eventName="AsmSec6Data" account="AzSecurityStore"/>
			<Declaration eventName="AsmSec6Diag" account="AzSecurityStore"/>
			<Declaration eventName="AsmSec6Alert" account="AzSecurityStore"/>
			<Declaration eventName="AsmSec6Inv" account="AzSecurityStore"/>
			<Declaration eventName="AsmSec6AdtInv" account="AuditStore"/>
			<Declaration eventName="AsmSec6AdtData" account="AuditStore"/>
	  </EventDeclarations>
   
    <FileMonitors>
      <FileWatchItem eventName="AsmSec6FM" account="AzSecurityStore" storeType="CentralBond" container="azsecasmfmevent" />
    </FileMonitors>

  </Events>
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSec6Data$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSec6Inv$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>