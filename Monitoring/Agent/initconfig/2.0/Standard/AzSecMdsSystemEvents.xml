<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Imports>
    <Import file="AzSecMdsSystemEventsOffline.xml" forceLocal="true" />
  </Imports>
  <!--
    Standard security events collected for various reporting and alerting purposes including:
    - security monitoring
    - evidence for compliance reporting
    - vulnerability analysis
    
    All MDS tables declared in this file use one of the following MDS account aliases that must be declared in the service's main MDS configuration.
    This enables uploading different classes of data to different storage accounts, but is not required. The aliases provide a logical storage 
    account mapping that can all write to a single account or to separate accounts depending on how the services chooses to secure its data.
    The aliases here are:
        AuditStore - associated with tables that may include events with PII
        AzSecurityStore - associated with all other security monitoring tables that do not contain PII
    -->
  <Events>
    <EventDeclarations storeType="CentralBond">
      <!-- Events that are already known to the team -->
      <Declaration eventName="AsmSysChg" account="AzSecurityStore" />
      <Declaration eventName="AsmSysADAccess" account="AzSecurityStore" />
      <Declaration eventName="AsmSysAuth" account="AzSecurityStore" />
      <Declaration eventName="AsmSysNet" account="AzSecurityStore" />
      <Declaration eventName="AsmSysLogon" account="AzSecurityStore" />
      <Declaration eventName="AsmSysWHEA2" account="AzSecurityStore" />
      <Declaration eventName="AsmSysCmd" account="AzSecurityStore" />
      <Declaration eventName="AsmSysSql" account="AzSecurityStore" />
      <Declaration eventName="AsmSysSecurity" account="AzSecurityStore" />
      <!-- Create a few more entries for reserved columns-->
      <Declaration eventName="AsmSysInv1" account="AzSecurityStore" />
      <Declaration eventName="AsmSysInv2" account="AzSecurityStore" />
      <Declaration eventName="AsmSysInv3" account="AzSecurityStore" />
      <Declaration eventName="AsmSysInv4" account="AzSecurityStore" />
      <Declaration eventName="AsmSysInv5" account="AzSecurityStore" />
    </EventDeclarations>
    <FileMonitors>
      <FileWatchItem eventName="AsmSysFMEvent" account="AzSecurityStore" storeType="CentralBond" container="azsecsysevtfm" />
    </FileMonitors>
  </Events>
  <!--Enable Syssecurity events upload to Cosmos from eventhub -->
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSysChg$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysADAccess$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysAuth$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysNet$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysLogon$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysWHEA2$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysCmd$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysInv1$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysInv2$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysInv3$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysInv4$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysInv5$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>