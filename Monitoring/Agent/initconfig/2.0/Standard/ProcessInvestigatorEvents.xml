<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=********-nia1709261429 -->
  <Imports>
    <Import file="ProcessInvestigatorEventsOffline.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <!-- Events that are already known to the team -->
      <Declaration eventName="AsmPiDiag" account="AuditStore" />
      <Declaration eventName="AsmPiAlert" account="AuditStore" />
      <!-- For future use in PI -->
      <Declaration eventName="AsmPiInv" account="AuditStore" />
      <Declaration eventName="AsmPiData" account="AuditStore" />
      <!-- For future use in CIS -->
      <Declaration eventName="AsmCisDiag" account="AuditStore" />
      <Declaration eventName="AsmCisAl" account="AuditStore" />
      <Declaration eventName="AsmCisInv" account="AuditStore" />
      <Declaration eventName="AsmCisData" account="AuditStore" />
    </EventDeclarations>
  </Events>
</MonitoringManagement>