<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <!--
    Standard security events collected for various reporting and alerting purposes including:
    - security monitoring
    - evidence for compliance reporting
    - vulnerability analysis
    
    All MDS tables declared in this file use one of the following MDS account aliases that must be declared in the service's main MDS configuration.
    This enables uploading different classes of data to different storage accounts, but is not required. The aliases provide a logical storage 
    account mapping that can all write to a single account or to separate accounts depending on how the services chooses to secure its data.
    The aliases here are:
        AuditStore - associated with tables that may include events with PII
        AzSecurityStore - associated with all other security monitoring tables that do not contain PII
    Decided to only use AuditStore for events written to AsmSysSecurity table to avoid edge case confusion.
    -->
  <Events>
    <WindowsEventLogSubscriptions>
      <!-- 
            Collects operational activity from system security channel excluding audit and antimalware related events; those are subscribed in
            the corresponding feature import file. HOBO services may require special handling if they support customer environments.
            Events here use the AuditStore account since some may contain PII and may access HOBO services.  HOBO services must contact AzSecurity
            during onboarding to identify potentially sensitive events for special handling.  Sensitive events, either from HOBO services, or due to
            event data sensitivity (e.g. 4688) may require special handling or be routed to protected tables.
            4/27/2016 - Removed Event 4688 from this list and moved to special table, since we will now collect command line data.
            2/01/2017 - Removed Event 5147 from this list
            -->
      <Subscription eventName="AsmSysSecurityLocal" query="Security!*[System[Provider[@Name='Microsoft-Windows-Security-Auditing'] and (EventID!=4688)]]" storeType="Local">
        <Column name="EventProvider">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/Event/EventData/Data | /Event/UserData/*/* | /Event/EventData | /Event/UserData/*</Value>
        </Column>
      </Subscription>
      <Subscription eventName="AsmSysCmdLocal" query="Security!*[System[(EventID=4688)]]" storeType="Local">
        <Column name="EventProvider">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/Event/EventData/Data | /Event/UserData/*/* | /Event/EventData | /Event/UserData/*</Value>
        </Column>
      </Subscription>
      <!--Add Task Scheduler Events 106, 129, 141 & 200.  4/7/2016 
              Enable monitoring use cases for registering (106), creating (129), starting (200) of scheduled tasks that may indicate persistence.
              142 – Task_Disabled added for 4.2
              -->
      <Subscription eventName="AsmSysSecurityLocal" query="Microsoft-Windows-TaskScheduler/Operational!*[System[(EventID=106) or (EventID=129) or (EventID=140) or (EventID=141) or (EventID=142) or (EventID=200)]]" storeType="Local">
        <Column name="EventProvider">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/Event/EventData/Data | /Event/EventData</Value>
        </Column>
      </Subscription>
      <!--Add Service Installation Start Event 7045, 1074 (Shutdown/Restart)  4/7/2016
              This enables security monitoring to determine when potential unexpected services were added.
              This event is both in the Application and System channel, so pulling from System.  JM  
              -->
      <Subscription eventName="AsmSysSecurityLocal" query="System!*[System[(EventID=1074) or (EventID=7045)]]" storeType="Local">
        <Column name="EventProvider">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/Event/EventData/Data | /Event/EventData</Value>
        </Column>
      </Subscription>
      <!--Add PowerShell Events 4103 (execute remote command) & 40962 (Console ready). 4/7/2016
              This is to enable security monitoring to look for unexpected activities.
              Did not inlcude Event 800, to avoid duplication in certain legacy environments.
              Added ThreadID.  It was missed in the earlier version and exists in the event log. 4/18/2016
              Added Events 4104-4106 for SCUBA detections. 4/27/2016
              -->
      <Subscription eventName="AsmSysCmdLocal" query="Microsoft-Windows-PowerShell/Operational!*[System[(EventID=4103) or (EventID=4104) or(EventID=4105) or (EventID=4106) or (EventID=40962)]]" storeType="Local">
        <Column name="EventProvider">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/Event/EventData/Data/@Name | /Event/EventData</Value>
        </Column>
      </Subscription>
      <!--Add RDP and TS Events from Azure. Captures Events 21 (logon), 22 (shell start), 23 (Logoff), 24 (disconnected), 25 (reconnected). 4/11/2016 
          Changed Account to AuditStore, since these events do not require special handling. 4/18/2016
          -->
      <Subscription eventName="AsmSysRDPLocal" query="Microsoft-Windows-TerminalServices-LocalSessionManager/Operational!*[System[(EventID=21) or (EventID=22) or (EventID=23) or (EventID=24) or (EventID=25)]]" storeType="Local">
        <Column name="EventProvider">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/Event/UserData/EventXML | /Event/UserData</Value>
        </Column>
      </Subscription>
      <!--Add Resource Exhaustion Event from Azure. Captures Event 2004 only, which represents Resource Exhaustion & affects other detections. 4/11/2016 
              This event is required by Anti-Malware and other detections to identify false positives on same machine.
            -->
      <Subscription eventName="AsmSysSecurityLocal" query="Microsoft-Windows-Resource-Exhaustion-Detector/Operational!*[System[(EventID=2004)]]" storeType="Local">
        <Column name="EventProvider">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/Event/UserData/EventXML | /Event/UserData</Value>
        </Column>
      </Subscription>
      <!--Add Kernel-WHEA Events, primarily for Bare Metal servers.  Modeled after HE event config. 4/18/2016 
                Changed Account to AzSecurityStore, since these events do not require special handling. 4/18/2016
            -->
      <Subscription eventName="AsmWHEAEventsLocal" query="Microsoft-Windows-Kernel-WHEA/Operational!*[System[(EventID=17) or (EventID=19) or (EventID=23)]]" storeType="Local">
        <Column name="EventProvider">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/EventData/Data/@Name | /EventData/Data</Value>
        </Column>
      </Subscription>
      <!--Added LsaSRV event ID 5000 (security package Schannel generated an exception) and
          User32 event 1074 (process wininit.exe has initiated the restart of computer) per IcM 7078593 to monitor for LSA crashes. 
            -->
      <Subscription eventName="AsmSysSecurityLocal" query="System!*[System[Provider[@Name='LsaSrv'] and (EventID=5000)]]" storeType="Local">
        <Column name="EventProvider">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/Event/EventData/Data | /Event/UserData/*/* | /Event/EventData | /Event/UserData/*</Value>
        </Column>
      </Subscription>
      <!-- Collect User Mode driver framework event 2003 which will be used for identifying USB key insertion
      -->
      <Subscription eventName="AsmSysSecurityLocal" query="Microsoft-Windows-DriverFrameworks-UserMode/Operational!*[System[(EventID=2003)]]" storeType="Local">
        <Column name="EventProvider">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/Event/UserData</Value>
        </Column>
      </Subscription>
      <Subscription eventName="AsmSysSecurityLocal" query="System!*[System[Provider[@Name='User32'] and (EventID=1074)]]" storeType="Local">
        <Column name="EventProvider">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/Event/EventData/Data | /Event/UserData/*/* | /Event/EventData | /Event/UserData/*</Value>
        </Column>
      </Subscription>
      <Subscription eventName="AsmSysSecurityLocal" query="Application!*[System[Provider[@Name='Microsoft-Windows-Wininit'] and (EventID=1015)]]" storeType="Local">
        <Column name="EventProvider">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/Event/EventData/Data | /Event/UserData/*/* | /Event/EventData | /Event/UserData/*</Value>
        </Column>
      </Subscription>
    </WindowsEventLogSubscriptions>
    <!--Derived Events to tag the Security Events with Azure Identity-->
    <DerivedEvents>
      <DerivedEvent source="AsmSysSecurityLocal" eventName="AsmSysChg" storeType="CentralBond" priority="Low" duration="PT5M" retryTimeout="PT10080M" account="AzSecurityStore" retentionInDays="30">
        <Query><![CDATA[
		    where (EventType="1100" || EventType="1102" || EventType="1104" || EventType="2003" || EventType="4608" || EventType="4609" || EventType="4610" || EventType="4611" || EventType="4612" || EventType="4614" || EventType="4615" || EventType="4616" || EventType="4618" || EventType="4621" || EventType="4622" || EventType="4658" || EventType="4674" || EventType="4691" || EventType="4697" || EventType="4698" || EventType="4699" || EventType="4700" || EventType="4701" || EventType="4702" || EventType="4713" || EventType="4714" || EventType="4715" || EventType="4719" || EventType="4817" || EventType="4819" || EventType="4826" || EventType="4864" || EventType="4865" || EventType="4866" || EventType="4867" || EventType="4902" || EventType="4904" || EventType="4905" || EventType="4906" || EventType="4907" || EventType="4908" || EventType="4909" || EventType="4910" || EventType="4912" || EventType="5038" || EventType="5056" || EventType="5057" || EventType="5059" || EventType="5060" || EventType="5062" || EventType="5063" || EventType="5064" || EventType="5065" || EventType="5066" || EventType="5067" || EventType="5068" || EventType="5069" || EventType="5070" || EventType="5140" || EventType="5142" || EventType="5143" || EventType="5144" || EventType="5145" || EventType="5148" || EventType="5149" || EventType="5150" || EventType="5151" || EventType="5154" || EventType="5155" || EventType="5156" || EventType="5158" || EventType="5159" || EventType="5168" || EventType="5447" || EventType="5888" || EventType="5889" || EventType="5890" || EventType="6144" || EventType="6145" || EventType="6410" || EventType="6416" || EventType="6419" || EventType="6420" || EventType="6421" || EventType="6422" || EventType="6423" || EventType="6424")
			let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")		  
            let AssetIdentity=GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
			let NodeIdentity=""
			let NodeType=""			
            select
				ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
				EventProvider, EventType, TimeCreated, EventPayload
            ]]></Query>
      </DerivedEvent>
      <DerivedEvent source="AsmSysSecurityLocal" eventName="AsmSysADAccess" storeType="CentralBond" priority="Low" duration="PT5M" retryTimeout="PT10080M" account="AzSecurityStore" retentionInDays="30">
        <Query><![CDATA[
			where (EventType="4662" || EventType="4663" || EventType="4706" || EventType="4707" || EventType="4716" || EventType="4739" || EventType="4776" || EventType="4777" || EventType="5136" || EventType="5137" || EventType="5138" || EventType="5139" || EventType="5141")
			let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")		  
            let AssetIdentity=GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
			let NodeIdentity=""
			let NodeType=""			
            select
				ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
				EventProvider, EventType, TimeCreated, EventPayload
            ]]></Query>
      </DerivedEvent>
      <DerivedEvent source="AsmSysSecurityLocal" eventName="AsmSysAuth" storeType="CentralBond" priority="Low" duration="PT5M" retryTimeout="PT10080M" account="AzSecurityStore" retentionInDays="30">
        <Query><![CDATA[
			where (EventType="4627" || EventType="4647" || EventType="4648" || EventType="4649" || EventType="4675" || EventType="4717" || EventType="4718" || EventType="4720" || EventType="4722" || EventType="4723" || EventType="4724" || EventType="4725" || EventType="4726" || EventType="4727" || EventType="4728" || EventType="4729" || EventType="4730" || EventType="4731" || EventType="4732" || EventType="4733" || EventType="4734" || EventType="4735" || EventType="4737" || EventType="4738" || EventType="4740" || EventType="4741" || EventType="4742" || EventType="4743" || EventType="4744" || EventType="4745" || EventType="4746" || EventType="4747" || EventType="4748" || EventType="4749" || EventType="4750" || EventType="4751" || EventType="4752" || EventType="4753" || EventType="4754" || EventType="4755" || EventType="4756" || EventType="4757" || EventType="4758" || EventType="4759" || EventType="4760" || EventType="4761" || EventType="4762" || EventType="4763" || EventType="4764" || EventType="4765" || EventType="4766" || EventType="4767" || EventType="4768" || EventType="4769" || EventType="4770" || EventType="4771" || EventType="4772" || EventType="4773" || EventType="4774" || EventType="4775" || EventType="4778" || EventType="4779" || EventType="4780" || EventType="4781" || EventType="4782" || EventType="4793" || EventType="4794" || EventType="4800" || EventType="4801" || EventType="4802" || EventType="4803" || EventType="4964" || EventType="5376" || EventType="5377" || EventType="5378" || EventType="5632" || EventType="5633")
			let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")		  
            let AssetIdentity=GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
			let NodeIdentity=""
			let NodeType=""			
            select
				ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
				EventProvider, EventType, TimeCreated, EventPayload
            ]]></Query>
      </DerivedEvent>
      <DerivedEvent source="AsmSysSecurityLocal" eventName="AsmSysNet" storeType="CentralBond" priority="Low" duration="PT5M" retryTimeout="PT10080M" account="AzSecurityStore" retentionInDays="30">
        <Query><![CDATA[
			where (EventType="4944" || EventType="4945" || EventType="4946" || EventType="4948" || EventType="4949" || EventType="4951" || EventType="4952" || EventType="4953" || EventType="4954" || EventType="4956" || EventType="4957" || EventType="4958" || EventType="5024" || EventType="5025" || EventType="5027" || EventType="5028" || EventType="5029" || EventType="5030" || EventType="5031" || EventType="5032" || EventType="5033" || EventType="5034" || EventType="5035" || EventType="5037" || EventType="6406" || EventType="6408")
			let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")		  
            let AssetIdentity=GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
			let NodeIdentity=""
			let NodeType=""			
            select
				ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
				EventProvider, EventType, TimeCreated, EventPayload
            ]]></Query>
      </DerivedEvent>
      <DerivedEvent source="AsmSysSecurityLocal" eventName="AsmSysLogon" storeType="CentralBond" priority="Low" duration="PT5M" retryTimeout="PT10080M" account="AzSecurityStore" retentionInDays="30">
        <Query><![CDATA[
			where (EventType="4624" || EventType="4625" || EventType="4634") 
			let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")		  
            let AssetIdentity=GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
			let NodeIdentity=""
			let NodeType=""			
            select
				ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
				EventProvider, EventType, TimeCreated, EventPayload
            ]]></Query>
      </DerivedEvent>
      <DerivedEvent source="AsmSysRDPLocal" eventName="AsmSysAuth" storeType="CentralBond" priority="Low" duration="PT5M" retryTimeout="PT10080M" account="AzSecurityStore" retentionInDays="30">
        <Query><![CDATA[
			let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")		  
            let AssetIdentity=GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
			let NodeIdentity=""
			let NodeType=""			
            select
				ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
				EventProvider, EventType, TimeCreated, EventPayload
            ]]></Query>
      </DerivedEvent>
      <DerivedEvent source="AsmWHEAEventsLocal" eventName="AsmSysWHEA2" storeType="CentralBond" priority="Low" duration="PT5M" retryTimeout="PT10080M" account="AzSecurityStore" retentionInDays="30">
        <Query><![CDATA[
			let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")
            let AssetIdentity=GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
			let NodeIdentity=""
			let NodeType=""			
            select
				ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
				EventProvider, EventType, TimeCreated, EventPayload
            ]]></Query>
      </DerivedEvent>
      <DerivedEvent source="AsmSysCmdLocal" eventName="AsmSysCmd" storeType="CentralBond" duration="PT5M" priority="Low" retryTimeout="PT10080M" account="AzSecurityStore" retentionInDays="30">
        <Query><![CDATA[
			let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")
            let AssetIdentity=GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
			let NodeIdentity=""
			let NodeType=""
            select
				ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
				EventProvider, EventType, TimeCreated, EventPayload
            ]]></Query>
      </DerivedEvent>
    </DerivedEvents>
  </Events>
</MonitoringManagement>