<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2017-11-01T22:18:05.3320241Z">
  <Imports>
    <Import file="ReservedEventsTeam5Offline.xml" forceLocal="true" />
  </Imports>
  
  <Events>
	  <EventDeclarations storeType="CentralBond">
			<Declaration eventName="AsmSec5Data" account="AzSecurityStore"/>
			<Declaration eventName="AsmSec5Diag" account="AzSecurityStore"/>
			<Declaration eventName="AsmSec5Alert" account="AzSecurityStore"/>
			<Declaration eventName="AsmSec5Inv" account="AzSecurityStore"/>
			<Declaration eventName="AsmSec5AdtInv" account="AuditStore"/>
			<Declaration eventName="AsmSec5AdtData" account="AuditStore"/>
	  </EventDeclarations>
   
    <FileMonitors>
      <FileWatchItem eventName="AsmSec5FM" account="AzSecurityStore" storeType="CentralBond" container="azsecasmfmevent" />
    </FileMonitors>

  </Events>
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSec5Data$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSec5Inv$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>