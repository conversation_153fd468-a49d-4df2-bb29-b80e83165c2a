<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Imports>
    <Import file="ReservedEventsTeam9OfflineHE.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <Declaration eventName="AsmSec9Data" account="MdsCHostShared" />
      <Declaration eventName="AsmSec9Diag" account="MdsCHostShared" />
      <Declaration eventName="AsmSec9Alert" account="MdsCHostShared" />
      <Declaration eventName="AsmSec9Inv" account="MdsCHostShared" />
      <Declaration eventName="AsmSec9AdtInv" account="MdsCHostShared" />
      <Declaration eventName="AsmSec9AdtData" account="MdsCHostShared" />
    </EventDeclarations>
    <FileMonitors>
      <FileWatchItem eventName="AsmSec9FM" account="MdsCHostShared" storeType="CentralBond" container="azsecasmfmevent" />
    </FileMonitors>
  </Events>
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSec9Data$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSec9Inv$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>