<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Imports>
    <Import file="AzSecMdsAsmScanOffline.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <!-- Events that are already known to the team -->
      <Declaration eventName="AsmSpDiag" account="AzSecurityStore" />
      <Declaration eventName="AsmSpAlert" account="AzSecurityStore" />
      <Declaration eventName="AsmSpCfgBase" account="AzSecurityStore" />
      <Declaration eventName="AsmSpPatch" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvPrdt" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvCfg" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvCert" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvKey" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvNet" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvRPC" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvDrv" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvSrvc" account="AzSecurityStore" />
      <Declaration eventName="AsmSpRegistry" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvSysLoc" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvUG" account="AzSecurityStore" />
      <Declaration eventName="AsmSpVer" account="AzSecurityStore" />
      <!-- Create a few more entries for reserved columns-->
      <Declaration eventName="AsmSpAlRes1" account="AzSecurityStore" />
      <Declaration eventName="AsmSpAlRes2" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvRes1" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvRes2" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvRes3" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvRes4" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvRes5" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvRes6" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvRes7" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvRes8" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvRes9" account="AzSecurityStore" />
      <Declaration eventName="AsmSpInvRes10" account="AzSecurityStore" />
      <Declaration eventName="AsmSpDataRes1" account="AzSecurityStore" />
      <Declaration eventName="AsmSpDataRes2" account="AzSecurityStore" />
      <Declaration eventName="AsmSpDataRes3" account="AzSecurityStore" />
      <Declaration eventName="AsmSpDataRes4" account="AzSecurityStore" />
      <Declaration eventName="AsmSpDataRes5" account="AzSecurityStore" />
      <Declaration eventName="AsmSpDataRes6" account="AzSecurityStore" />
    </EventDeclarations>
    <FileMonitors>
      <FileWatchItem eventName="AsmSpFMEvent" account="AzSecurityStore" storeType="CentralBond" container="azsecasmfmevent" />
    </FileMonitors>
  </Events>
  <!--Enable Cosmos data upload for Inventory Data specific tables.-->
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSpCfgBase$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpPatch$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvPrdt$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvCfg$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvCert$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvNet$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvRPC$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvDrv$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvSrvc$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpRegistry$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvSysLoc$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvUG$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpVer$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvRes1$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvRes2$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvRes3$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvRes4$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpInvRes5$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpDataRes1$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpDataRes2$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSpDataRes3$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>