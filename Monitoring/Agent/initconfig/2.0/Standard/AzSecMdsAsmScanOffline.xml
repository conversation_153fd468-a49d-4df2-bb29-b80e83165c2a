<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Events>
    <EtwProviders>
      <EtwProvider guid="9a65c11b-e330-4ecd-a666-3c3d2c320622" format="Manifest" storeType="Local" manifest="extensions\AzureSecurityPack\SecurityScanLoggerUnifiedManifest.man" duration="PT1M">
        <DefaultEvent eventName="AsmScannerDefaultEvents" />
        <!-- Diagnostics Logs -->
        <Event id="100" eventName="AsmDiagnostics" />
        <!-- LogScanEvent() -->
        <Event id="101" eventName="AsmScannerData" />
        <!-- LogInventoryEvent() -->
        <Event id="102" eventName="AsmInventoryData" />
        <!-- AlertData() -->
        <Event id="103" eventName="AsmAlertsData" />
        <!-- HeartBeatData() -->
        <Event id="120" eventName="AsmHeartbeatData" />
      </EtwProvider>
    </EtwProviders>
    <!-- 
      Diagnostic Tool File Monitor. 
      
      When the diagnostics tool is run it places all the diagnostic data
      under c:\DiagnosticsZipDir\*.zip, this file Monitor will upload 
      data to the corresponding storage account as soon as it detects
      any activity under this dir.
            -->
    <FileMonitors storeType="CentralBond">
      <FileWatchItem eventName="AsmSpFMEvent" account="AzSecurityStore" container="azsecasmfmevent" directoryQuotaInMB="100" lastChangeOffsetInSeconds="10" removeEmptyDirectories="false">
        <Directory><![CDATA[Concat("", GetStaticEnvironmentVariable("SystemDrive"), "\DiagnosticsZipDir")]]></Directory>
      </FileWatchItem>
    </FileMonitors>
    <DerivedEvents>
      <DerivedEvent source="AsmDiagnostics" duration="PT15M" eventName="AsmSpDiag" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                    where (EventType = "Error" || EventType = "Startup" || EventType = "Shutdown") and (EventProvider != "PILauncher" and EventProvider != "NetIsoScanner" and EventProvider != "OffNodeVulnScan")
                    select 
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
      <!-- 
            Alerting feed. 
            
            All scanners using LogAlertingEvent will have those records processed on a one
            minute cycles. This is expected to be low volume output from the scanners.
            -->
      <DerivedEvent source="AsmAlertsData" duration="PT1M" eventName="AsmSpAlert" account="AzSecurityStore" priority="Normal" retryTimeout="PT10080M" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
					where (EventProvider != "PILauncher" and EventProvider != "NetIsoScanner" and EventProvider != "OffNodeVulnScan")
					select 
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
      <!-- Reporting feeds -->
      <!-- Baseline settings -->
      <DerivedEvent source="AsmInventoryData" duration="PT15M" eventName="AsmSpCfgBase" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                    where EventProvider = "BaselineScanner"
					let OsVersion = UserField1
                    select 
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference,
						OsVersion
                    ]]></Query>
      </DerivedEvent>
      <!-- Installed products, features, patches, and OS version inventory -->
      <DerivedEvent source="AsmInventoryData" duration="PT15M" eventName="AsmSpPatch" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                    where (EventProvider = "SoftwareInventoryScanner") && (EventType = "Patch")
                    select
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
      <DerivedEvent source="AsmInventoryData" duration="PT15M" eventName="AsmSpInvPrdt" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                    where (EventProvider = "SoftwareInventoryScanner") && (EventType = "Product" || EventType = "Feature" || EventType = "Version" )
                    select
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
      <!-- NetworkShares inventory -->
      <DerivedEvent source="AsmInventoryData" duration="PT15M" eventName="AsmSpInvCfg" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                    where (EventProvider = "SoftwareInventoryScanner") && (EventType = "NetworkShare" || EventType = "NamedPipe" || EventType = "AutoRuns" || EventType = "NTPStatus")
                    select 
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
      <!-- Certificates inventory -->
      <DerivedEvent source="AsmInventoryData" duration="PT15M" eventName="AsmSpInvCert" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                    where (EventProvider = "SoftwareInventoryScanner") && (EventType = "Certificate")
                    select 
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
      <!-- Certificates Exported Public Key inventory -->
      <DerivedEvent source="AsmInventoryData" duration="PT15M" eventName="AsmSpInvKey" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                    where(EventProvider = "SoftwareInventoryScanner") && (EventType = "ExportedCertPubKeys")
                    select 
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
      <!-- HeavyTalker inventory -->
      <DerivedEvent source="AsmInventoryData" duration="PT15M" eventName="AsmSpInvNet" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                    where (EventProvider = "KernelScanner") && (EventType = "HeavyTalker")
                    select 
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
      <!-- RpcEndpoint inventory -->
      <DerivedEvent source="AsmInventoryData" duration="PT15M" eventName="AsmSpInvRPC" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                    where (EventProvider = "SoftwareInventoryScanner") && (EventType = "RpcEndpoint")
                    select 
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
      <!-- Drivers inventory -->
      <DerivedEvent source="AsmInventoryData" duration="PT15M" eventName="AsmSpInvDrv" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                    where (EventProvider = "SoftwareInventoryScanner") && (EventType = "Drivers")
                    select 
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
      <!-- Win32 services inventory -->
      <DerivedEvent source="AsmInventoryData" duration="PT15M" eventName="AsmSpInvSrvc" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                     where (EventProvider = "SoftwareInventoryScanner") && (EventType = "Services")
                    select 
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
      <!-- Registry inventory -->
      <DerivedEvent source="AsmInventoryData" duration="PT15M" eventName="AsmSpRegistry" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local">
        <Query><![CDATA[
                     where (EventProvider = "SoftwareInventoryScanner") && (EventType = "AsepRegistry" || EventType = "AntiVirusRegistry" || EventType = "WUSettingRegistry" || EventType = "AntiMalwareRegistry" || EventType = "MSRCRegistry" || EventType = "DSMSRegistry")
                    select 
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
      <!-- System lockdown attributes -->
      <DerivedEvent source="AsmInventoryData" duration="PT15M" eventName="AsmSpInvSysLoc" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                     where (EventProvider = "SoftwareInventoryScanner") && (EventType = "ConfigCIEnabled" || EventType = "CiAlEtwEnabled" || EventType = "BitlockerStatus" || EventType = "ApplockerRegistry" || EventType = "HardwareRegistry" || EventType = "SecureBootRegistry" || EventType = "TPMRegistry" || EventType = "Version")
                    select 
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
      <!-- Local user inventory -->
      <DerivedEvent source="AsmInventoryData" duration="PT15M" eventName="AsmSpInvUG" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                    where (EventProvider = "UserGroupScanner") && (EventType = "UsersInventory" || EventType = "GroupsInventory")
                    select 
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
      <!-- Docker Container inventory -->
      <DerivedEvent source="AsmInventoryData" duration="PT15M" eventName="AsmSpInvRes1" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                    where (EventProvider = "SoftwareInventoryScanner") && (EventType = "DockerVersion" || EventType = "DockerImages" || EventType = "DockerContainers" || EventType = "DockerVolumes")
                    select 
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
      <!-- SQL VA inventory -->
      <DerivedEvent source="AsmInventoryData" duration="PT15M" eventName="AsmSpInvRes2" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                    where EventProvider = "SqlVaScanner"
                    select 
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
      <!-- 
            Heartbeat feed. 
            -->
      <DerivedEvent source="AsmHeartbeatData" duration="PT15M" eventName="AsmSpVer" account="AzSecurityStore" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                    select 
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
                        EventProvider, EventType, EventPayload, Truncated, TotalChunks, ChunkId, ChunkReference
                    ]]></Query>
      </DerivedEvent>
    </DerivedEvents>
    <Extensions>
      <Extension extensionName="AzureSecurityPack">
        <CommandLine>SecurityScanMgr.exe</CommandLine>
        <!-- <AlternativeExtensionLocation></AlternativeExtensionLocation> -->
        <!-- <Body></Body> -->
        <ResourceUsage cpuPercentUsage="5" cpuThrottling="Hard" memoryLimitInMB="64" />
      </Extension>
    </Extensions>
  </Events>
</MonitoringManagement>