<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Imports>
    <Import file="ReservedEventsTeam5Offline.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <Declaration eventName="AsmSec6Data" account="MdsCHostShared" />
      <Declaration eventName="AsmSec6Diag" account="MdsCHostShared" />
      <Declaration eventName="AsmSec6Alert" account="MdsCHostShared" />
      <Declaration eventName="AsmSec6Inv" account="MdsCHostShared" />
      <Declaration eventName="AsmSec6AdtInv" account="MdsCHostShared" />
      <Declaration eventName="AsmSec6AdtData" account="MdsCHostShared" />
    </EventDeclarations>
    <FileMonitors>
      <FileWatchItem eventName="AsmSec6FM" account="MdsCHostShared" storeType="CentralBond" container="azsecasmfmevent" />
    </FileMonitors>
  </Events>
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSec6Data$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSec6Inv$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>