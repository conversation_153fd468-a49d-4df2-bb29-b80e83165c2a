<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Imports>
    <Import file="AzSecMdsVulnScanOffline.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <Declaration eventName="AsmVsaFMSnap" account="AzSecurityStore" />
      <Declaration eventName="AsmVsaDiag" account="AzSecurityStore" />
      <Declaration eventName="AsmVsaData" account="AzSecurityStore" />
      <Declaration eventName="AsmVsaAl" account="AzSecurityStore" />
      <!-- Inventory table is reserved -->
      <Declaration eventName="AsmVsaInv" account="AzSecurityStore" />
    </EventDeclarations>
    <FileMonitors storeType="CentralBond">
      <FileWatchItem eventName="AsmVsaFMSnap" container="shava-snapshots" account="AzSecurityStore" />
    </FileMonitors>
  </Events>
</MonitoringManagement>