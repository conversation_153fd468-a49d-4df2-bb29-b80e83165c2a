<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <!--
    All AppLocker event logs are collected.
    -->
  <Imports>
    <Import file="AzSecMdsAppLockerHEOffline.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <!-- Events that are already known to the team -->
      <Declaration eventName="AsmSLALExe" account="MdsCHostShared" />
      <Declaration eventName="AsmSLALScr" account="MdsCHostShared" />
      <Declaration eventName="AsmSLALApps" account="MdsCHostShared" />
      <Declaration eventName="AsmSLALAlerts" account="MdsCHostShared" />
      <Declaration eventName="AsmSLALData" account="MdsCHostShared" />
      <!-- Few more entries for reserved columns-->
      <Declaration eventName="AsmSLALDiag" account="MdsCHostShared" />
      <Declaration eventName="AsmSLALInv" account="MdsCHostShared" />
      <Declaration eventName="AsmSLALAl" account="MdsCHostShared" />
      <Declaration eventName="AsmSLALRes" account="MdsCHostShared" />
    </EventDeclarations>
  </Events>
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSLALExe$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLALScr$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLALApps$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLALAlerts$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLALInv$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLALAl$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLALData$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSLALRes$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>