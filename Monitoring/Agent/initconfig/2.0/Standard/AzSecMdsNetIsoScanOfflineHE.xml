<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Events>
    <FileMonitors storeType="CentralBond">
      <FileWatchItem eventName="AsmNetIsoFM" container="azsecnetisofm" account="MdsCHostShared" compressionType="gzip" directoryQuotaInMB="100" lastChangeOffsetInSeconds="10" retentionInDays="4" removeEmptyDirectories="true">
        <Directory><![CDATA[Concat("", GetEnvironmentVariable("LOCALAPPDATA"), "\SHANetIso")]]></Directory>
      </FileWatchItem>
    </FileMonitors>
    <DerivedEvents>
      <!-- network isolation scanner -->
      <DerivedEvent source="AsmInventoryData" duration="PT15M" eventName="AsmNetIsoInv" account="MdsCHostShared" priority="Normal" deadline="PT15M" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
             where EventProvider = "NetIsoScanner"
               let SchemaVersion = UserField1,
                   Session       = UserField2,
                   Count         = UserField3,
                   Item          = UserField4,
                   Metadata      = UserField5
            select ReportingIdentity,
                   AssetIdentity,
                   NodeIdentity,
                   NodeType,
                   EventProvider,
                   EventType,
                   EventPayload,
                   Truncated,
                   TotalChunks,
                   ChunkId,
                   ChunkReference,
                   SchemaVersion,
                   Session,
                   Count,
                   Item,
                   Metadata
            ]]></Query>
      </DerivedEvent>
    </DerivedEvents>
  </Events>
</MonitoringManagement>