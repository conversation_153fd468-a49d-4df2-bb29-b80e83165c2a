<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******* -->
  <Events>
    <CounterSets storeType="Local">
      <CounterSet eventName="AsmScannerCounter" sampleRateInSeconds="10">
        <Counter>\Process(UserGroupScanner)\% Processor Time</Counter>
        <Counter>\Process(UserGroupScanner)\IO Data Bytes/sec</Counter>
        <Counter>\Process(UserGroupScanner)\IO Data Operations/sec</Counter>
        <Counter>\Process(UserGroupScanner)\IO Read Bytes/sec</Counter>
        <Counter>\Process(UserGroupScanner)\IO Read Operations/sec</Counter>
        <Counter>\Process(UserGroupScanner)\IO Write Bytes/sec</Counter>
        <Counter>\Process(UserGroupScanner)\IO Write Operations/sec</Counter>
        <Counter>\Process(UserGroupScanner)\Pool Nonpaged Bytes</Counter>
        <Counter>\Process(UserGroupScanner)\Pool Paged Bytes</Counter>
        <Counter>\Process(UserGroupScanner)\Virtual Bytes</Counter>
        <Counter>\Process(UserGroupScanner)\Working Set</Counter>
        <Counter>\Process(UserGroupScanner)\Working Set Peak</Counter>
        <Counter>\Process(AsmBaselineScanner)\% Processor Time</Counter>
        <Counter>\Process(AsmBaselineScanner)\IO Data Bytes/sec</Counter>
        <Counter>\Process(AsmBaselineScanner)\IO Data Operations/sec</Counter>
        <Counter>\Process(AsmBaselineScanner)\IO Read Bytes/sec</Counter>
        <Counter>\Process(AsmBaselineScanner)\IO Read Operations/sec</Counter>
        <Counter>\Process(AsmBaselineScanner)\IO Write Bytes/sec</Counter>
        <Counter>\Process(AsmBaselineScanner)\IO Write Operations/sec</Counter>
        <Counter>\Process(AsmBaselineScanner)\Pool Nonpaged Bytes</Counter>
        <Counter>\Process(AsmBaselineScanner)\Pool Paged Bytes</Counter>
        <Counter>\Process(AsmBaselineScanner)\Virtual Bytes</Counter>
        <Counter>\Process(AsmBaselineScanner)\Working Set</Counter>
        <Counter>\Process(AsmBaselineScanner)\Working Set Peak</Counter>
        <Counter>\Process(SecurityScanMgr)\% Processor Time</Counter>
        <Counter>\Process(SecurityScanMgr)\IO Data Bytes/sec</Counter>
        <Counter>\Process(SecurityScanMgr)\IO Data Operations/sec</Counter>
        <Counter>\Process(SecurityScanMgr)\IO Read Bytes/sec</Counter>
        <Counter>\Process(SecurityScanMgr)\IO Read Operations/sec</Counter>
        <Counter>\Process(SecurityScanMgr)\IO Write Bytes/sec</Counter>
        <Counter>\Process(SecurityScanMgr)\IO Write Operations/sec</Counter>
        <Counter>\Process(SecurityScanMgr)\Pool Nonpaged Bytes</Counter>
        <Counter>\Process(SecurityScanMgr)\Pool Paged Bytes</Counter>
        <Counter>\Process(SecurityScanMgr)\Virtual Bytes</Counter>
        <Counter>\Process(SecurityScanMgr)\Working Set</Counter>
        <Counter>\Process(SecurityScanMgr)\Working Set Peak</Counter>
        <Counter>\Process(SWinventoryScanner)\% Processor Time</Counter>
        <Counter>\Process(SWinventoryScanner)\IO Data Bytes/sec</Counter>
        <Counter>\Process(SWinventoryScanner)\IO Data Operations/sec</Counter>
        <Counter>\Process(SWinventoryScanner)\IO Read Bytes/sec</Counter>
        <Counter>\Process(SWinventoryScanner)\IO Read Operations/sec</Counter>
        <Counter>\Process(SWinventoryScanner)\IO Write Bytes/sec</Counter>
        <Counter>\Process(SWinventoryScanner)\IO Write Operations/sec</Counter>
        <Counter>\Process(SWinventoryScanner)\Pool Nonpaged Bytes</Counter>
        <Counter>\Process(SWinventoryScanner)\Pool Paged Bytes</Counter>
        <Counter>\Process(SWinventoryScanner)\Virtual Bytes</Counter>
        <Counter>\Process(SWinventoryScanner)\Working Set</Counter>
        <Counter>\Process(SWinventoryScanner)\Working Set Peak</Counter>
        <Counter>\Process(EventFilterScanner)\% Processor Time</Counter>
        <Counter>\Process(EventFilterScanner)\IO Data Bytes/sec</Counter>
        <Counter>\Process(EventFilterScanner)\IO Data Operations/sec</Counter>
        <Counter>\Process(EventFilterScanner)\IO Read Bytes/sec</Counter>
        <Counter>\Process(EventFilterScanner)\IO Read Operations/sec</Counter>
        <Counter>\Process(EventFilterScanner)\IO Write Bytes/sec</Counter>
        <Counter>\Process(EventFilterScanner)\IO Write Operations/sec</Counter>
        <Counter>\Process(EventFilterScanner)\Pool Nonpaged Bytes</Counter>
        <Counter>\Process(EventFilterScanner)\Pool Paged Bytes</Counter>
        <Counter>\Process(EventFilterScanner)\Virtual Bytes</Counter>
        <Counter>\Process(EventFilterScanner)\Working Set</Counter>
        <Counter>\Process(EventFilterScanner)\Working Set Peak</Counter>
        <Counter>\Process(KernelInventoryScanner)\% Processor Time</Counter>
        <Counter>\Process(KernelInventoryScanner)\IO Data Bytes/sec</Counter>
        <Counter>\Process(KernelInventoryScanner)\IO Data Operations/sec</Counter>
        <Counter>\Process(KernelInventoryScanner)\IO Read Bytes/sec</Counter>
        <Counter>\Process(KernelInventoryScanner)\IO Read Operations/sec</Counter>
        <Counter>\Process(KernelInventoryScanner)\IO Write Bytes/sec</Counter>
        <Counter>\Process(KernelInventoryScanner)\IO Write Operations/sec</Counter>
        <Counter>\Process(KernelInventoryScanner)\Pool Nonpaged Bytes</Counter>
        <Counter>\Process(KernelInventoryScanner)\Pool Paged Bytes</Counter>
        <Counter>\Process(KernelInventoryScanner)\Virtual Bytes</Counter>
        <Counter>\Process(KernelInventoryScanner)\Working Set</Counter>
        <Counter>\Process(KernelInventoryScanner)\Working Set Peak</Counter>
        <Counter>\Process(NetIsoScanner)\% Processor Time</Counter>
        <Counter>\Process(NetIsoScanner)\IO Data Bytes/sec</Counter>
        <Counter>\Process(NetIsoScanner)\IO Data Operations/sec</Counter>
        <Counter>\Process(NetIsoScanner)\IO Read Bytes/sec</Counter>
        <Counter>\Process(NetIsoScanner)\IO Read Operations/sec</Counter>
        <Counter>\Process(NetIsoScanner)\IO Write Bytes/sec</Counter>
        <Counter>\Process(NetIsoScanner)\IO Write Operations/sec</Counter>
        <Counter>\Process(NetIsoScanner)\Pool Nonpaged Bytes</Counter>
        <Counter>\Process(NetIsoScanner)\Pool Paged Bytes</Counter>
        <Counter>\Process(NetIsoScanner)\Virtual Bytes</Counter>
        <Counter>\Process(NetIsoScanner)\Working Set</Counter>
        <Counter>\Process(NetIsoScanner)\Working Set Peak</Counter>
        <Counter>\Process(MsSenseS)\% Processor Time</Counter>
        <Counter>\Process(MsSenseS)\IO Data Bytes/sec</Counter>
        <Counter>\Process(MsSenseS)\IO Data Operations/sec</Counter>
        <Counter>\Process(MsSenseS)\IO Read Bytes/sec</Counter>
        <Counter>\Process(MsSenseS)\IO Read Operations/sec</Counter>
        <Counter>\Process(MsSenseS)\IO Write Bytes/sec</Counter>
        <Counter>\Process(MsSenseS)\IO Write Operations/sec</Counter>
        <Counter>\Process(MsSenseS)\Pool Nonpaged Bytes</Counter>
        <Counter>\Process(MsSenseS)\Pool Paged Bytes</Counter>
        <Counter>\Process(MsSenseS)\Virtual Bytes</Counter>
        <Counter>\Process(MsSenseS)\Working Set</Counter>
        <Counter>\Process(MsSenseS)\Working Set Peak</Counter>
        <Counter>\Process(PILauncher)\% Processor Time</Counter>
        <Counter>\Process(PILauncher)\IO Data Bytes/sec</Counter>
        <Counter>\Process(PILauncher)\IO Data Operations/sec</Counter>
        <Counter>\Process(PILauncher)\IO Read Bytes/sec</Counter>
        <Counter>\Process(PILauncher)\IO Read Operations/sec</Counter>
        <Counter>\Process(PILauncher)\IO Write Bytes/sec</Counter>
        <Counter>\Process(PILauncher)\IO Write Operations/sec</Counter>
        <Counter>\Process(PILauncher)\Pool Nonpaged Bytes</Counter>
        <Counter>\Process(PILauncher)\Pool Paged Bytes</Counter>
        <Counter>\Process(PILauncher)\Virtual Bytes</Counter>
        <Counter>\Process(PILauncher)\Working Set</Counter>
        <Counter>\Process(PILauncher)\Working Set Peak</Counter>
        <Counter>\Process(ShavaVulnScan)\% Processor Time</Counter>
        <Counter>\Process(ShavaVulnScan)\IO Data Bytes/sec</Counter>
        <Counter>\Process(ShavaVulnScan)\IO Data Operations/sec</Counter>
        <Counter>\Process(ShavaVulnScan)\IO Read Bytes/sec</Counter>
        <Counter>\Process(ShavaVulnScan)\IO Read Operations/sec</Counter>
        <Counter>\Process(ShavaVulnScan)\IO Write Bytes/sec</Counter>
        <Counter>\Process(ShavaVulnScan)\IO Write Operations/sec</Counter>
        <Counter>\Process(ShavaVulnScan)\Pool Nonpaged Bytes</Counter>
        <Counter>\Process(ShavaVulnScan)\Pool Paged Bytes</Counter>
        <Counter>\Process(ShavaVulnScan)\Virtual Bytes</Counter>
        <Counter>\Process(ShavaVulnScan)\Working Set</Counter>
        <Counter>\Process(ShavaVulnScan)\Working Set Peak</Counter>
        <Counter>\Process(AzQualysScanCommand)\% Processor Time</Counter>
        <Counter>\Process(AzQualysScanCommand)\IO Data Bytes/sec</Counter>
        <Counter>\Process(AzQualysScanCommand)\IO Data Operations/sec</Counter>
        <Counter>\Process(AzQualysScanCommand)\IO Read Bytes/sec</Counter>
        <Counter>\Process(AzQualysScanCommand)\IO Read Operations/sec</Counter>
        <Counter>\Process(AzQualysScanCommand)\IO Write Bytes/sec</Counter>
        <Counter>\Process(AzQualysScanCommand)\IO Write Operations/sec</Counter>
        <Counter>\Process(AzQualysScanCommand)\Pool Nonpaged Bytes</Counter>
        <Counter>\Process(AzQualysScanCommand)\Pool Paged Bytes</Counter>
        <Counter>\Process(AzQualysScanCommand)\Virtual Bytes</Counter>
        <Counter>\Process(AzQualysScanCommand)\Working Set</Counter>
        <Counter>\Process(AzQualysScanCommand)\Working Set Peak</Counter>
      </CounterSet>
    </CounterSets>
    <DerivedEvents>
      <DerivedEvent source="AsmScannerCounter" duration="PT15M" eventName="AsmSecPackCntr" account="MdsCHostShared" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                  groupby Identity(), CounterName
                  let SampleCount=Count()
                  let CounterValue=Average(CounterValue)
                  let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")
                  let AzureIdentity=GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
              ]]></Query>
      </DerivedEvent>
    </DerivedEvents>
  </Events>
</MonitoringManagement>