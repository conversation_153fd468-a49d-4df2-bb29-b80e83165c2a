<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Imports>
    <Import file="AzSecMdsWDATPOfflineHE.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <!-- Events that are already known to the team -->
      <Declaration eventName="AsmMsSense" account="MdsCHostShared" />
      <Declaration eventName="AsmMsSDiag" account="MdsCHostShared" />
      <!-- Create a few more entries for reserved columns-->
      <Declaration eventName="AsmMsSInv" account="MdsCHostShared" />
      <Declaration eventName="AsmMsSAL" account="MdsCHostShared" />
    </EventDeclarations>
  </Events>
</MonitoringManagement>