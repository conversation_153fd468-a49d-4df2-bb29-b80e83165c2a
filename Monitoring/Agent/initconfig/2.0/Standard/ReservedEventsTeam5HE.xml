<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Imports>
    <Import file="ReservedEventsTeam5OfflineHE.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <Declaration eventName="AsmSec5Data" account="MdsCHostShared" />
      <Declaration eventName="AsmSec5Diag" account="MdsCHostShared" />
      <Declaration eventName="AsmSec5Alert" account="MdsCHostShared" />
      <Declaration eventName="AsmSec5Inv" account="MdsCHostShared" />
      <Declaration eventName="AsmSec5AdtInv" account="MdsCHostShared" />
      <Declaration eventName="AsmSec5AdtData" account="MdsCHostShared" />
    </EventDeclarations>
    <FileMonitors>
      <FileWatchItem eventName="AsmSec5FM" account="MdsCHostShared" storeType="CentralBond" container="azsecasmfmevent" />
    </FileMonitors>
  </Events>
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSec5Data$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSec5Inv$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>