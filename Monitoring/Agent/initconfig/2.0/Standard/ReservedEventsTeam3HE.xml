<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Imports>
    <Import file="ReservedEventsTeam3OfflineHE.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <Declaration eventName="AsmSec3Data" account="MdsCHostShared" />
      <Declaration eventName="AsmSec3Diag" account="MdsCHostShared" />
      <Declaration eventName="AsmSec3Alert" account="MdsCHostShared" />
      <Declaration eventName="AsmSec3Inv" account="MdsCHostShared" />
      <Declaration eventName="AsmSec3AdtInv" account="MdsCHostShared" />
      <Declaration eventName="AsmSec3AdtData" account="MdsCHostShared" />
    </EventDeclarations>
    <FileMonitors>
      <FileWatchItem eventName="AsmSec3FM" account="MdsCHostShared" storeType="CentralBond" container="azsecasmfmevent" />
    </FileMonitors>
  </Events>
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSec3Data$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSec3Inv$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>