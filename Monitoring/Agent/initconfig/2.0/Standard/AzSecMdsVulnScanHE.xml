<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Imports>
    <Import file="AzSecMdsVulnScanOfflineHE.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <Declaration eventName="AsmVsaFMSnap" account="MdsCHostShared" />
      <Declaration eventName="AsmVsaDiag" account="MdsCHostShared" />
      <Declaration eventName="AsmVsaData" account="MdsCHostShared" />
      <Declaration eventName="AsmVsaAl" account="MdsCHostShared" />
      <!-- Inventory table is reserved -->
      <Declaration eventName="AsmVsaInv" account="MdsCHostShared" />
    </EventDeclarations>
    <FileMonitors storeType="CentralBond">
      <FileWatchItem eventName="AsmVsaFMSnap" container="shava-snapshots" account="MdsCHostShared" />
    </FileMonitors>
  </Events>
</MonitoringManagement>