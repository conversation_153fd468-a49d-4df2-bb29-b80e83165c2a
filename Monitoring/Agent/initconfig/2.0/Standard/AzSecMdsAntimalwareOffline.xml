<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Events>
    <!-- 
              Diagnostic Tool File Monitor. 
            
              When the doagnostic tool is run it places all the MEPSupport cab files
              under %Program Data%\Microsoft\Microsoft Antimalware\Support\*.cab, this file Monitor will upload 
                                                  data to the corresponding storage account as soon as it detects
                                                  any activity under this dir.
              -->
    <FileMonitors storeType="CentralBond">
      <FileWatchItem eventName="AsmSysAvFMEvent" filter=".*\.cab" container="azsecsysavfm" contextParam="MalwareEngineLogs" directoryQuotaInMB="100" lastChangeOffsetInSeconds="60" uploadDelayInSeconds="60" removeEmptyDirectories="false" retentionInDays="30" account="AzSecurityStore">
        <Directory><![CDATA[Concat("", GetEnvironmentVariable("ProgramData"), "\Microsoft\Microsoft Antimalware\Support")]]></Directory>
      </FileWatchItem>
      <FileWatchItem eventName="AsmSysAvFMEvent" container="azsecsysavfm" compressionType="gzip" contextParam="QuarantineFiles" directoryQuotaInMB="100" lastChangeOffsetInSeconds="60" uploadDelayInSeconds="60" removeEmptyDirectories="false" retentionInDays="30" account="AzSecurityStore">
        <Directory><![CDATA[Concat("", GetEnvironmentVariable("ProgramData"), "\Microsoft\Microsoft Antimalware\Quarantine")]]></Directory>
      </FileWatchItem>
      <FileWatchItem eventName="AsmSysAvFMEvent" filter=".*\.cab" container="azsecsysavfm" contextParam="MalwareEngineLogs" directoryQuotaInMB="100" lastChangeOffsetInSeconds="60" uploadDelayInSeconds="60" removeEmptyDirectories="false" retentionInDays="30" account="AzSecurityStore">
        <Directory><![CDATA[Concat("", GetEnvironmentVariable("ProgramData"), "\Microsoft\Windows Defender\Support")]]></Directory>
      </FileWatchItem>
      <FileWatchItem eventName="AsmSysAvFMEvent" container="azsecsysavfm" compressionType="gzip" contextParam="QuarantineFiles" directoryQuotaInMB="100" lastChangeOffsetInSeconds="60" uploadDelayInSeconds="60" removeEmptyDirectories="false" retentionInDays="30" account="AzSecurityStore">
        <Directory><![CDATA[Concat("", GetEnvironmentVariable("ProgramData"), "\Microsoft\Windows Defender\Quarantine")]]></Directory>
      </FileWatchItem>
    </FileMonitors>
    <WindowsEventLogSubscriptions>
      <!-- Captures all Microsoft Antimalware Events -->
      <Subscription eventName="AsmSysMepLocal" query="System!*[System[Provider[@Name='Microsoft Antimalware'] and (EventID != 5007)]]" storeType="Local">
        <Column name="EventProvider" defaultAssignment="">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType" defaultAssignment="0">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated" defaultAssignment="">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/Event/EventData/Data</Value>
        </Column>
        <Column name="EventDescription">
          <Value>GetEventMetadata("Description")</Value>
        </Column>
      </Subscription>
      <!-- Captures all Windows Defender Events -->
      <Subscription eventName="AsmSysMepLocal" query="Microsoft-Windows-Windows Defender/Operational!*[System[(EventID != 5007)]]" storeType="Local">
        <Column name="EventProvider" defaultAssignment="">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType" defaultAssignment="0">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated" defaultAssignment="">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/Event/EventData/Data</Value>
        </Column>
        <Column name="EventDescription">
          <Value>GetEventMetadata("Description")</Value>
        </Column>
      </Subscription>
    </WindowsEventLogSubscriptions>
    <DerivedEvents>
      <DerivedEvent source="AsmSysMepLocal" eventName="AsmSysAV" storeType="CentralBond" duration="PT5M" account="AzSecurityStore" retentionInDays="90">
        <Query><![CDATA[
					let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")		  
					let AssetIdentity=GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
					let NodeIdentity=""
					let NodeType=""

					select
						ReportingIdentity, AssetIdentity, NodeIdentity, NodeType,
						EventProvider, EventType, TimeCreated, EventPayload, EventDescription
					]]></Query>
      </DerivedEvent>
    </DerivedEvents>
  </Events>
</MonitoringManagement>