<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Imports>
    <Import file="AzSecMdsSystemEventsOfflineHE.xml" forceLocal="true" />
  </Imports>
  <!--
    Standard security events collected for various reporting and alerting purposes including:
    - security monitoring
    - evidence for compliance reporting
    - vulnerability analysis
    
    All MDS tables declared in this file use one of the following MDS account aliases that must be declared in the service's main MDS configuration.
    This enables uploading different classes of data to different storage accounts, but is not required. The aliases provide a logical storage 
    account mapping that can all write to a single account or to separate accounts depending on how the services chooses to secure its data.
    The aliases here are:
        AuditStore - associated with tables that may include events with PII
        MdsCHostShared - associated with all other security monitoring tables that do not contain PII
    -->
  <Events>
    <EventDeclarations storeType="CentralBond">
      <!-- Events that are already known to the team -->
      <Declaration eventName="AsmSysChg" account="MdsCHostShared" />
      <Declaration eventName="AsmSysADAccess" account="MdsCHostShared" />
      <Declaration eventName="AsmSysAuth" account="MdsCHostShared" />
      <Declaration eventName="AsmSysNet" account="MdsCHostShared" />
      <Declaration eventName="AsmSysLogon" account="MdsCHostShared" />
      <Declaration eventName="AsmSysWHEA2" account="MdsCHostShared" />
      <Declaration eventName="AsmSysCmd" account="MdsCHostShared" />
      <Declaration eventName="AsmSysSql" account="MdsCHostShared" />
      <Declaration eventName="AsmSysSecurity" account="MdsCHostShared" />
      <!-- Create a few more entries for reserved columns-->
      <Declaration eventName="AsmSysInv1" account="MdsCHostShared" />
      <Declaration eventName="AsmSysInv2" account="MdsCHostShared" />
      <Declaration eventName="AsmSysInv3" account="MdsCHostShared" />
      <Declaration eventName="AsmSysInv4" account="MdsCHostShared" />
      <Declaration eventName="AsmSysInv5" account="MdsCHostShared" />
    </EventDeclarations>
    <FileMonitors>
      <FileWatchItem eventName="AsmSysFMEvent" account="MdsCHostShared" storeType="CentralBond" container="azsecsysevtfm" />
    </FileMonitors>
  </Events>
  <!--Enable Syssecurity events upload to Cosmos from eventhub -->
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AsmSysChg$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysADAccess$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysAuth$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysNet$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysLogon$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysWHEA2$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysCmd$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysInv1$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysInv2$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysInv3$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysInv4$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmSysInv5$">
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>