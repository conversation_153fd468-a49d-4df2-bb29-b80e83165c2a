<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Events>
    <FileMonitors storeType="CentralBond">
      <FileWatchItem eventName="AsmVsaFMSnap" container="shava-snapshots" account="MdsCHostShared" compressionType="none" directoryQuotaInMB="100" uploadDelayInSeconds="10" retentionInDays="5" removeEmptyDirectories="true">
        <Directory><![CDATA[Concat("", GetEnvironmentVariable("LOCALAPPDATA"), "\ShavaVulnScanSnap")]]></Directory>
      </FileWatchItem>
    </FileMonitors>
    <DerivedEvents>
      <DerivedEvent source="AsmScannerData" eventName="AsmVsaData" account="MdsCHostShared" duration="PT5M" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                        where EventProvider = "OffNodeVulnScan"
                        select ReportingIdentity,
                            AssetIdentity,
                            NodeIdentity,
                            NodeType,
                            EventProvider,
                            EventType,
                            EventPayload,
                            Truncated,
                            TotalChunks,
                            ChunkId,
                            ChunkReference,
						    UserField1, 
                            UserField2, 
                            UserField3, 
                            UserField4, 
                            UserField5
            ]]></Query>
      </DerivedEvent>
      <DerivedEvent source="AsmDiagnostics" eventName="AsmVsaDiag" account="MdsCHostShared" duration="PT15M" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                        where EventProvider = "OffNodeVulnScan" && (EventType = "Error" || EventType = "Warning")
                        select ReportingIdentity,
                            AssetIdentity,
                            NodeIdentity,
                            NodeType,
                            EventProvider,
                            EventType,
                            EventPayload,
                            Truncated,
                            TotalChunks,
                            ChunkId,
                            ChunkReference,
						    UserField1, 
                            UserField2, 
                            UserField3, 
                            UserField4, 
                            UserField5
            ]]></Query>
      </DerivedEvent>
      <DerivedEvent source="AsmAlertsData" eventName="AsmVsaAl" account="MdsCHostShared" duration="PT1M" priority="Normal" retryTimeout="PT1H" storeType="CentralBond" whereToRun="Local" retentionInDays="30">
        <Query><![CDATA[
                        where EventProvider = "OffNodeVulnScan"
                        select ReportingIdentity, 
                            AssetIdentity, 
                            NodeIdentity, 
                            NodeType,
                            EventProvider, 
                            EventType, 
                            EventPayload, 
                            Truncated, 
                            TotalChunks, 
                            ChunkId, 
                            ChunkReference,
						    UserField1, 
                            UserField2, 
                            UserField3, 
                            UserField4, 
                            UserField5
            ]]></Query>
      </DerivedEvent>
    </DerivedEvents>
  </Events>
</MonitoringManagement>