<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <!--
    All Code Integrity event logs are collected.
    
    All MDS tables declared in this file use one of the following MDS account aliases that must be declared in the service's main MDS configuration.
    This enables uploading different classes of data to different storage accounts, but is not required. The aliases provide a logical storage 
    account mapping that can all write to a single account or to separate accounts depending on how the services chooses to secure its data.
    The aliases here are:
        AuditStore - associated with tables that may include events with PII
        AzSecurityStore - associated with all other security monitoring tables that do not contain PII
        
    Revision 9/11/2017 Payalr - updated as per unified schema for AzSecPack Auto update
    1/18/2018 - added ability to monitor clear log events
    -->
  <Events>
    <WindowsEventLogSubscriptions>
      <!-- Code Integrity Policy Exe Violations 
            3076 is audit from CI
            3077 would be the enforcement event version of 3076(if a file was blocked by policy)
            3067 is audit from winload, it has less information overall, and can only be a kernel block
            3068 is the enforcement (non-audit) version of 3067
            Events here are routed to security store
            -->
      <Subscription eventName="LocalCIExe" query="Microsoft-Windows-CodeIntegrity/Operational!*" storeType="Local">
        <Column name="EventProvider" defaultAssignment="">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType" defaultAssignment="0">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated" defaultAssignment="">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/</Value>
        </Column>
      </Subscription>
      <!--  Collects everything from AppLocker MSI and SCRIPT channels. Code integrity violation events for script reuses the AppLocker/ MSI and Script event channel (it can't be in the normal CI channel for esoteric reasons)- more poor behavior for the event.Code Integrity Policy Script Violation events:  
            8028 = Audit mode policy fail
            8029 = Enforced policy fail
            8036 is new to RS3 in enforcement and is an informational event about a failure in WldpIsClassInApprovedList,
            related to a hardcoded list of COM classes that get blocked in interactive prompts (and some other load scenarios) when UMCI is on
            Events here are routed to security store
            -->
      <Subscription eventName="LocalCIScr" query="Microsoft-Windows-AppLocker/MSI and Script!*[System[(EventID=8028) or (EventID=8029) or (EventID=8036)]]" storeType="Local">
        <Column name="EventProvider" defaultAssignment="">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType" defaultAssignment="0">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated" defaultAssignment="">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/</Value>
        </Column>
      </Subscription>
      <!-- Collect log clear event 
            -->
      <Subscription eventName="LocalCIdel" query="System!*[System[Provider[@Name='Microsoft-Windows-Eventlog'] and (EventID=104)] and UserData[LogFileCleared[(Channel='Microsoft-Windows-CodeIntegrity/Operational')]]]" storeType="Local">
        <Column name="EventProvider" defaultAssignment="">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="EventType" defaultAssignment="0">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="TimeCreated" defaultAssignment="">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="EventPayload" defaultAssignment="">
          <Value>/</Value>
        </Column>
      </Subscription>
    </WindowsEventLogSubscriptions>
    <DerivedEvents>
      <DerivedEvent sourceRegex="LocalCI.+" duration="PT5M" eventName="AsmSLCI" priority="Low" retryTimeout="PT10080M" account="AzSecurityStore" storeType="CentralBond" whereToRun="Local">
        <Query><![CDATA[
                      let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")
                      let AssetIdentity = GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
                      let NodeIdentity=""
                      let NodeType=""
                      let PlatformType="NonHE"
                      let Version="4.6"
                      Select ReportingIdentity,
                          AssetIdentity,
                          NodeIdentity, 
                          NodeType,
                          EventProvider,
                          EventType, 
                          TimeCreated,
                          EventPayload,
                          PlatformType,
                          Version
                    ]]></Query>
      </DerivedEvent>
      <DerivedEvent sourceRegex="LocalCI.+" duration="PT5M" eventName="AsmSLCIAl" priority="Low" retryTimeout="PT10080M" account="AzSecurityStore" storeType="CentralBond" whereToRun="Local">
        <Query><![CDATA[
                      let ReportingIdentity=GetStaticEnvironmentVariable("MA_HEARTBEAT_IDENTITY")
                      let AssetIdentity = GetStaticEnvironmentVariable("MA_AZURE_IDENTITY")
                      let NodeIdentity=""
                      let NodeType=""
                      let PlatformType="NonHE"
                      let Version="4.6"
                      Select ReportingIdentity,
                          AssetIdentity,
                          NodeIdentity, 
                          NodeType,
                          EventProvider,
                          EventType, 
                          TimeCreated,
                          EventPayload,
                          PlatformType,
                          Version
                    where EventType == "3076" || EventType =="3077" || EventType =="8028" || EventType =="8029" || EventType == "104"
                    ]]></Query>
      </DerivedEvent>
    </DerivedEvents>
  </Events>
</MonitoringManagement>