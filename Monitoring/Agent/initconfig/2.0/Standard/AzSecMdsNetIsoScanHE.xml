<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" timestamp="2018-11-22T05:37:42.8320908Z">
  <!-- Autogenerated version comment - DO NOT REMOVE: AzSecPackShipVersion=******** -->
  <Imports>
    <Import file="AzSecMdsNetIsoScanOfflineHE.xml" forceLocal="true" />
  </Imports>
  <Events>
    <EventDeclarations storeType="CentralBond">
      <!-- legacy event (azsecpack <= v4.3) -->
      <Declaration eventName="AsmInvFwRl" account="MdsCHostShared" />
      <!-- standard events (azsecpack >= v4.4) -->
      <Declaration eventName="AsmNetIsoAl" account="MdsCHostShared" />
      <Declaration eventName="AsmNetIsoData" account="MdsCHostShared" />
      <Declaration eventName="AsmNetIsoDiag" account="MdsCHostShared" />
      <Declaration eventName="AsmNetIsoInv" account="MdsCHostShared" />
    </EventDeclarations>
    <FileMonitors>
      <FileWatchItem eventName="AsmNetIsoFM" account="MdsCHostShared" storeType="CentralBond" container="azsecnetisofm" />
    </FileMonitors>
  </Events>
  <EventStreamingAnnotations>
    <!-- legacy event (azsecpack <= v4.3) -->
    <EventStreamingAnnotation name="^AsmInvFwRl$">
      <Cosmos>
        <Content><![CDATA[<Config/>]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <!-- standard events (azsecpack >= v4.4) -->
    <EventStreamingAnnotation name="^AsmNetIsoAl$">
      <Cosmos>
        <Content><![CDATA[<Config/>]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmNetIsoData$">
      <Cosmos>
        <Content><![CDATA[<Config/>]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmNetIsoDiag$">
      <Cosmos>
        <Content><![CDATA[<Config/>]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmNetIsoFM$">
      <Cosmos>
        <Content><![CDATA[<Config/>]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^AsmNetIsoInv$">
      <Cosmos>
        <Content><![CDATA[<Config/>]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>