<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.IT.Commerce.Diagnostics.Abstractions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Category">
            <summary>
            A class containing constant strings used to define logging categories
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Category.Event">
            <summary>
            The Event category which aggregate all log events in all categories
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Category.Exception">
            <summary>
            The Exception category used to aggregate all error messages
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Category.Message">
            <summary>
            The Message category used to aggregate all message entries
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Category.Timing">
            <summary>
            The Timing category used to aggregate all method timings
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Category.Metric">
            <summary>
            The Timing category used to aggregate all application metrics
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Category.Milestone">
            <summary>
            The Milestone category used to aggregate all business process tracking messages
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Category.ThreatAudit">
            <summary>
            The ThreatAudit category used to aggregate all audit result messages from ThreatMetrics scans
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Category.BusinessEvent">
            <summary>
            The BusinessEvent category used to aggregate all business checkpoints
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Category.Request">
            <summary>
            The Request category used to aggregate all requests
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Defaults.MethodName">
            <summary>
            The default method name.
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Defaults.DefaultCategory">
            <summary>
            The default log category.
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Defaults.Severity">
            <summary>
            The default log severity.
            </summary>
        </member>
        <member name="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Defaults.LogCreatorName">
            <summary>
            The default database frequency (in milliseconds) to send logs to the db.
            </summary>
        </member>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.ICorrelationIdProvider">
            <summary>
            Contract for a correlation id provider.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.ICorrelationIdProvider.GetCorrelationId">
            <summary>
            Gets the correlation id for the current activity or <c>null</c> if none defined.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.ICorrelationIdProvider.SetCorrelationId(System.Guid)">
            <summary>
            Sets the correlation id for the current activity.
            </summary>
            <param name="correlationId">The correlation id.</param>
        </member>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.DiagnosticsCorrelationManager">
            <summary>
            Manages the correlation 
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsCorrelationManager.CorrelationId">
            <summary>
            Gets or sets the correlation id.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsCorrelationManager.CorrelationIdProviders">
            <summary>
            Gets or sets the correlation identifier providers.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsCorrelationManager.SetCorrelationIdProviders(System.Collections.Generic.IEnumerable{Microsoft.IT.Commerce.Diagnostics.ICorrelationIdProvider})">
            <summary>
            Sets the correlation id providers in order of precedence.
            </summary>
            <param name="providers">The ordered list of providers.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsBusinessEvent.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.DiagnosticsBusinessEvent"/> class. <br/>
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsBusinessEvent.BusinessEventName">
            <summary>
            Gets or sets the name of this business event.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsBusinessEvent.Metrics">
            <summary>
            Gets or sets the metrics for this event.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsBusinessEvent.ToMilestone">
            <summary>
            Converts business event to milestone.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Message">
            <summary>
            Gets or sets the log message.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Severity">
            <summary>
            Gets or sets the severity of the event.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.TimeStamp">
            <summary>
            Gets or sets the date and time of the occurrence of the event.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.EventId">
            <summary>
            Gets or sets the event identifier.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Title">
            <summary>
            Gets or sets the title of the event.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Category">
            <summary>
            Gets or sets the category for the event.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.LogAttributes">
            <summary>
            Gets or sets the attributes associated with this event.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.ApplicationName">
            <summary>
            Gets or sets the name of the application.
            Note: If not set, defaults to the application name in configuration.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.EnvironmentName">
            <summary>
            Gets or sets the name of the environment.
            Note: If not set, defaults to the environment name in configuration.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.MachineName">
            <summary>
            Gets or sets the machine name on which the event occurred.
            Note: This property is automatically set by diagnostics.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.ClassName">
            <summary>
            Gets or sets the name of the caller's class name.
            Note: This property is automatically set by diagnostics.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.MethodName">
            <summary>
            Gets or sets the name of the caller's method name.
            Note: This property is automatically set by diagnostics.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.ActivityId">
            <summary>
            Gets or sets the activity id of the thread in which the event occurred.
            Note: This property is automatically set by diagnostics.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.LogCreatorName">
            <summary>
            Gets or sets the Log creator Name
            Note: If not set, it defaults to as "Not Configured"
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsLogException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.DiagnosticsLogException"/> class. <br/>
            Defaults <see cref="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Category"/> to <see cref="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Category.Exception"/> <br/>
            Defaults <see cref="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Severity"/> to <see cref="F:System.Diagnostics.TraceEventType.Error"/> <br/>
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsLogException.Exception">
            <summary>
            Gets or sets the exception which occurred.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsLogMessage.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.DiagnosticsLogMessage"/> class. <br/>
            Defaults <see cref="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Category"/> to <see cref="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Category.Message"/>. <br/>
            Defaults <see cref="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Severity"/> to <see cref="F:System.Diagnostics.TraceEventType.Information"/>. <br/>
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMetric.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMetric"/> class. <br/>
            Defaults <see cref="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Category"/> to <see cref="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Category.Metric"/>. <br/>
            Defaults <see cref="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Severity"/> to <see cref="F:System.Diagnostics.TraceEventType.Information"/>. <br/>
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMetric.MetricName">
            <summary>
            Gets or sets the name of the timing transaction.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMetric.MetricValue">
            <summary>
            Gets or sets the metric value.
            </summary>
        </member>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMilestone">
            <summary>
            A class used to house relevant data describing the occurrence of a business process or workflow step
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMilestone.#ctor">
            <summary>
            Default constructor that prefills MilestoneInstanceId and DateTimeStamp. <br/>
            Defaults <see cref="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Category"/> to <see cref="!:Diagnostics.Constants.DiagnosticsConstants.Category.Milestone"/>. <br/>
            Defaults <see cref="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Severity"/> to <see cref="F:System.Diagnostics.TraceEventType.Information"/>. <br/>
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMilestone.ApplicationId">
            <summary>
            Gets or sets the unique identifier of the application definition that the milestone is associated with
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMilestone.WorkflowId">
            <summary>
            Gets or sets the unique identifier of the workflow definition that the milestone is associated with
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMilestone.WorkflowName">
            <summary>
            Gets or sets the name of the workflow definition that the milestone is associated with
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMilestone.MilestoneId">
            <summary>
            Gets or sets the unique identifier of the milestone definition
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMilestone.MilestoneName">
            <summary>
            Gets or sets the name of the milestone definition
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMilestone.WorkflowInstanceId">
            <summary>
            Gets or sets the unique identifier of the specific instance of the workflow
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMilestone.MilestoneInstanceId">
            <summary>
            Gets or sets the unique identifier of the specific instance of the milestone
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMilestone.ParentWorkflowInstanceId">
            <summary>
            Gets or sets the unique identifier of the parent workflow that this milestone's workflow is associated with (this value is optional)
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMilestone.MilestoneState">
            <summary>
            Gets or sets the state of the workflow as a result of the occurrence of this milestone
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMilestone.ReferenceId">
            <summary>
            Gets or sets a value that can be used to link this milestone to another external object known to the milestone creator (this value is optional)
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMilestone.AlternateReferenceId">
            <summary>
            Gets or sets a alternate value that can be used to link this milestone to another external object known to the milestone creator (this value is optional)
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMilestone.PayloadXml">
            <summary>
            Gets or sets an xml blob containing reference data that are relevant to this milestone.  It is expected that this data 
            can be used to drill down into the execution context that generated the milestone record for analysis purposes
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMilestone.Metrics">
            <summary>
            Gets or sets the metrics for this event.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsRequest.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.DiagnosticsRequest"/> class.<br/>
            Defaults <see cref="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Category"/> to <see cref="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Category.Request"/> <br/>
            Defaults <see cref="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Severity"/> to <see cref="F:System.Diagnostics.TraceEventType.Information"/> <br/>
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsRequest.RequestUrl">
            <summary>
            Gets or sets request url (optional).
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsRequest.RequestHttpMethod">
            <summary>
            Gets or sets the HTTP method of the request.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsRequest.RequestName">
            <summary>
            Gets or sets human-readable name of the requested page.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsRequest.RequestDuration">
            <summary>
            Gets or sets the amount of time it took the application to handle the request.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsRequest.RequestResponseCode">
            <summary>
            Gets or sets response code returned by the application after handling the request.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsRequest.RequestSuccess">
            <summary>
            Gets or sets a value indicating whether application handled the request successfully.
            </summary>
        </member>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.DiagnosticsThreatAudit">
            <summary>
            A class used to encapsulate an Audit entry generated by ThreatMetrics
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsThreatAudit.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.DiagnosticsThreatAudit"/> class. <br/>
            Defaults <see cref="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Category"/> to <see cref="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Category.ThreatAudit"/>. <br/>
            Defaults <see cref="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Severity"/> to <see cref="F:System.Diagnostics.TraceEventType.Information"/>. <br/>
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsThreatAudit.ApplicationId">
            <summary>
            Gets or sets the unique identifier of the application definition that is trying to log the audit information
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsThreatAudit.PrimaryIdentifier">
            <summary>
            Gets or sets the primary identifier of the user
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsThreatAudit.SecondaryIdentifier">
            <summary>
            Gets or sets the secondary identifier of the user
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsThreatAudit.SessionId">
            <summary>
            Gets or sets the session ID
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsThreatAudit.AuditMessage">
            <summary>
            Gets or sets the actual ThreatMetrics result message that needs to be logged
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsThreatAudit.DateCreated">
            <summary>
            Gets or sets the time when the audit entry was created
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsThreatAudit.DateModified">
            <summary>
            Gets or sets the time when the audit entry was last modified
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsTiming.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.DiagnosticsTiming"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsTiming.TransactionName">
            <summary>
            Gets or sets the name of the timing transaction.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsTiming.ElapsedMilliseconds">
            <summary>
            Gets or sets the duration of the timing event in milliseconds.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.DiagnosticsTiming.op_Implicit(Microsoft.IT.Commerce.Diagnostics.DiagnosticsTiming)~Microsoft.IT.Commerce.Diagnostics.DiagnosticsMetric">
            <summary>
            Performs an implicit conversion from <see cref="T:Microsoft.IT.Commerce.Diagnostics.DiagnosticsTiming"/> to <see cref="T:Microsoft.IT.Commerce.Diagnostics.DiagnosticsMetric"/>.
            Sets <see cref="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Category"/> to <see cref="F:Microsoft.IT.Commerce.Diagnostics.DiagnosticsConstants.Category.Timing"/>. <br/>
            Sets <see cref="P:Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase.Severity"/> to <see cref="F:System.Diagnostics.TraceEventType.Critical"/> to ensure timing events are not filtered out. <br/>
            </summary>
            <param name="timing">The timing event.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.IDiagnosticsLogger.Write(Microsoft.IT.Commerce.Diagnostics.DiagnosticsEntityBase,System.String)">
            <summary>
            Writes a diagnostics entity to the configured trace listeners.
            </summary>
            <param name="diagnosticsEntity">The diagnostics entity.</param>
            <param name="methodName">Name of the calling method.  Note: Do NOT pass this parameter as the compiler will fill it in for you.</param>
        </member>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.TimedAttribute">
            <summary>
            Attribute implementation
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.TimedAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.TimedAttribute"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.TimedAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.TimedAttribute"/> class.
            </summary>
            <param name="prefix">The prefix to add to the transaction name.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.TimedAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.TimedAttribute"/> class.
            </summary>
            <param name="prefix">The prefix to add to the transaction name.</param>
            <param name="suffix">The suffix to add to the transaction name.</param>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.TimedAttribute.Order">
            <summary>
            Order in which the handler will be executed
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.TimedAttribute.PrefixMessage">
            <summary>
            Gets or sets the prefix message to add to the transaction name.
            </summary>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.TimedAttribute.SuffixMessage">
            <summary>
            Gets or sets the suffix message to add to the transaction name.
            </summary>
        </member>
    </members>
</doc>
