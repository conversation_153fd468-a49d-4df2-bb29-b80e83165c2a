﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Text.Json</name>
  </assembly>
  <members>
    <member name="T:System.Text.Json.JsonCommentHandling">
      <summary>Defines how the <see cref="T:System.Text.Json.Utf8JsonReader" /> struct handles comments.</summary>
    </member>
    <member name="F:System.Text.Json.JsonCommentHandling.Allow">
      <summary>Allows comments within the JSON input and treats them as valid tokens. While reading, the caller can access the comment values.</summary>
    </member>
    <member name="F:System.Text.Json.JsonCommentHandling.Disallow">
      <summary>Doesn't allow comments within the JSON input. Comments are treated as invalid JSON if found, and a <see cref="T:System.Text.Json.JsonException" /> is thrown. This is the default value.</summary>
    </member>
    <member name="F:System.Text.Json.JsonCommentHandling.Skip">
      <summary>Allows comments within the JSON input and ignores them. The <see cref="T:System.Text.Json.Utf8JsonReader" /> behaves as if no comments are present.</summary>
    </member>
    <member name="T:System.Text.Json.JsonDocument">
      <summary>Provides a mechanism for examining the structural content of a JSON value without automatically instantiating data values.</summary>
    </member>
    <member name="M:System.Text.Json.JsonDocument.Dispose">
      <summary>Releases the resources used by this <see cref="T:System.Text.Json.JsonDocument" /> instance.</summary>
    </member>
    <member name="M:System.Text.Json.JsonDocument.Parse(System.Buffers.ReadOnlySequence{System.Byte},System.Text.Json.JsonDocumentOptions)">
      <summary>Parses a sequence as UTF-8-encoded text representing a single JSON byte value into a JsonDocument.</summary>
      <param name="utf8Json">The JSON text to parse.</param>
      <param name="options">Options to control the reader behavior during parsing.</param>
      <returns>A JsonDocument representation of the JSON value.</returns>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="utf8Json" /> does not represent a valid single JSON value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="readerOptions" /> contains unsupported options.</exception>
    </member>
    <member name="M:System.Text.Json.JsonDocument.Parse(System.IO.Stream,System.Text.Json.JsonDocumentOptions)">
      <summary>Parses a <see cref="T:System.IO.Stream" /> as UTF-8-encoded data representing a single JSON value into a JsonDocument. The stream is read to completion.</summary>
      <param name="utf8Json">The JSON data to parse.</param>
      <param name="options">Options to control the reader behavior during parsing.</param>
      <returns>A JsonDocument representation of the JSON value.</returns>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="utf8Json" /> does not represent a valid single JSON value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="readerOptions" /> contains unsupported options.</exception>
    </member>
    <member name="M:System.Text.Json.JsonDocument.Parse(System.ReadOnlyMemory{System.Byte},System.Text.Json.JsonDocumentOptions)">
      <summary>Parses memory as UTF-8-encoded text representing a single JSON byte value into a JsonDocument.</summary>
      <param name="utf8Json">The JSON text to parse.</param>
      <param name="options">Options to control the reader behavior during parsing.</param>
      <returns>A JsonDocument representation of the JSON value.</returns>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="utf8Json" /> does not represent a valid single JSON value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="readerOptions" /> contains unsupported options.</exception>
    </member>
    <member name="M:System.Text.Json.JsonDocument.Parse(System.ReadOnlyMemory{System.Char},System.Text.Json.JsonDocumentOptions)">
      <summary>Parses text representing a single JSON character value into a JsonDocument.</summary>
      <param name="json">The JSON text to parse.</param>
      <param name="options">Options to control the reader behavior during parsing.</param>
      <returns>A JsonDocument representation of the JSON value.</returns>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="json" /> does not represent a valid single JSON value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="readerOptions" /> contains unsupported options.</exception>
    </member>
    <member name="M:System.Text.Json.JsonDocument.Parse(System.String,System.Text.Json.JsonDocumentOptions)">
      <summary>Parses text representing a single JSON string value into a JsonDocument.</summary>
      <param name="json">The JSON text to parse.</param>
      <param name="options">Options to control the reader behavior during parsing.</param>
      <returns>A JsonDocument representation of the JSON value.</returns>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="json" /> does not represent a valid single JSON value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="readerOptions" /> contains unsupported options.</exception>
    </member>
    <member name="M:System.Text.Json.JsonDocument.ParseAsync(System.IO.Stream,System.Text.Json.JsonDocumentOptions,System.Threading.CancellationToken)">
      <summary>Parses a <see cref="T:System.IO.Stream" /> as UTF-8-encoded data representing a single JSON value into a JsonDocument. The stream is read to completion.</summary>
      <param name="utf8Json">The JSON data to parse.</param>
      <param name="options">Options to control the reader behavior during parsing.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests.</param>
      <returns>A task to produce a JsonDocument representation of the JSON value.</returns>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="utf8Json" /> does not represent a valid single JSON value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="readerOptions" /> contains unsupported options.</exception>
    </member>
    <member name="M:System.Text.Json.JsonDocument.ParseValue(System.Text.Json.Utf8JsonReader@)">
      <summary>Parses one JSON value (including objects or arrays) from the provided reader.</summary>
      <param name="reader">The reader to read.</param>
      <returns>A JsonDocument representing the value (and nested values) read from the reader.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> contains unsupported options.
-or-
The current <paramref name="reader" /> token does not start or represent a value.</exception>
      <exception cref="T:System.Text.Json.JsonException">A value could not be read from the reader.</exception>
    </member>
    <member name="P:System.Text.Json.JsonDocument.RootElement">
      <summary>Gets the root element of this JSON document.</summary>
      <returns>A <see cref="T:System.Text.Json.JsonElement" /> representing the value of the document.</returns>
    </member>
    <member name="M:System.Text.Json.JsonDocument.TryParseValue(System.Text.Json.Utf8JsonReader@,System.Text.Json.JsonDocument@)">
      <summary>Attempts to parse one JSON value (including objects or arrays) from the provided reader.</summary>
      <param name="reader">The reader to read.</param>
      <param name="document">When the method returns, contains the parsed document.</param>
      <returns>
        <see langword="true" /> if a value was read and parsed into a JsonDocument; <see langword="false" /> if the reader ran out of data while parsing. All other situations result in an exception being thrown.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> contains unsupported options.
-or-
The current <paramref name="reader" /> token does not start or represent a value.</exception>
      <exception cref="T:System.Text.Json.JsonException">A value could not be read from the reader.</exception>
    </member>
    <member name="M:System.Text.Json.JsonDocument.WriteTo(System.Text.Json.Utf8JsonWriter)">
      <summary>Writes the document to the provided writer as a JSON value.</summary>
      <param name="writer">The writer to which to write the document.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Text.Json.JsonElement.ValueKind" /> of this <see cref="P:System.Text.Json.JsonDocument.RootElement" /> would result in invalid JSON.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="T:System.Text.Json.JsonDocumentOptions">
      <summary>Provides the ability for the user to define custom behavior when parsing JSON to create a <see cref="T:System.Text.Json.JsonDocument" />.</summary>
    </member>
    <member name="P:System.Text.Json.JsonDocumentOptions.AllowTrailingCommas">
      <summary>Gets or sets a value that indicates whether an extra comma at the end of a list of JSON values in an object or array is allowed (and ignored) within the JSON payload being read.</summary>
      <returns>
        <see langword="true" /> if an extra comma at the end of a list of JSON values in an object or array is allowed; otherwise, <see langword="false" />. Default is <see langword="false" /></returns>
    </member>
    <member name="P:System.Text.Json.JsonDocumentOptions.CommentHandling">
      <summary>Gets or sets a value that determines how the <see cref="T:System.Text.Json.JsonDocument" /> handles comments when reading through the JSON data.</summary>
      <returns>One of the enumeration values that indicates how comments are handled.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The comment handling enum is set to a value that is not supported (or not within the <see cref="T:System.Text.Json.JsonCommentHandling" /> enum range).</exception>
    </member>
    <member name="P:System.Text.Json.JsonDocumentOptions.MaxDepth">
      <summary>Gets or sets the maximum depth allowed when parsing JSON data, with the default (that is, 0) indicating a maximum depth of 64.</summary>
      <returns>The maximum depth allowed when parsing JSON data.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The max depth is set to a negative value.</exception>
    </member>
    <member name="T:System.Text.Json.JsonElement">
      <summary>Represents a specific JSON value within a <see cref="T:System.Text.Json.JsonDocument" />.</summary>
    </member>
    <member name="T:System.Text.Json.JsonElement.ArrayEnumerator">
      <summary>Represents an enumerator for the contents of a JSON array.</summary>
    </member>
    <member name="P:System.Text.Json.JsonElement.ArrayEnumerator.Current">
      <summary>Gets the element in the collection at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ArrayEnumerator.Dispose">
      <summary>Releases the resources used by this <see cref="T:System.Text.Json.JsonElement.ArrayEnumerator" /> instance.</summary>
    </member>
    <member name="M:System.Text.Json.JsonElement.ArrayEnumerator.GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator that can be used to iterate through the array.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ArrayEnumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the collection.</summary>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ArrayEnumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
    </member>
    <member name="M:System.Text.Json.JsonElement.ArrayEnumerator.System#Collections#Generic#IEnumerable{System#Text#Json#JsonElement}#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator for an array of <see cref="T:System.Text.Json.JsonElement" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ArrayEnumerator.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:System.Text.Json.JsonElement.ArrayEnumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element in the collection at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.Clone">
      <summary>Gets a JsonElement that can be safely stored beyond the lifetime of the original <see cref="T:System.Text.Json.JsonDocument" />.</summary>
      <returns>A JsonElement that can be safely stored beyond the lifetime of the original <see cref="T:System.Text.Json.JsonDocument" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.EnumerateArray">
      <summary>Gets an enumerator to enumerate the values in the JSON array represented by this JsonElement.</summary>
      <returns>An enumerator to enumerate the values in the JSON array represented by this JsonElement.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Array" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.EnumerateObject">
      <summary>Gets an enumerator to enumerate the properties in the JSON object represented by this JsonElement.</summary>
      <returns>An enumerator to enumerate the properties in the JSON object represented by this JsonElement.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Object" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetArrayLength">
      <summary>Gets the number of values contained within the current array value.</summary>
      <returns>The number of values contained within the current array value.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Array" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetBoolean">
      <summary>Gets the value of the element as a <see cref="T:System.Boolean" />.</summary>
      <returns>The value of the element as a <see cref="T:System.Boolean" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is neither <see cref="F:System.Text.Json.JsonValueKind.True" /> nor <see cref="F:System.Text.Json.JsonValueKind.False" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetByte">
      <summary>Gets the current JSON number as a <see cref="T:System.Byte" />.</summary>
      <returns>The current JSON number as a <see cref="T:System.Byte" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.Byte" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetBytesFromBase64">
      <summary>Gets the value of the element as a byte array.</summary>
      <returns>The value decoded as a byte array.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <exception cref="T:System.FormatException">The value is not encoded as Base64 text and hence cannot be decoded to bytes.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetDateTime">
      <summary>Gets the value of the element as a <see cref="T:System.DateTime" />.</summary>
      <returns>The value of the element as a <see cref="T:System.DateTime" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be read as a <see cref="T:System.DateTime" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetDateTimeOffset">
      <summary>Gets the value of the element as a <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>The value of the element as a <see cref="T:System.DateTimeOffset" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be read as a <see cref="T:System.DateTimeOffset" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetDecimal">
      <summary>Gets the current JSON number as a <see cref="T:System.Decimal" />.</summary>
      <returns>The current JSON number as a <see cref="T:System.Decimal" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetDouble">
      <summary>Gets the current JSON number as a <see cref="T:System.Double" />.</summary>
      <returns>The current JSON number as a <see cref="T:System.Double" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.Double" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetGuid">
      <summary>Gets the value of the element as a <see cref="T:System.Guid" />.</summary>
      <returns>The value of the element as a <see cref="T:System.Guid" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.Guid" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetInt16">
      <summary>Gets the current JSON number as an <see cref="T:System.Int16" />.</summary>
      <returns>The current JSON number as an <see cref="T:System.Int16" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as an <see cref="T:System.Int16" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetInt32">
      <summary>Gets the current JSON number as an <see cref="T:System.Int32" />.</summary>
      <returns>The current JSON number as an <see cref="T:System.Int32" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as an <see cref="T:System.Int32" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetInt64">
      <summary>Gets the current JSON number as an <see cref="T:System.Int64" />.</summary>
      <returns>The current JSON number as an <see cref="T:System.Int64" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.Int64" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetProperty(System.ReadOnlySpan{System.Byte})">
      <summary>Gets a <see cref="T:System.Text.Json.JsonElement" /> representing the value of a required property identified by <paramref name="utf8PropertyName" />.</summary>
      <param name="utf8PropertyName">The UTF-8 representation (with no Byte-Order-Mark (BOM)) of the name of the property to return.</param>
      <returns>A <see cref="T:System.Text.Json.JsonElement" /> representing the value of the requested property.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Object" />.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">No property was found with the requested name.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetProperty(System.ReadOnlySpan{System.Char})">
      <summary>Gets a <see cref="T:System.Text.Json.JsonElement" /> representing the value of a required property identified by <paramref name="propertyName" />.</summary>
      <param name="propertyName">The name of the property whose value is to be returned.</param>
      <returns>A <see cref="T:System.Text.Json.JsonElement" /> representing the value of the requested property.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Object" />.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">No property was found with the requested name.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetProperty(System.String)">
      <summary>Gets a <see cref="T:System.Text.Json.JsonElement" /> representing the value of a required property identified by <paramref name="propertyName" />.</summary>
      <param name="propertyName">The name of the property whose value is to be returned.</param>
      <returns>A <see cref="T:System.Text.Json.JsonElement" /> representing the value of the requested property.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Object" />.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">No property was found with the requested name.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetRawText">
      <summary>Gets a string that represents the original input data backing this value.</summary>
      <returns>The original input data backing this value.</returns>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetSByte">
      <summary>Gets the current JSON number as an <see cref="T:System.SByte" />.</summary>
      <returns>The current JSON number as an <see cref="T:System.SByte" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as an <see cref="T:System.SByte" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetSingle">
      <summary>Gets the current JSON number as a <see cref="T:System.Single" />.</summary>
      <returns>The current JSON number as a <see cref="T:System.Single" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.Single" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetString">
      <summary>Gets the value of the element as a <see cref="T:System.String" />.</summary>
      <returns>The value of the element as a <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is neither <see cref="F:System.Text.Json.JsonValueKind.String" /> nor <see cref="F:System.Text.Json.JsonValueKind.Null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetUInt16">
      <summary>Gets the current JSON number as a <see cref="T:System.UInt16" />.</summary>
      <returns>The current JSON number as a <see cref="T:System.UInt16" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.UInt16" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetUInt32">
      <summary>Gets the current JSON number as a <see cref="T:System.UInt32" />.</summary>
      <returns>The current JSON number as a <see cref="T:System.UInt32" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.UInt32" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetUInt64">
      <summary>Gets the current JSON number as a <see cref="T:System.UInt64" />.</summary>
      <returns>The current JSON number as a <see cref="T:System.UInt64" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.UInt64" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="P:System.Text.Json.JsonElement.Item(System.Int32)">
      <summary>Gets the value at the specified index if the current value is an <see cref="F:System.Text.Json.JsonValueKind.Array" />.</summary>
      <param name="index">The item index.</param>
      <returns>The value at the specified index.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Array" />.</exception>
      <exception cref="T:System.IndexOutOfRangeException">
        <paramref name="index" /> is not in the range [0, <see cref="M:System.Text.Json.JsonElement.GetArrayLength" />()).</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="T:System.Text.Json.JsonElement.ObjectEnumerator">
      <summary>Represents an enumerator for the properties of a JSON object.</summary>
    </member>
    <member name="P:System.Text.Json.JsonElement.ObjectEnumerator.Current">
      <summary>Gets the element in the collection at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ObjectEnumerator.Dispose">
      <summary>Releases the resources used by this <see cref="T:System.Text.Json.JsonElement.ObjectEnumerator" /> instance.</summary>
    </member>
    <member name="M:System.Text.Json.JsonElement.ObjectEnumerator.GetEnumerator">
      <summary>Returns an enumerator that iterates the properties of an object.</summary>
      <returns>An enumerator that can be used to iterate through the object.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ObjectEnumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the collection.</summary>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ObjectEnumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
    </member>
    <member name="M:System.Text.Json.JsonElement.ObjectEnumerator.System#Collections#Generic#IEnumerable{System#Text#Json#JsonProperty}#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator for <see cref="T:System.Text.Json.JsonProperty" /> objects that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ObjectEnumerator.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:System.Text.Json.JsonElement.ObjectEnumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element in the collection at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ToString">
      <summary>Gets a string representation for the current value appropriate to the value type.</summary>
      <returns>A string representation for the current value appropriate to the value type.</returns>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetByte(System.Byte@)">
      <summary>Attempts to represent the current JSON number as a <see cref="T:System.Byte" />.</summary>
      <param name="value">When the method returns, contains the byte equivalent of the current JSON number if the conversion succeeded.</param>
      <returns>
        <see langword="true" /> if the number can be represented as a <see cref="T:System.Byte" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetBytesFromBase64(System.Byte[]@)">
      <summary>Attempts to represent the current JSON string as a byte array, assuming that it is Base64 encoded.</summary>
      <param name="value">If the method succeeds, contains the decoded binary representation of the Base64 text.</param>
      <returns>
        <see langword="true" /> if the entire token value is encoded as valid Base64 text and can be successfully decoded to bytes; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetDateTime(System.DateTime@)">
      <summary>Attempts to represent the current JSON string as a <see cref="T:System.DateTime" />.</summary>
      <param name="value">When this method returns, contains the date and time value equivalent to the current JSON string.</param>
      <returns>
        <see langword="true" /> if the string can be represented as a <see cref="T:System.DateTime" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetDateTimeOffset(System.DateTimeOffset@)">
      <summary>Attempts to represent the current JSON string as a <see cref="T:System.DateTimeOffset" />.</summary>
      <param name="value">When this method returns, contains the date and time equivalent to the current JSON string.</param>
      <returns>
        <see langword="true" /> if the string can be represented as a <see cref="T:System.DateTimeOffset" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetDecimal(System.Decimal@)">
      <summary>Attempts to represent the current JSON number as a <see cref="T:System.Decimal" />.</summary>
      <param name="value">When this method returns, contains the decimal equivalent of the current JSON number.</param>
      <returns>
        <see langword="true" /> if the number can be represented as a <see cref="T:System.Decimal" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetDouble(System.Double@)">
      <summary>Attempts to represent the current JSON number as a <see cref="T:System.Double" />.</summary>
      <param name="value">When this method returns, contains a double-precision floating point value equivalent to the current JSON number.</param>
      <returns>
        <see langword="true" /> if the number can be represented as a <see cref="T:System.Double" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetGuid(System.Guid@)">
      <summary>Attempts to represent the current JSON string as a <see cref="T:System.Guid" />.</summary>
      <param name="value">When this method returns, contains the GUID equivalent to the current JSON string.</param>
      <returns>
        <see langword="true" /> if the string can be represented as a <see cref="T:System.Guid" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetInt16(System.Int16@)">
      <summary>Attempts to represent the current JSON number as an <see cref="T:System.Int16" />.</summary>
      <param name="value">When the method returns, contains the 16-bit integer equivalent of the current JSON number if the conversion succeeded.</param>
      <returns>
        <see langword="true" /> if the number can be represented as an <see cref="T:System.Int16" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetInt32(System.Int32@)">
      <summary>Attempts to represent the current JSON number as an <see cref="T:System.Int32" />.</summary>
      <param name="value">When this method returns, contains the 32-biut integer value equivalent to the current JSON number.</param>
      <returns>
        <see langword="true" /> if the number can be represented as an <see cref="T:System.Int32" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetInt64(System.Int64@)">
      <summary>Attempts to represent the current JSON number as a <see cref="T:System.Int64" />.</summary>
      <param name="value">When this method returns, contains the 64-bit integer value equivalent to the current JSON number.</param>
      <returns>
        <see langword="true" /> if the number can be represented as a <see cref="T:System.Int64" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetProperty(System.ReadOnlySpan{System.Byte},System.Text.Json.JsonElement@)">
      <summary>Looks for a property named <paramref name="utf8PropertyName" /> in the current object, returning a value that indicates whether or not such a property exists. When the property exists, the method assigns its value to the <paramref name="value" /> argument.</summary>
      <param name="utf8PropertyName">The UTF-8 (with no Byte-Order-Mark (BOM)) representation of the name of the property to return.</param>
      <param name="value">Receives the value of the located property.</param>
      <returns>
        <see langword="true" /> if the property was found; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Object" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetProperty(System.ReadOnlySpan{System.Char},System.Text.Json.JsonElement@)">
      <summary>Looks for a property named <paramref name="propertyName" /> in the current object, returning a value that indicates whether or not such a property exists. When the property exists, the method assigns its value to the <paramref name="value" /> argument.</summary>
      <param name="propertyName">The name of the property to find.</param>
      <param name="value">When this method returns, contains the value of the specified property.</param>
      <returns>
        <see langword="true" /> if the property was found; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Object" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetProperty(System.String,System.Text.Json.JsonElement@)">
      <summary>Looks for a property named <paramref name="propertyName" /> in the current object, returning a value that indicates whether or not such a property exists. When the property exists, its value is assigned to the <paramref name="value" /> argument.</summary>
      <param name="propertyName">The name of the property to find.</param>
      <param name="value">When this method returns, contains the value of the specified property.</param>
      <returns>
        <see langword="true" /> if the property was found; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Object" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetSByte(System.SByte@)">
      <summary>Attempts to represent the current JSON number as an <see cref="T:System.SByte" />.</summary>
      <param name="value">When the method returns, contains the signed byte equivalent of the current JSON number if the conversion succeeded.</param>
      <returns>
        <see langword="true" /> if the number can be represented as an <see cref="T:System.SByte" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetSingle(System.Single@)">
      <summary>Attempts to represent the current JSON number as a <see cref="T:System.Single" />.</summary>
      <param name="value">When this method returns, contains the single-precision floating point value equivalent to the current JSON number.</param>
      <returns>
        <see langword="true" /> if the number can be represented as a <see cref="T:System.Single" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetUInt16(System.UInt16@)">
      <summary>Attempts to represent the current JSON number as a <see cref="T:System.UInt16" />.</summary>
      <param name="value">When the method returns, contains the unsigned 16-bit integer equivalent of the current JSON number if the conversion succeeded.</param>
      <returns>
        <see langword="true" /> if the number can be represented as a <see cref="T:System.UInt16" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetUInt32(System.UInt32@)">
      <summary>Attempts to represent the current JSON number as a <see cref="T:System.UInt32" />.</summary>
      <param name="value">When this method returns, contains unsigned 32-bit integer value equivalent to the current JSON number.</param>
      <returns>
        <see langword="true" /> if the number can be represented as a <see cref="T:System.UInt32" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetUInt64(System.UInt64@)">
      <summary>Attempts to represent the current JSON number as a <see cref="T:System.UInt64" />.</summary>
      <param name="value">When this method returns, contains unsigned 64-bit integer value equivalent to the current JSON number.</param>
      <returns>
        <see langword="true" /> if the number can be represented as a <see cref="T:System.UInt64" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.ValueEquals(System.ReadOnlySpan{System.Byte})">
      <summary>Compares the text represented by a UTF8-encoded byte span to the string value of this element.</summary>
      <param name="utf8Text">The UTF-8 encoded text to compare against.</param>
      <returns>
        <see langword="true" /> if the string value of this element has the same UTF-8 encoding as
<paramref name="utf8Text" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.ValueEquals(System.ReadOnlySpan{System.Char})">
      <summary>Compares a specified read-only character span to the string value of this element.</summary>
      <param name="text">The text to compare against.</param>
      <returns>
        <see langword="true" /> if the string value of this element matches <paramref name="text" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.ValueEquals(System.String)">
      <summary>Compares a specified string to the string value of this element.</summary>
      <param name="text">The text to compare against.</param>
      <returns>
        <see langword="true" /> if the string value of this element matches <paramref name="text" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
    </member>
    <member name="P:System.Text.Json.JsonElement.ValueKind">
      <summary>Gets the type of the current JSON value.</summary>
      <returns>The type of the current JSON value.</returns>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.JsonElement.WriteTo(System.Text.Json.Utf8JsonWriter)">
      <summary>Writes the element to the specified writer as a JSON value.</summary>
      <param name="writer">The writer to which to write the element.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Text.Json.JsonElement.ValueKind" /> of this value is <see cref="F:System.Text.Json.JsonValueKind.Undefined" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="T:System.Text.Json.JsonEncodedText">
      <summary>Provides methods to transform UTF-8 or UTF-16 encoded text into a form that is suitable for JSON.</summary>
    </member>
    <member name="M:System.Text.Json.JsonEncodedText.Encode(System.ReadOnlySpan{System.Byte},System.Text.Encodings.Web.JavaScriptEncoder)">
      <summary>Encodes a UTF-8 text value as a JSON string.</summary>
      <param name="utf8Value">The UTF-8 encoded text to convert to JSON encoded text.</param>
      <param name="encoder">The encoder to use when escaping the string, or <see langword="null" /> to use the default encoder.</param>
      <returns>The encoded JSON text.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="utf8Value" /> is too large.
-or-
<paramref name="utf8Value" /> contains invalid UTF-8 bytes.</exception>
    </member>
    <member name="M:System.Text.Json.JsonEncodedText.Encode(System.ReadOnlySpan{System.Char},System.Text.Encodings.Web.JavaScriptEncoder)">
      <summary>Encodes a specified text value as a JSON string.</summary>
      <param name="value">The value to convert to JSON encoded text.</param>
      <param name="encoder">The encoder to use when escaping the string, or <see langword="null" /> to use the default encoder.</param>
      <returns>The encoded JSON text.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is too large.
-or-
<paramref name="value" /> contains invalid UTF-16 characters.</exception>
    </member>
    <member name="M:System.Text.Json.JsonEncodedText.Encode(System.String,System.Text.Encodings.Web.JavaScriptEncoder)">
      <summary>Encodes the string text value as a JSON string.</summary>
      <param name="value">The value to convert to JSON encoded text.</param>
      <param name="encoder">The encoder to use when escaping the string, or <see langword="null" /> to use the default encoder.</param>
      <returns>The encoded JSON text.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is too large.
-or-
<paramref name="value" /> contains invalid UTF-16 characters.</exception>
    </member>
    <member name="P:System.Text.Json.JsonEncodedText.EncodedUtf8Bytes">
      <summary>Gets the UTF-8 encoded representation of the pre-encoded JSON text.</summary>
      <returns>The UTF-8 encoded representation of the pre-encoded JSON text.</returns>
    </member>
    <member name="M:System.Text.Json.JsonEncodedText.Equals(System.Object)">
      <summary>Determines whether this instance and a specified object, which must also be a <see cref="T:System.Text.Json.JsonEncodedText" /> instance, have the same value.</summary>
      <param name="obj">The object to compare to this instance.</param>
      <returns>
        <see langword="true" /> if the current instance and <paramref name="obj" /> are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonEncodedText.Equals(System.Text.Json.JsonEncodedText)">
      <summary>Determines whether this instance and another specified <see cref="T:System.Text.Json.JsonEncodedText" /> instance have the same value.</summary>
      <param name="other">The object to compare to this instance.</param>
      <returns>
        <see langword="true" /> if this instance and <paramref name="other" /> have the same value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonEncodedText.GetHashCode">
      <summary>Returns the hash code for this <see cref="T:System.Text.Json.JsonEncodedText" />.</summary>
      <returns>The hash code for this instance.</returns>
    </member>
    <member name="M:System.Text.Json.JsonEncodedText.ToString">
      <summary>Converts the value of this instance to a <see cref="T:System.String" />.</summary>
      <returns>The underlying UTF-16 encoded string.</returns>
    </member>
    <member name="T:System.Text.Json.JsonException">
      <summary>Defines a custom exception object that is thrown when invalid JSON text is encountered, when the defined maximum depth is passed, or the JSON text is not compatible with the type of a property on an object.</summary>
    </member>
    <member name="M:System.Text.Json.JsonException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.JsonException" /> class.</summary>
    </member>
    <member name="M:System.Text.Json.JsonException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Creates a new exception object with serialized data.</summary>
      <param name="info">The serialized object data about the exception being thrown.</param>
      <param name="context">An object that contains contextual information about the source or destination.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.JsonException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.JsonException" /> class with a specified error message.</summary>
      <param name="message">The context-specific error message.</param>
    </member>
    <member name="M:System.Text.Json.JsonException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.JsonException" /> class, with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The context-specific error message.</param>
      <param name="innerException">The exception that caused the current exception.</param>
    </member>
    <member name="M:System.Text.Json.JsonException.#ctor(System.String,System.String,System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>Creates a new exception object to relay error information to the user.</summary>
      <param name="message">The context-specific error message.</param>
      <param name="path">The path where the invalid JSON was encountered.</param>
      <param name="lineNumber">The line number (starting at 0) at which the invalid JSON was encountered when deserializing.</param>
      <param name="bytePositionInLine">The byte count within the current line (starting at 0) where the invalid JSON was encountered.</param>
    </member>
    <member name="M:System.Text.Json.JsonException.#ctor(System.String,System.String,System.Nullable{System.Int64},System.Nullable{System.Int64},System.Exception)">
      <summary>Creates a new exception object to relay error information to the user that includes a specified inner exception.</summary>
      <param name="message">The context-specific error message.</param>
      <param name="path">The path where the invalid JSON was encountered.</param>
      <param name="lineNumber">The line number (starting at 0) at which the invalid JSON was encountered when deserializing.</param>
      <param name="bytePositionInLine">The byte count (starting at 0) within the current line where the invalid JSON was encountered.</param>
      <param name="innerException">The exception that caused the current exception.</param>
    </member>
    <member name="P:System.Text.Json.JsonException.BytePositionInLine">
      <summary>Gets the zero-based number of bytes read within the current line before the exception.</summary>
      <returns>The zero-based number of bytes read within the current line before the exception.</returns>
    </member>
    <member name="M:System.Text.Json.JsonException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with information about the exception.</summary>
      <param name="info">The serialized object data about the exception being thrown.</param>
      <param name="context">An object that contains contextual information about the source or destination.</param>
    </member>
    <member name="P:System.Text.Json.JsonException.LineNumber">
      <summary>Gets the zero-based number of lines read before the exception.</summary>
      <returns>The zero-based number of lines read before the exception.</returns>
    </member>
    <member name="P:System.Text.Json.JsonException.Message">
      <summary>Gets a message that describes the current exception.</summary>
      <returns>The error message that describes the current exception.</returns>
    </member>
    <member name="P:System.Text.Json.JsonException.Path">
      <summary>Gets The path within the JSON where the exception was encountered.</summary>
      <returns>The path within the JSON where the exception was encountered.</returns>
    </member>
    <member name="T:System.Text.Json.JsonNamingPolicy">
      <summary>Determines the naming policy used to convert a string-based name to another format, such as a camel-casing format.</summary>
    </member>
    <member name="M:System.Text.Json.JsonNamingPolicy.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Text.Json.JsonNamingPolicy" />.</summary>
    </member>
    <member name="P:System.Text.Json.JsonNamingPolicy.CamelCase">
      <summary>Gets the naming policy for camel-casing.</summary>
      <returns>The naming policy for camel-casing.</returns>
    </member>
    <member name="M:System.Text.Json.JsonNamingPolicy.ConvertName(System.String)">
      <summary>When overridden in a derived class, converts the specified name according to the policy.</summary>
      <param name="name">The name to convert.</param>
      <returns>The converted name.</returns>
    </member>
    <member name="T:System.Text.Json.JsonProperty">
      <summary>Represents a single property for a JSON object.</summary>
    </member>
    <member name="P:System.Text.Json.JsonProperty.Name">
      <summary>Gets the name of this property.</summary>
      <returns>The name of this property.</returns>
    </member>
    <member name="M:System.Text.Json.JsonProperty.NameEquals(System.ReadOnlySpan{System.Byte})">
      <summary>Compares the specified UTF-8 encoded text to the name of this property.</summary>
      <param name="utf8Text">The UTF-8 encoded text to compare against.</param>
      <returns>
        <see langword="true" /> if the name of this property has the same UTF-8 encoding as <paramref name="utf8Text" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="T:System.Type" /> is not <see cref="F:System.Text.Json.JsonTokenType.PropertyName" />.</exception>
    </member>
    <member name="M:System.Text.Json.JsonProperty.NameEquals(System.ReadOnlySpan{System.Char})">
      <summary>Compares the specified text as a character span to the name of this property.</summary>
      <param name="text">The text to compare against.</param>
      <returns>
        <see langword="true" /> if the name of this property matches <paramref name="text" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="T:System.Type" /> is not <see cref="F:System.Text.Json.JsonTokenType.PropertyName" />.</exception>
    </member>
    <member name="M:System.Text.Json.JsonProperty.NameEquals(System.String)">
      <summary>Compares the specified string to the name of this property.</summary>
      <param name="text">The text to compare against.</param>
      <returns>
        <see langword="true" /> if the name of this property matches <paramref name="text" />; otherwise <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="T:System.Type" /> is not <see cref="F:System.Text.Json.JsonTokenType.PropertyName" />.</exception>
    </member>
    <member name="M:System.Text.Json.JsonProperty.ToString">
      <summary>Provides a string representation of the property for debugging purposes.</summary>
      <returns>A string containing the uninterpreted value of the property, beginning at the declaring open-quote and ending at the last character that is part of the value.</returns>
    </member>
    <member name="P:System.Text.Json.JsonProperty.Value">
      <summary>Gets the value of this property.</summary>
      <returns>The value of this property.</returns>
    </member>
    <member name="M:System.Text.Json.JsonProperty.WriteTo(System.Text.Json.Utf8JsonWriter)">
      <summary>Writes the property to the provided writer as a named JSON object property.</summary>
      <param name="writer">The writer to which to write the property.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Text.Json.JsonProperty.Name" /> is too large to be a JSON object property.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Text.Json.JsonElement.ValueKind" /> of this JSON property's <see cref="P:System.Text.Json.JsonProperty.Value" /> would result in invalid JSON.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="T:System.Text.Json.JsonReaderOptions">
      <summary>Provides the ability for the user to define custom behavior when reading JSON.</summary>
    </member>
    <member name="P:System.Text.Json.JsonReaderOptions.AllowTrailingCommas">
      <summary>Gets or sets a value that defines whether an extra comma at the end of a list of JSON values in an object or array is allowed (and ignored) within the JSON payload being read.</summary>
      <returns>
        <see langword="true" /> if an extra comma is allowed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Text.Json.JsonReaderOptions.CommentHandling">
      <summary>Gets or sets a value that determines how the <see cref="T:System.Text.Json.Utf8JsonReader" /> handles comments when reading through the JSON data.</summary>
      <returns>One of the enumeration values that indicates how comments are handled.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The property is being set to a value that is not a member of the <see cref="T:System.Text.Json.JsonCommentHandling" /> enumeration.</exception>
    </member>
    <member name="P:System.Text.Json.JsonReaderOptions.MaxDepth">
      <summary>Gets or sets the maximum depth allowed when reading JSON, with the default (that is, 0) indicating a maximum depth of 64.</summary>
      <returns>The maximum depth allowed when reading JSON.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The maximum depth is being set to a negative value.</exception>
    </member>
    <member name="T:System.Text.Json.JsonReaderState">
      <summary>Defines an opaque type that holds and saves all the relevant state information, which must be provided to the <see cref="T:System.Text.Json.Utf8JsonReader" /> to continue reading after processing incomplete data.</summary>
    </member>
    <member name="M:System.Text.Json.JsonReaderState.#ctor(System.Text.Json.JsonReaderOptions)">
      <summary>Constructs a new <see cref="T:System.Text.Json.JsonReaderState" /> instance.</summary>
      <param name="options">Defines the customized behavior of the <see cref="T:System.Text.Json.Utf8JsonReader" /> that is different from the JSON RFC (for example how to handle comments, or the maximum depth allowed when reading). By default, the <see cref="T:System.Text.Json.Utf8JsonReader" /> follows the JSON RFC strictly (comments within the JSON are invalid) and reads up to a maximum depth of 64.</param>
      <exception cref="T:System.ArgumentException">The maximum depth is set to a non-positive value (&lt; 0).</exception>
    </member>
    <member name="P:System.Text.Json.JsonReaderState.Options">
      <summary>Gets the custom behavior to use when reading JSON data using the <see cref="T:System.Text.Json.Utf8JsonReader" /> struct that may deviate from strict adherence to the JSON specification, which is the default behavior.</summary>
      <returns>The custom behavior to use when reading JSON data.</returns>
    </member>
    <member name="T:System.Text.Json.JsonSerializer">
      <summary>Provides functionality to serialize objects or value types to JSON and to deserialize JSON into objects or value types.</summary>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.ReadOnlySpan{System.Byte},System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Parses the UTF-8 encoded text representing a single JSON value into an instance of a specified type.</summary>
      <param name="utf8Json">The JSON text to parse.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="options">Options to control the behavior during parsing.</param>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="returnType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.
-or-
<typeparamref name="returnType" /> is not compatible with the JSON.
-or-
There is remaining data in the span beyond a single JSON value.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.String,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Parses the text representing a single JSON value into an instance of a specified type.</summary>
      <param name="json">The JSON text to parse.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="options">Options to control the behavior during parsing.</param>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="json" /> or <paramref name="returnType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.
-or-
<typeparamref name="TValue" /> is not compatible with the JSON.
-or-
There is remaining data in the string beyond a single JSON value.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.Text.Json.Utf8JsonReader@,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Reads one JSON value (including objects or arrays) from the provided reader and converts it into an instance of  a specified type.</summary>
      <param name="reader">The reader to read the JSON from.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="options">Options to control the serializer behavior during reading.</param>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="returnType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.
-or-
<typeparamref name="returnType" /> is not compatible with the JSON.
-or-
A value could not be read from the reader.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> is using unsupported options.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.ReadOnlySpan{System.Byte},System.Text.Json.JsonSerializerOptions)">
      <summary>Parses the UTF-8 encoded text representing a single JSON value into an instance of the type specified by a generic type parameter.</summary>
      <param name="utf8Json">The JSON text to parse.</param>
      <param name="options">Options to control the behavior during parsing.</param>
      <typeparam name="TValue">The target type of the UTF-8 encoded text.</typeparam>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.
-or-
<typeparamref name="TValue" /> is not compatible with the JSON.
-or-
There is remaining data in the span beyond a single JSON value.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.String,System.Text.Json.JsonSerializerOptions)">
      <summary>Parses the text representing a single JSON value into an instance of the type specified by a generic type parameter.</summary>
      <param name="json">The JSON text to parse.</param>
      <param name="options">Options to control the behavior during parsing.</param>
      <typeparam name="TValue">The target type of the JSON value.</typeparam>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="json" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.
-or-
<typeparamref name="TValue" /> is not compatible with the JSON.
-or-
There is remaining data in the string beyond a single JSON value.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.Text.Json.Utf8JsonReader@,System.Text.Json.JsonSerializerOptions)">
      <summary>Reads one JSON value (including objects or arrays) from the provided reader into an instance of the type specified by a generic type parameter.</summary>
      <param name="reader">The reader to read the JSON from.</param>
      <param name="options">Options to control serializer behavior during reading.</param>
      <typeparam name="TValue">The target type of the JSON value.</typeparam>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.
-or-
<typeparamref name="TValue" /> is not compatible with the JSON.
-or-
A value could not be read from the reader.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> uses unsupported options.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.DeserializeAsync(System.IO.Stream,System.Type,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Asynchronously reads the UTF-8 encoded text representing a single JSON value into an instance of a specified type. The stream will be read to completion.</summary>
      <param name="utf8Json">The JSON data to parse.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="options">Options to control the behavior during reading.</param>
      <param name="cancellationToken">A cancellation token that may be used to cancel the read operation.</param>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> or <paramref name="returnType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.
-or-
<typeparamref name="TValue" /> is not compatible with the JSON.
-or-
There is remaining data in the stream.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.DeserializeAsync``1(System.IO.Stream,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Asynchronously reads the UTF-8 encoded text representing a single JSON value into an instance of a type specified by a generic type parameter. The stream will be read to completion.</summary>
      <param name="utf8Json">The JSON data to parse.</param>
      <param name="options">Options to control the behavior during reading.</param>
      <param name="cancellationToken">A token that may be used to cancel the read operation.</param>
      <typeparam name="TValue">The target type of the JSON value.</typeparam>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.
-or-
<typeparamref name="TValue" /> is not compatible with the JSON.
-or-
There is remaining data in the stream.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Serialize(System.Object,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the value of a specified type into a JSON string.</summary>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="options">Options to control the conversion behavior.</param>
      <returns>The JSON string representation of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Serialize(System.Text.Json.Utf8JsonWriter,System.Object,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Writes the JSON representation of the specified type to the provided writer.</summary>
      <param name="writer">The JSON writer to write to.</param>
      <param name="value">The value to convert and write.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="options">Options to control serialization behavior.</param>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Serialize``1(``0,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the value of a type specified by a generic type parameter into a JSON string.</summary>
      <param name="value">The value to convert.</param>
      <param name="options">Options to control serialization behavior.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <returns>A JSON string representation of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Serialize``1(System.Text.Json.Utf8JsonWriter,``0,System.Text.Json.JsonSerializerOptions)">
      <summary>Writes the JSON representation of a type specified by a generic type parameter to the provided writer.</summary>
      <param name="writer">A JSON writer to write to.</param>
      <param name="value">The value to convert and write.</param>
      <param name="options">Options to control serialization behavior.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeAsync(System.IO.Stream,System.Object,System.Type,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Asynchronously converts the value of a specified type to UTF-8 encoded JSON text and writes it to the specified stream.</summary>
      <param name="utf8Json">The UTF-8 stream to write to.</param>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="options">Options to control serialization behavior.</param>
      <param name="cancellationToken">A token that may be used to cancel the write operation.</param>
      <returns>A task that represents the asynchronous write operation.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeAsync``1(System.IO.Stream,``0,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Asynchronously converts a value of a type specified by a generic type parameter to UTF-8 encoded JSON text and writes it to a stream.</summary>
      <param name="utf8Json">The UTF-8 stream to write to.</param>
      <param name="value">The value to convert.</param>
      <param name="options">Options to control serialization behavior.</param>
      <param name="cancellationToken">A token that may be used to cancel the write operation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <returns>A task that represents the asynchronous write operation.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(System.Object,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts a value of the specified type into a JSON string, encoded as UTF-8 bytes.</summary>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="options">Options to control the conversion behavior.</param>
      <returns>A JSON string representation of the value, encoded as UTF-8 bytes.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToUtf8Bytes``1(``0,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the value of a type specified by a generic type parameter into a JSON string, encoded as UTF-8 bytes.</summary>
      <param name="value">The value to convert.</param>
      <param name="options">Options to control the conversion behavior.</param>
      <typeparam name="TValue">The type of the value.</typeparam>
      <returns>A JSON string representation of the value, encoded as UTF-8 bytes.</returns>
    </member>
    <member name="T:System.Text.Json.JsonSerializerOptions">
      <summary>Provides options to be used with <see cref="T:System.Text.Json.JsonSerializer" />.</summary>
    </member>
    <member name="M:System.Text.Json.JsonSerializerOptions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.JsonSerializerOptions" /> class.</summary>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.AllowTrailingCommas">
      <summary>Get or sets a value that indicates whether an extra comma at the end of a list of JSON values in an object or array is allowed (and ignored) within the JSON payload being deserialized.</summary>
      <returns>
        <see langword="true" /> if an extra comma at the end of a list of JSON values in an object or array is allowed (and ignored); <see langword="false" /> otherwise.</returns>
      <exception cref="T:System.InvalidOperationException">This property was set after serialization or deserialization has occurred.</exception>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.Converters">
      <summary>Gets the list of user-defined converters that were registered.</summary>
      <returns>The list of custom converters.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.DefaultBufferSize">
      <summary>Gets or sets the default buffer size, in bytes, to use when creating temporary buffers.</summary>
      <returns>The default buffer size in bytes.</returns>
      <exception cref="T:System.ArgumentException">The buffer size is less than 1.</exception>
      <exception cref="T:System.InvalidOperationException">This property was set after serialization or deserialization has occurred.</exception>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.DictionaryKeyPolicy">
      <summary>Gets or sets the policy used to convert a <see cref="T:System.Collections.IDictionary" /> key's name to another format, such as camel-casing.</summary>
      <returns>The policy used to convert a <see cref="T:System.Collections.IDictionary" /> key's name to another format.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.Encoder">
      <summary>Gets or sets the encoder to use when escaping strings, or <see langword="null" /> to use the default encoder.</summary>
      <returns>The JavaScript character encoding.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializerOptions.GetConverter(System.Type)">
      <summary>Returns the converter for the specified type.</summary>
      <param name="typeToConvert">The type to return a converter for.</param>
      <returns>The first converter that supports the given type, or <see langword="null" /> if there is no converter.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.IgnoreNullValues">
      <summary>Gets or sets a value that determines whether <see langword="null" /> values are ignored during serialization and deserialization. The default value is <see langword="false" />.</summary>
      <returns>
        <see langword="true" /> to ignore null values during serialization and deserialization; otherwise, see langword="false" /&gt;.</returns>
      <exception cref="T:System.InvalidOperationException">This property was set after serialization or deserialization has occurred.</exception>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.IgnoreReadOnlyProperties">
      <summary>Gets a value that determines whether read-only properties are ignored during serialization. The default value is <see langword="false" />.</summary>
      <returns>
        <see langword="true" /> to ignore read-only properties during serialization; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This property was set after serialization or deserialization has occurred.</exception>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.MaxDepth">
      <summary>Gets or sets the maximum depth allowed when serializing or deserializing JSON, with the default value of 0 indicating a maximum depth of 64.</summary>
      <returns>The maximum depth allowed when serializing or deserializing JSON.</returns>
      <exception cref="T:System.InvalidOperationException">This property was set after serialization or deserialization has occurred.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The max depth is set to a negative value.</exception>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.PropertyNameCaseInsensitive">
      <summary>Gets or sets a value that determines whether a property's name uses a case-insensitive comparison during deserialization. The default value is <see langword="false" />.</summary>
      <returns>
        <see langword="true" /> to compare property names using case-insensitive comparison; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.PropertyNamingPolicy">
      <summary>Gets or sets a value that specifies the policy used to convert a property's name on an object to another format, such as camel-casing, or <see langword="null" /> to leave property names unchanged.</summary>
      <returns>A property naming policy, or <see langword="null" /> to leave property names unchanged.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.ReadCommentHandling">
      <summary>Gets or sets a value that defines how comments are handled during deserialization.</summary>
      <returns>A value that indicates whether comments are allowed, disallowed, or skipped.</returns>
      <exception cref="T:System.InvalidOperationException">This property was set after serialization or deserialization has occurred.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The comment handling enum is set to a value that is not supported (or not within the <see cref="T:System.Text.Json.JsonCommentHandling" /> enum range).</exception>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.WriteIndented">
      <summary>Gets or sets a value that defines whether JSON should use pretty printing. By default, JSON is serialized without any extra white space.</summary>
      <returns>
        <see langword="true" /> if JSON should pretty print on serialization; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">This property was set after serialization or deserialization has occurred.</exception>
    </member>
    <member name="T:System.Text.Json.JsonTokenType">
      <summary>Defines the various JSON tokens that make up a JSON text.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.Comment">
      <summary>The token type is a comment string.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.EndArray">
      <summary>The token type is the end of a JSON array.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.EndObject">
      <summary>The token type is the end of a JSON object.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.False">
      <summary>The token type is the JSON literal false.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.None">
      <summary>There is no value (as distinct from <see cref="F:System.Text.Json.JsonTokenType.Null" />).</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.Null">
      <summary>The token type is the JSON literal null.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.Number">
      <summary>The token type is a JSON number.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.PropertyName">
      <summary>The token type is a JSON property name.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.StartArray">
      <summary>The token type is the start of a JSON array.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.StartObject">
      <summary>The token type is the start of a JSON object.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.String">
      <summary>The token type is a JSON string.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.True">
      <summary>The token type is the JSON literal true.</summary>
    </member>
    <member name="T:System.Text.Json.JsonValueKind">
      <summary>Specifies the data type of a JSON value.</summary>
    </member>
    <member name="F:System.Text.Json.JsonValueKind.Array">
      <summary>A JSON array.</summary>
    </member>
    <member name="F:System.Text.Json.JsonValueKind.False">
      <summary>The JSON value false.</summary>
    </member>
    <member name="F:System.Text.Json.JsonValueKind.Null">
      <summary>The JSON value null.</summary>
    </member>
    <member name="F:System.Text.Json.JsonValueKind.Number">
      <summary>A JSON number.</summary>
    </member>
    <member name="F:System.Text.Json.JsonValueKind.Object">
      <summary>A JSON object.</summary>
    </member>
    <member name="F:System.Text.Json.JsonValueKind.String">
      <summary>A JSON string.</summary>
    </member>
    <member name="F:System.Text.Json.JsonValueKind.True">
      <summary>The JSON value true.</summary>
    </member>
    <member name="F:System.Text.Json.JsonValueKind.Undefined">
      <summary>There is no value (as distinct from <see cref="F:System.Text.Json.JsonValueKind.Null" />).</summary>
    </member>
    <member name="T:System.Text.Json.JsonWriterOptions">
      <summary>Allows the user to define custom behavior when writing JSON using the <see cref="T:System.Text.Json.Utf8JsonWriter" />.</summary>
    </member>
    <member name="P:System.Text.Json.JsonWriterOptions.Encoder">
      <summary>Gets or sets the encoder to use when escaping strings, or <see langword="null" /> to use the default encoder.</summary>
      <returns>The JavaScript character encoder used to override the escaping behavior.</returns>
    </member>
    <member name="P:System.Text.Json.JsonWriterOptions.Indented">
      <summary>Gets or sets a value that indicates whether the <see cref="T:System.Text.Json.Utf8JsonWriter" /> should format the JSON output, which includes indenting nested JSON tokens, adding new lines, and adding white space between property names and values.</summary>
      <returns>
        <see langword="true" /> to format the JSON output; <see langword="false" /> to write without any extra white space. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Text.Json.JsonWriterOptions.SkipValidation">
      <summary>Gets or sets a value that indicates whether the <see cref="T:System.Text.Json.Utf8JsonWriter" /> should skip structural validation and allow the user to write invalid JSON.</summary>
      <returns>
        <see langword="true" /> to skip structural validation and allow invalid JSON; <see langword="false" /> to throw an <see cref="T:System.InvalidOperationException" /> on any attempt to write invalid JSON.</returns>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonAttribute">
      <summary>Provides the base class for serialization attributes.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonAttribute.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Text.Json.Serialization.JsonAttribute" />.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonConverter">
      <summary>Converts an object or value to or from JSON.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverter.CanConvert(System.Type)">
      <summary>When overridden in a derived class, determines whether the converter instance can convert the specified object type.</summary>
      <param name="typeToConvert">The type of the object to check whether it can be converted by this converter instance.</param>
      <returns>
        <see langword="true" /> if the instance can convert the specified object type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonConverter`1">
      <summary>Converts an object or value to or from JSON.</summary>
      <typeparam name="T">The type of object or value handled by the converter.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverter`1.#ctor">
      <summary>Initializes a new <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverter`1.CanConvert(System.Type)">
      <summary>Determines whether the specified type can be converted.</summary>
      <param name="typeToConvert">The type to compare against.</param>
      <returns>
        <see langword="true" /> if the type can be converted; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverter`1.Read(System.Text.Json.Utf8JsonReader@,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Reads and converts the JSON to type <typeparamref name="T" />.</summary>
      <param name="reader">The reader.</param>
      <param name="typeToConvert">The type to convert.</param>
      <param name="options">An object that specifies serialization options to use.</param>
      <returns>The converted value.</returns>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverter`1.Write(System.Text.Json.Utf8JsonWriter,`0,System.Text.Json.JsonSerializerOptions)">
      <summary>Writes a specified value as JSON.</summary>
      <param name="writer">The writer to write to.</param>
      <param name="value">The value to convert to JSON.</param>
      <param name="options">An object that specifies serialization options to use.</param>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonConverterAttribute">
      <summary>When placed on a property or type, specifies the converter type to use.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverterAttribute.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Text.Json.Serialization.JsonConverterAttribute" />.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverterAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of <see cref="T:System.Text.Json.Serialization.JsonConverterAttribute" /> with the specified converter type.</summary>
      <param name="converterType">The type of the converter.</param>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonConverterAttribute.ConverterType">
      <summary>Gets the type of the <see cref="T:System.Text.Json.Serialization.JsonConverterAttribute" />, or <see langword="null" /> if it was created without a type.</summary>
      <returns>The type of the <see cref="T:System.Text.Json.Serialization.JsonConverterAttribute" />, or <see langword="null" /> if it was created without a type.</returns>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverterAttribute.CreateConverter(System.Type)">
      <summary>When overridden in a derived class and <see cref="P:System.Text.Json.Serialization.JsonConverterAttribute.ConverterType" /> is <see langword="null" />, allows the derived class to create a <see cref="T:System.Text.Json.Serialization.JsonConverter" /> in order to pass additional state.</summary>
      <param name="typeToConvert">The type of the converter.</param>
      <returns>The custom converter.</returns>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonConverterFactory">
      <summary>Supports converting several types by using a factory pattern.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverterFactory.#ctor">
      <summary>When overidden in a derived class, initializes a new instance of the <see cref="T:System.Text.Json.Serialization.JsonConverterFactory" /> class.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverterFactory.CreateConverter(System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Creates a converter for a specified type.</summary>
      <param name="typeToConvert">The type handled by the converter.</param>
      <param name="options">The serialization options to use.</param>
      <returns>A converter for which <typeparamref name="T" /> is compatible with <paramref name="typeToConvert" />.</returns>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonExtensionDataAttribute">
      <summary>When placed on a property of type <see cref="T:System.Collections.Generic.IDictionary`2" />, any properties that do not have a matching member are added to that dictionary during deserialization and written during serialization.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonExtensionDataAttribute.#ctor">
      <summary>Instantiates a new instance of the <see cref="T:System.Text.Json.Serialization.JsonExtensionDataAttribute" /> class.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonIgnoreAttribute">
      <summary>Prevents a property from being serialized or deserialized.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonIgnoreAttribute.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Text.Json.Serialization.JsonIgnoreAttribute" />.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonPropertyNameAttribute">
      <summary>Specifies the property name that is present in the JSON when serializing and deserializing. This overrides any naming policy specified by <see cref="T:System.Text.Json.JsonNamingPolicy" />.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonPropertyNameAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of <see cref="T:System.Text.Json.Serialization.JsonPropertyNameAttribute" /> with the specified property name.</summary>
      <param name="name">The name of the property.</param>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonPropertyNameAttribute.Name">
      <summary>Gets the name of the property.</summary>
      <returns>The name of the property.</returns>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonStringEnumConverter">
      <summary>Converts enumeration values to and from strings.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonStringEnumConverter.#ctor">
      <summary>Initializes an instance of the <see cref="T:System.Text.Json.Serialization.JsonStringEnumConverter" /> class with the default naming policy that allows integer values.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonStringEnumConverter.#ctor(System.Text.Json.JsonNamingPolicy,System.Boolean)">
      <summary>Initializes an instance of the <see cref="T:System.Text.Json.Serialization.JsonStringEnumConverter" /> class with a specified naming policy and a value that indicates whether undefined enumeration values are allowed.</summary>
      <param name="namingPolicy">The optional naming policy for writing enum values.</param>
      <param name="allowIntegerValues">
        <see langword="true" /> to allow undefined enum values; otherwise, <see langword="false" />. When <see langword="true" />, if an enum value isn't defined, it will output as a number rather than a string.</param>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonStringEnumConverter.CanConvert(System.Type)">
      <summary>Determines whether the specified type can be converted to an enum.</summary>
      <param name="typeToConvert">The type to be checked.</param>
      <returns>
        <see langword="true" /> if the type can be converted; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonStringEnumConverter.CreateConverter(System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Creates a converter for the specified type.</summary>
      <param name="typeToConvert">The type handled by the converter.</param>
      <param name="options">The serialization options to use.</param>
      <returns>A converter for which <typeparamref name="T" /> is compatible with <paramref name="typeToConvert" />.</returns>
    </member>
    <member name="T:System.Text.Json.Utf8JsonReader">
      <summary>Provides a high-performance API for forward-only, read-only access to UTF-8 encoded JSON text.</summary>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.#ctor(System.Buffers.ReadOnlySequence{System.Byte},System.Boolean,System.Text.Json.JsonReaderState)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Utf8JsonReader" /> structure that processes a read-only sequence of UTF-8 encoded text and indicates whether the input contains all the text to process.</summary>
      <param name="jsonData">The UTF-8 encoded JSON text to process.</param>
      <param name="isFinalBlock">
        <see langword="true" /> to indicate that the input sequence contains the entire data to process; <see langword="false" /> to indicate that the input span contains partial data with more data to follow.</param>
      <param name="state">An object that contains the reader state. If this is the first call to the constructor, pass the default state; otherwise, pass the value of the <see cref="P:System.Text.Json.Utf8JsonReader.CurrentState" /> property from the previous instance of the <see cref="T:System.Text.Json.Utf8JsonReader" />.</param>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.#ctor(System.Buffers.ReadOnlySequence{System.Byte},System.Text.Json.JsonReaderOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Utf8JsonReader" /> structure that processes a read-only sequence of UTF-8 encoded text using the specified options.</summary>
      <param name="jsonData">The UTF-8 encoded JSON text to process.</param>
      <param name="options">Defines customized behavior of the <see cref="T:System.Text.Json.Utf8JsonReader" /> that differs from the JSON RFC (for example how to handle comments or maximum depth allowed when reading). By default, the <see cref="T:System.Text.Json.Utf8JsonReader" /> follows the JSON RFC strictly; comments within the JSON are invalid, and the maximum depth is 64.</param>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.#ctor(System.ReadOnlySpan{System.Byte},System.Boolean,System.Text.Json.JsonReaderState)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Utf8JsonReader" /> structure that processes a read-only span of UTF-8 encoded text and indicates whether the input contains all the text to process.</summary>
      <param name="jsonData">The UTF-8 encoded JSON text to process.</param>
      <param name="isFinalBlock">
        <see langword="true" /> to indicate that the input sequence contains the entire data to process; <see langword="false" /> to indicate that the input span contains partial data with more data to follow.</param>
      <param name="state">An object that contains the reader state. If this is the first call to the constructor, pass the default state; otherwise, pass the value of the <see cref="P:System.Text.Json.Utf8JsonReader.CurrentState" /> property from the previous instance of the <see cref="T:System.Text.Json.Utf8JsonReader" />.</param>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.#ctor(System.ReadOnlySpan{System.Byte},System.Text.Json.JsonReaderOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Utf8JsonReader" /> structure that processes a read-only span of UTF-8 encoded text using the specified options.</summary>
      <param name="jsonData">The UTF-8 encoded JSON text to process.</param>
      <param name="options">Defines customized behavior of the <see cref="T:System.Text.Json.Utf8JsonReader" /> that differs from the JSON RFC (for example how to handle comments or maximum depth allowed when reading). By default, the <see cref="T:System.Text.Json.Utf8JsonReader" /> follows the JSON RFC strictly; comments within the JSON are invalid, and the maximum depth is 64.</param>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.BytesConsumed">
      <summary>Gets the total number of bytes consumed so far by this instance of the <see cref="T:System.Text.Json.Utf8JsonReader" />.</summary>
      <returns>The total number of bytes consumed so far.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.CurrentDepth">
      <summary>Gets the depth of the current token.</summary>
      <returns>The depth of the current token.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.CurrentState">
      <summary>Gets the current <see cref="T:System.Text.Json.Utf8JsonReader" /> state to pass to a <see cref="T:System.Text.Json.Utf8JsonReader" /> constructor with more data.</summary>
      <returns>The current reader state.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetBoolean">
      <summary>Reads the next JSON token value from the source as a <see cref="T:System.Boolean" />.</summary>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Text.Json.Utf8JsonReader.TokenType" /> is <see cref="F:System.Text.Json.JsonTokenType.True" />; <see langword="false" /> if the <see cref="P:System.Text.Json.Utf8JsonReader.TokenType" /> is <see cref="F:System.Text.Json.JsonTokenType.False" />.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token isn't a boolean value (that is, <see cref="F:System.Text.Json.JsonTokenType.True" /> or <see cref="F:System.Text.Json.JsonTokenType.False" />).</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetByte">
      <summary>Parses the current JSON token value from the source as a <see cref="T:System.Byte" />.</summary>
      <returns>The value of the UTF-8 encoded token.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token is not a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The numeric format of the JSON token value is incorrect (for example, it contains a fractional value or is written in scientific notation).
-or-
The JSON token value represents a number less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetBytesFromBase64">
      <summary>Parses the current JSON token value from the source and decodes the Base64 encoded JSON string as a byte array.</summary>
      <returns>The byte array that represents the current JSON token value.</returns>
      <exception cref="T:System.InvalidOperationException">The type of the JSON token is not a <see cref="F:System.Text.Json.JsonTokenType.String" />.</exception>
      <exception cref="T:System.FormatException">The value is not encoded as Base64 text, so it can't be decoded to bytes.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetComment">
      <summary>Parses the current JSON token value from the source as a comment, transcoded it as a <see cref="T:System.String" />.</summary>
      <returns>The comment that represents the current JSON token value.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token is not a comment.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetDateTime">
      <summary>Reads the next JSON token value from the source and parses it to a <see cref="T:System.DateTime" />.</summary>
      <returns>The date and time value, if the entire UTF-8 encoded token value can be successfully parsed.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token isn't a <see cref="F:System.Text.Json.JsonTokenType.String" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value cannot be read as a <see cref="T:System.DateTime" />.
-or-
The entire UTF-8 encoded token value cannot be parsed to a <see cref="T:System.DateTime" /> value.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetDateTimeOffset">
      <summary>Reads the next JSON token value from the source and parses it to a <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>The date and time offset, if the entire UTF-8 encoded token value can be successfully parsed.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token isn't a <see cref="F:System.Text.Json.JsonTokenType.String" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value cannot be read as a <see cref="T:System.DateTimeOffset" />.
-or-
The entire UTF-8 encoded token value cannot be parsed to a <see cref="T:System.DateTimeOffset" /> value.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetDecimal">
      <summary>Reads the next JSON token value from the source and parses it to a <see cref="T:System.Decimal" />.</summary>
      <returns>The UTF-8 encoded token value parsed to a <see cref="T:System.Decimal" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value represents a number less than <see cref="F:System.Decimal.MinValue" /> or greater than <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetDouble">
      <summary>Reads the next JSON token value from the source and parses it to a <see cref="T:System.Double" />.</summary>
      <returns>The UTF-8 encoded token value parsed to a <see cref="T:System.Double" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value represents a number less than <see cref="F:System.Double.MinValue" /> or greater than <see cref="F:System.Double.MaxValue" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetGuid">
      <summary>Reads the next JSON token value from the source and parses it to a <see cref="T:System.Guid" />.</summary>
      <returns>The GUID value, if the entire UTF-8 encoded token value can be successfully parsed.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token isn't a <see cref="F:System.Text.Json.JsonTokenType.String" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value is in an unsupported format for a Guid.
-or-
The entire UTF-8 encoded token value cannot be parsed to a <see cref="T:System.Guid" /> value.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetInt16">
      <summary>Parses the current JSON token value from the source as a <see cref="T:System.Int16" />.</summary>
      <returns>The UTF-8 encoded token value parsed to an <see cref="T:System.Int16" />.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token is not a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The numeric format of the JSON token value is incorrect (for example, it contains a fractional value or is written in scientific notation).
-or-
The JSON token value represents a number less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetInt32">
      <summary>Reads the next JSON token value from the source and parses it to an <see cref="T:System.Int32" />.</summary>
      <returns>The UTF-8 encoded token value parsed to an <see cref="T:System.Int32" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value is of the incorrect numeric format. For example, it contains a decimal or is written in scientific notation.
-or-
The JSON token value represents a number less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetInt64">
      <summary>Reads the next JSON token value from the source and parses it to an <see cref="T:System.Int64" />.</summary>
      <returns>The UTF-8 encoded token value parsed to an <see cref="T:System.Int64" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value is of the incorrect numeric format. For example, it contains a decimal or is written in scientific notation.
-or-
The JSON token value represents a number less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetSByte">
      <summary>Parses the current JSON token value from the source as an <see cref="T:System.SByte" />.</summary>
      <returns>The UTF-8 encoded token value parsed to an <see cref="T:System.SByte" />.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token is not a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The numeric format of the JSON token value is incorrect (for example, it contains a fractional value or is written in scientific notation).
-or-
The JSON token value represents a number less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetSingle">
      <summary>Reads the next JSON token value from the source and parses it to a <see cref="T:System.Single" />.</summary>
      <returns>The UTF-8 encoded token value parsed to a <see cref="T:System.Single" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value represents a number less than <see cref="F:System.Single.MinValue" /> or greater than <see cref="F:System.Single.MaxValue" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetString">
      <summary>Reads the next JSON token value from the source, unescaped, and transcoded as a string.</summary>
      <returns>The token value parsed to a string, or <see langword="null" /> if <see cref="P:System.Text.Json.Utf8JsonReader.TokenType" /> is <see cref="F:System.Text.Json.JsonTokenType.Null" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a string (that is, not a <see cref="F:System.Text.Json.JsonTokenType.String" />, <see cref="F:System.Text.Json.JsonTokenType.PropertyName" />, or <see cref="F:System.Text.Json.JsonTokenType.Null" />).
-or-
The JSON string contains invalid UTF-8 bytes or invalid UTF-16 surrogates.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetUInt16">
      <summary>Parses the current JSON token value from the source as a <see cref="T:System.UInt16" />.</summary>
      <returns>The UTF-8 encoded token value parsed to a <see cref="T:System.UInt16" />.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token is not a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The numeric format of the JSON token value is incorrect (for example, it contains a fractional value or is written in scientific notation).
-or-
The JSON token value represents a number less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetUInt32">
      <summary>Reads the next JSON token value from the source and parses it to a <see cref="T:System.UInt32" />.</summary>
      <returns>The UTF-8 encoded token value parsed to a <see cref="T:System.UInt32" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value is of the incorrect numeric format. For example, it contains a decimal or is written in scientific notation.
-or-
The JSON token value represents a number less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetUInt64">
      <summary>Reads the next JSON token value from the source and parses it to a <see cref="T:System.UInt64" />.</summary>
      <returns>The UTF-8 encoded token value parsed to a <see cref="T:System.UInt64" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value is of the incorrect numeric format. For example, it contains a decimal or is written in scientific notation.
-or-
The JSON token value represents a number less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />.</exception>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.HasValueSequence">
      <summary>Gets a value that indicates which <c>Value</c> property to use to get the token value.</summary>
      <returns>
        <see langword="true" /> if <see cref="P:System.Text.Json.Utf8JsonReader.ValueSequence" /> should be used to get the token value; <see langword="false" /> if <see cref="P:System.Text.Json.Utf8JsonReader.ValueSpan" /> should be used instead.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.IsFinalBlock">
      <summary>Gets the mode of this instance of the <see cref="T:System.Text.Json.Utf8JsonReader" /> which indicates whether all the JSON data was provided or there is more data to come.</summary>
      <returns>
        <see langword="true" /> if the reader was constructed with the input span or sequence containing the entire JSON data to process; <see langword="false" /> if the reader was constructed with an input span or sequence that may contain partial JSON data with more data to follow.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.Position">
      <summary>Gets the current <see cref="T:System.SequencePosition" /> within the provided UTF-8 encoded input ReadOnlySequence&lt;byte&gt; or a default <see cref="T:System.SequencePosition" /> if the <see cref="T:System.Text.Json.Utf8JsonReader" /> struct was constructed with a ReadOnlySpan&lt;byte&gt;.</summary>
      <returns>The current <see cref="T:System.SequencePosition" /> within the provided UTF-8 encoded input ReadOnlySequence&lt;byte&gt; or a default <see cref="T:System.SequencePosition" /> if the <see cref="T:System.Text.Json.Utf8JsonReader" /> struct was constructed with a ReadOnlySpan&lt;byte&gt;.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.Read">
      <summary>Reads the next JSON token from the input source.</summary>
      <returns>
        <see langword="true" /> if the token was read successfully; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.Text.Json.JsonException">An invalid JSON token according to the JSON RFC is encountered.
-or-
The current depth exceeds the recursive limit set by the maximum depth.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.Skip">
      <summary>Skips the children of the current JSON token.</summary>
      <exception cref="T:System.InvalidOperationException">The reader was given partial data with more data to follow (that is, <see cref="P:System.Text.Json.Utf8JsonReader.IsFinalBlock" /> is <see langword="false" />).</exception>
      <exception cref="T:System.Text.Json.JsonException">An invalid JSON token was encountered while skipping, according to the JSON RFC.
-or-
The current depth exceeds the recursive limit set by the maximum depth.</exception>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.TokenStartIndex">
      <summary>Gets the index that the last processed JSON token starts at (within the given UTF-8 encoded input text), skipping any white space.</summary>
      <returns>The starting index of the last processed JSON token within the given UTF-8 encoded input text.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.TokenType">
      <summary>Gets the type of the last processed JSON token in the UTF-8 encoded JSON text.</summary>
      <returns>The type of the last processed JSON token.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetByte(System.Byte@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.Byte" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.Byte" /> value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetBytesFromBase64(System.Byte[]@)">
      <summary>Tries to parse the current JSON token value from the source and decodes the Base64 encoded JSON string as a byte array and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the decoded binary representation of the Base64 text.</param>
      <returns>
        <see langword="true" /> if the entire token value is encoded as valid Base64 text and can be successfully decoded to bytes; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token is not a <see cref="F:System.Text.Json.JsonTokenType.String" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetDateTime(System.DateTime@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.DateTime" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.DateTime" /> value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token isn't a <see cref="F:System.Text.Json.JsonTokenType.String" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetDateTimeOffset(System.DateTimeOffset@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.DateTimeOffset" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.DateTimeOffset" /> value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token isn't a <see cref="F:System.Text.Json.JsonTokenType.String" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetDecimal(System.Decimal@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.Decimal" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.Decimal" /> value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetDouble(System.Double@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.Double" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.Double" /> value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetGuid(System.Guid@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.Guid" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.Guid" /> value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token isn't a <see cref="F:System.Text.Json.JsonTokenType.String" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetInt16(System.Int16@)">
      <summary>Tries to parse the current JSON token value from the source as an <see cref="T:System.Int16" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.Int16" /> value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetInt32(System.Int32@)">
      <summary>Tries to parse the current JSON token value from the source as an <see cref="T:System.Int32" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to an <see cref="T:System.Int32" /> value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetInt64(System.Int64@)">
      <summary>Tries to parse the current JSON token value from the source as an <see cref="T:System.Int64" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to an <see cref="T:System.Int64" /> value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetSByte(System.SByte@)">
      <summary>Tries to parse the current JSON token value from the source as an <see cref="T:System.SByte" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to an <see cref="T:System.SByte" /> value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetSingle(System.Single@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.Single" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to an <see cref="T:System.Single" /> value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetUInt16(System.UInt16@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.UInt16" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.UInt16" /> value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetUInt32(System.UInt32@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.UInt32" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.UInt32" /> value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetUInt64(System.UInt64@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.UInt64" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.UInt64" /> value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TrySkip">
      <summary>Tries to skip the children of the current JSON token.</summary>
      <returns>
        <see langword="true" /> if there was enough data for the children to be skipped successfully; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.Text.Json.JsonException">An invalid JSON token was encountered while skipping, according to the JSON RFC.
-or -
The current depth exceeds the recursive limit set by the maximum depth.</exception>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.ValueSequence">
      <summary>Gets the raw value of the last processed token as a ReadOnlySequence&lt;byte&gt; slice of the input payload, only if the token is contained within multiple segments.</summary>
      <returns>A byte read-only sequence.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.ValueSpan">
      <summary>Gets the raw value of the last processed token as a ReadOnlySpan&lt;byte&gt; slice of the input payload, if the token fits in a single segment or if the reader was constructed with a JSON payload contained in a ReadOnlySpan&lt;byte&gt;.</summary>
      <returns>A read-only span of bytes.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.ValueTextEquals(System.ReadOnlySpan{System.Byte})">
      <summary>Compares the UTF-8 encoded text in a read-only byte span to the unescaped JSON token value in the source and returns a value that indicates whether they match.</summary>
      <param name="utf8Text">The UTF-8 encoded text to compare against.</param>
      <returns>
        <see langword="true" /> if the JSON token value in the source matches the UTF-8 encoded lookup text; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token is not a JSON string (that is, it is not <see cref="F:System.Text.Json.JsonTokenType.String" /> or <see cref="F:System.Text.Json.JsonTokenType.PropertyName" />).</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.ValueTextEquals(System.ReadOnlySpan{System.Char})">
      <summary>Compares the text in a read-only character span to the unescaped JSON token value in the source and returns a value that indicates whether they match.</summary>
      <param name="text">The text to compare against.</param>
      <returns>
        <see langword="true" /> if the JSON token value in the source matches the lookup text; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token is not a JSON string (that is, it is not <see cref="F:System.Text.Json.JsonTokenType.String" /> or <see cref="F:System.Text.Json.JsonTokenType.PropertyName" />).</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.ValueTextEquals(System.String)">
      <summary>Compares the string text to the unescaped JSON token value in the source and returns a value that indicates whether they match.</summary>
      <param name="text">The text to compare against.</param>
      <returns>
        <see langword="true" /> if the JSON token value in the source matches the lookup text; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The JSON token is not a JSON string (that is, it is not <see cref="F:System.Text.Json.JsonTokenType.String" /> or <see cref="F:System.Text.Json.JsonTokenType.PropertyName" />).</exception>
    </member>
    <member name="T:System.Text.Json.Utf8JsonWriter">
      <summary>Provides a high-performance API for forward-only, non-cached writing of UTF-8 encoded JSON text.</summary>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.#ctor(System.Buffers.IBufferWriter{System.Byte},System.Text.Json.JsonWriterOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Utf8JsonWriter" /> class using the specified <see cref="T:System.Buffers.IBufferWriter`1" /> to write the output to and customization options.</summary>
      <param name="bufferWriter">The destination for writing JSON text.</param>
      <param name="options">Defines the customized behavior of the <see cref="T:System.Text.Json.Utf8JsonWriter" />. By default, it writes minimized JSON (with no extra white space) and validates that the JSON being written is structurally valid according to the JSON RFC.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bufferWriter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.#ctor(System.IO.Stream,System.Text.Json.JsonWriterOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Utf8JsonWriter" /> class using the specified stream to write the output to and customization options.</summary>
      <param name="utf8Json">The destination for writing JSON text.</param>
      <param name="options">Defines the customized behavior of the <see cref="T:System.Text.Json.Utf8JsonWriter" />. By default, it writes minimized JSON (with no extra white space) and validates that the JSON being written is structurally valid according to the JSON RFC.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Text.Json.Utf8JsonWriter.BytesCommitted">
      <summary>Gets the total number of bytes committed to the output by the current instance so far.</summary>
      <returns>The total number of bytes committed to the output by the <see cref="T:System.Text.Json.Utf8JsonWriter" /> so far.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonWriter.BytesPending">
      <summary>Gets the number of bytes written by the <see cref="T:System.Text.Json.Utf8JsonWriter" /> so far that have not yet been flushed to the output and committed.</summary>
      <returns>The number of bytes written so far by the <see cref="T:System.Text.Json.Utf8JsonWriter" /> that have not yet been flushed to the output and committed.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonWriter.CurrentDepth">
      <summary>Gets the depth of the current token.</summary>
      <returns>The depth of the current token.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.Dispose">
      <summary>Commits any leftover JSON text that has not yet been flushed and releases all resources used by the current instance.</summary>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.DisposeAsync">
      <summary>Asynchronously commits any leftover JSON text that has not yet been flushed and releases all resources used by the current instance.</summary>
      <returns>A task representing the asynchronous dispose operation.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.Flush">
      <summary>Commits the JSON text written so far, which makes it visible to the output destination.</summary>
      <exception cref="T:System.ObjectDisposedException">This instance has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.FlushAsync(System.Threading.CancellationToken)">
      <summary>Asynchronously commits the JSON text written so far, which makes it visible to the output destination.</summary>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task representing the asynchronous flush operation.</returns>
      <exception cref="T:System.ObjectDisposedException">This instance has been disposed.</exception>
    </member>
    <member name="P:System.Text.Json.Utf8JsonWriter.Options">
      <summary>Gets the custom behavior when writing JSON using this instance, which indicates whether to format the output while writing, whether to skip structural JSON validation, and which characters to escape.</summary>
      <returns>The custom behavior of this instance of the writer for formatting, validating, and escaping.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.Reset">
      <summary>Resets the internal state of this instance so that it can be reused.</summary>
      <exception cref="T:System.ObjectDisposedException">This instance has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.Reset(System.Buffers.IBufferWriter{System.Byte})">
      <summary>Resets the internal state of this instance so that it can be reused with a new instance of <see cref="T:System.Buffers.IBufferWriter`1" />.</summary>
      <param name="bufferWriter">The destination for writing JSON text.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bufferWriter" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">This instance has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.Reset(System.IO.Stream)">
      <summary>Resets the internal state of this instance so that it can be reused with a new instance of <see cref="T:System.IO.Stream" />.</summary>
      <param name="utf8Json">The destination for writing JSON text.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">This instance has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBase64String(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Writes the property name and raw bytes value (as a Base64 encoded JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded name of the property to write.</param>
      <param name="bytes">The binary data to write as Base64 encoded text.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBase64String(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Byte})">
      <summary>Writes the property name and raw bytes value (as a Base64 encoded JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="bytes">The binary data to write as Base64 encoded text.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBase64String(System.String,System.ReadOnlySpan{System.Byte})">
      <summary>Writes the property name and raw bytes value (as a Base64 encoded JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="bytes">The binary data to write as Base64 encoded text.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBase64String(System.Text.Json.JsonEncodedText,System.ReadOnlySpan{System.Byte})">
      <summary>Writes the pre-encoded property name and raw bytes value (as a Base64 encoded JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON-encoded name of the property to write.</param>
      <param name="bytes">The binary data to write as Base64 encoded text.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBase64StringValue(System.ReadOnlySpan{System.Byte})">
      <summary>Writes the raw bytes value as a Base64 encoded JSON string as an element of a JSON array.</summary>
      <param name="bytes">The binary data to be written as a Base64 encoded JSON string element of a JSON array.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBoolean(System.ReadOnlySpan{System.Byte},System.Boolean)">
      <summary>Writes a property name specified as a read-only span of bytes and a <see cref="T:System.Boolean" /> value (as a JSON literal true or false) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON literal true or false as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBoolean(System.ReadOnlySpan{System.Char},System.Boolean)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.Boolean" /> value (as a JSON literal true or false) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON literal true or false as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBoolean(System.String,System.Boolean)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.Boolean" /> value (as a JSON literal true or false) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON literal true or false as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBoolean(System.Text.Json.JsonEncodedText,System.Boolean)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.Boolean" /> value (as a JSON literal true or false) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON literal true or false as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBooleanValue(System.Boolean)">
      <summary>Writes a <see cref="T:System.Boolean" /> value (as a JSON literal true or false) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON literal true or false as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteCommentValue(System.ReadOnlySpan{System.Byte})">
      <summary>Writes a UTF-8 text value as a JSON comment.</summary>
      <param name="utf8Value">The UTF-8 encoded value to be written as a JSON comment within /*..*/.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.
-or-
<paramref name="utf8Value" /> contains a comment delimiter (that is, */).</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteCommentValue(System.ReadOnlySpan{System.Char})">
      <summary>Writes a UTF-16 text value as a JSON comment.</summary>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON comment within /*..*/.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.
-or-
<paramref name="value" /> contains a comment delimiter (that is, */).</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteCommentValue(System.String)">
      <summary>Writes a string text value as a JSON comment.</summary>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON comment within /*..*/.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.
-or-
<paramref name="value" /> contains a comment delimiter (that is, */).</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteEndArray">
      <summary>Writes the end of a JSON array.</summary>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteEndObject">
      <summary>Writes the end of a JSON object.</summary>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNull(System.ReadOnlySpan{System.Byte})">
      <summary>Writes a property name specified as a read-only span of bytes and the JSON literal null as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNull(System.ReadOnlySpan{System.Char})">
      <summary>Writes a property name specified as a read-only character span and the JSON literal null as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNull(System.String)">
      <summary>Writes a property name specified as a string and the JSON literal null as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNull(System.Text.Json.JsonEncodedText)">
      <summary>Writes the pre-encoded property name and the JSON literal null as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNullValue">
      <summary>Writes the JSON literal null as an element of a JSON array.</summary>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Byte},System.Decimal)">
      <summary>Writes a property name specified as a read-only span of bytes and a <see cref="T:System.Decimal" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Byte},System.Double)">
      <summary>Writes a property name specified as a read-only span of bytes and a <see cref="T:System.Double" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Byte},System.Int32)">
      <summary>Writes a property name specified as a read-only span of bytes and an <see cref="T:System.Int32" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Byte},System.Int64)">
      <summary>Writes a property name specified as a read-only span of bytes and an <see cref="T:System.Int64" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Byte},System.Single)">
      <summary>Writes a property name specified as a read-only span of bytes and a <see cref="T:System.Single" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Byte},System.UInt32)">
      <summary>Writes a property name specified as a read-only span of bytes and a <see cref="T:System.UInt32" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Byte},System.UInt64)">
      <summary>Writes a property name specified as a read-only span of bytes and a <see cref="T:System.UInt64" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Char},System.Decimal)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.Decimal" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Char},System.Double)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.Double" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Char},System.Int32)">
      <summary>Writes a property name specified as a read-only character span and an <see cref="T:System.Int32" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Char},System.Int64)">
      <summary>Writes a property name specified as a read-only character span and an <see cref="T:System.Int64" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Char},System.Single)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.Single" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Char},System.UInt32)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.UInt32" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Char},System.UInt64)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.UInt64" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.String,System.Decimal)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.Decimal" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.String,System.Double)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.Double" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.String,System.Int32)">
      <summary>Writes a property name specified as a string and an <see cref="T:System.Int32" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.String,System.Int64)">
      <summary>Writes a property name specified as a string and an <see cref="T:System.Int64" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.String,System.Single)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.Single" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.String,System.UInt32)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.UInt32" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.String,System.UInt64)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.UInt64" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.Text.Json.JsonEncodedText,System.Decimal)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.Decimal" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.Text.Json.JsonEncodedText,System.Double)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.Double" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.Text.Json.JsonEncodedText,System.Int32)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.Int32" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.Text.Json.JsonEncodedText,System.Int64)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.Int64" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.Text.Json.JsonEncodedText,System.Single)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.Single" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.Text.Json.JsonEncodedText,System.UInt32)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.UInt32" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.Text.Json.JsonEncodedText,System.UInt64)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.UInt64" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumberValue(System.Decimal)">
      <summary>Writes a <see cref="T:System.Decimal" /> value (as a JSON number) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON number as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumberValue(System.Double)">
      <summary>Writes a <see cref="T:System.Double" /> value (as a JSON number) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON number as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumberValue(System.Int32)">
      <summary>Writes an <see cref="T:System.Int32" /> value (as a JSON number) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON number as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumberValue(System.Int64)">
      <summary>Writes an <see cref="T:System.Int64" /> value (as a JSON number) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON number as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumberValue(System.Single)">
      <summary>Writes a <see cref="T:System.Single" /> value (as a JSON number) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON number as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumberValue(System.UInt32)">
      <summary>Writes a <see cref="T:System.UInt32" /> value (as a JSON number) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON number as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumberValue(System.UInt64)">
      <summary>Writes a <see cref="T:System.UInt64" /> value (as a JSON number) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON number as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WritePropertyName(System.ReadOnlySpan{System.Byte})">
      <summary>Writes the UTF-8 property name (as a JSON string) as the first part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WritePropertyName(System.ReadOnlySpan{System.Char})">
      <summary>Writes the property name (as a JSON string) as the first part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WritePropertyName(System.String)">
      <summary>Writes the property name (as a JSON string) as the first part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyName" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WritePropertyName(System.Text.Json.JsonEncodedText)">
      <summary>Writes the pre-encoded property name (as a JSON string) as the first part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartArray">
      <summary>Writes the beginning of a JSON array.</summary>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON exceeds the maximum depth of 1,000.
-or-
Validation is enabled, and this write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartArray(System.ReadOnlySpan{System.Byte})">
      <summary>Writes the beginning of a JSON array with a property name specified as a read-only span of bytes as the key.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON array to be written.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON exceeds the maximum depth of 1,000.
-or-
Validation is enabled, and this write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartArray(System.ReadOnlySpan{System.Char})">
      <summary>Writes the beginning of a JSON array with a property name specified as a read-only character span as the key.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON array to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON exceeds the maximum depth of 1,000.
-or-
Validation is enabled, and this write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartArray(System.String)">
      <summary>Writes the beginning of a JSON array with a property name specified as a string as the key.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON array to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON exceeds the maximum depth of 1,000.
-or-
Validation is enabled, and this write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartArray(System.Text.Json.JsonEncodedText)">
      <summary>Writes the beginning of a JSON array with a pre-encoded property name as the key.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON array to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON has exceeded the maximum depth of 1,000.
-or-
Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartObject">
      <summary>Writes the beginning of a JSON object.</summary>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON exceeds the maximum depth of 1,000.
-or-
Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartObject(System.ReadOnlySpan{System.Byte})">
      <summary>Writes the beginning of a JSON object with a property name specified as a read-only span of bytes as the key.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON exceeds the maximum depth of 1,000.
-or-
Validation is enabled, and this write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartObject(System.ReadOnlySpan{System.Char})">
      <summary>Writes the beginning of a JSON object with a property name specififed as a read-only character span as the key.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON exceeds the maximum depth of 1,000.
-or-
Validation is enabled, and this write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartObject(System.String)">
      <summary>Writes the beginning of a JSON object with a property name specified as a string as the key.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON exceeds the maximum depth of 1,000.
-or-
Validation is enabled, and this write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartObject(System.Text.Json.JsonEncodedText)">
      <summary>Writes the beginning of a JSON object with a pre-encoded property name as the key.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON has exceeded the maximum depth of 1,000.
-or-
Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Byte},System.DateTime)">
      <summary>Writes a UTF-8 property name and a <see cref="T:System.DateTime" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Byte},System.DateTimeOffset)">
      <summary>Writes a UTF-8 property name and a <see cref="T:System.DateTimeOffset" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Byte},System.Guid)">
      <summary>Writes a UTF-8 property name and a <see cref="T:System.Guid" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Writes a UTF-8 property name and UTF-8 text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="utf8Value">The UTF-8 encoded value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Char})">
      <summary>Writes a UTF-8 property name and UTF-16 text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Byte},System.String)">
      <summary>Writes a UTF-8 property name and string text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Byte},System.Text.Json.JsonEncodedText)">
      <summary>Writes the UTF-8 property name and pre-encoded value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The JSON encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Char},System.DateTime)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.DateTime" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Char},System.DateTimeOffset)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.DateTimeOffset" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Char},System.Guid)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.Guid" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Byte})">
      <summary>Writes a UTF-16 property name and UTF-8 text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="utf8Value">The UTF-8 encoded value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char})">
      <summary>Writes a UTF-16 property name and UTF-16 text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Char},System.String)">
      <summary>Writes a UTF-16 property name and string text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Char},System.Text.Json.JsonEncodedText)">
      <summary>Writes the property name and pre-encoded value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The JSON encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.String,System.DateTime)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.DateTime" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.String,System.DateTimeOffset)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.DateTimeOffset" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.String,System.Guid)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.Guid" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.String,System.ReadOnlySpan{System.Byte})">
      <summary>Writes a property name specified as a string and a UTF-8 text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="utf8Value">The UTF-8 encoded value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.String,System.ReadOnlySpan{System.Char})">
      <summary>Writes a property name specified as a string and a UTF-16 text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.String,System.String)">
      <summary>Writes a property name specified as a string and a string text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.String,System.Text.Json.JsonEncodedText)">
      <summary>Writes the property name and pre-encoded value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The JSON encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.Text.Json.JsonEncodedText,System.DateTime)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.DateTime" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.Text.Json.JsonEncodedText,System.DateTimeOffset)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.DateTimeOffset" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.Text.Json.JsonEncodedText,System.Guid)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.Guid" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.Text.Json.JsonEncodedText,System.ReadOnlySpan{System.Byte})">
      <summary>Writes the pre-encoded property name and UTF-8 text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="utf8Value">The UTF-8 encoded value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.Text.Json.JsonEncodedText,System.ReadOnlySpan{System.Char})">
      <summary>Writes the pre-encoded property name and text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.Text.Json.JsonEncodedText,System.String)">
      <summary>Writes the pre-encoded property name and string text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.Text.Json.JsonEncodedText,System.Text.Json.JsonEncodedText)">
      <summary>Writes the pre-encoded property name and pre-encoded value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The JSON encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStringValue(System.DateTime)">
      <summary>Writes a <see cref="T:System.DateTime" /> value (as a JSON string) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON string as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStringValue(System.DateTimeOffset)">
      <summary>Writes a <see cref="T:System.DateTimeOffset" /> value (as a JSON string) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON string as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStringValue(System.Guid)">
      <summary>Writes a <see cref="T:System.Guid" /> value (as a JSON string) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON string as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStringValue(System.ReadOnlySpan{System.Byte})">
      <summary>Writes a UTF-8 text value (as a JSON string) as an element of a JSON array.</summary>
      <param name="utf8Value">The UTF-8 encoded value to be written as a JSON string element of a JSON array.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStringValue(System.ReadOnlySpan{System.Char})">
      <summary>Writes a UTF-16 text value (as a JSON string) as an element of a JSON array.</summary>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON string element of a JSON array.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStringValue(System.String)">
      <summary>Writes a string text value (as a JSON string) as an element of a JSON array.</summary>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON string element of a JSON array.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStringValue(System.Text.Json.JsonEncodedText)">
      <summary>Writes the pre-encoded text value (as a JSON string) as an element of a JSON array.</summary>
      <param name="value">The JSON encoded value to be written as a UTF-8 transcoded JSON string element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
  </members>
</doc>