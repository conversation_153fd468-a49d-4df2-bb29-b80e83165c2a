﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.ApplicationServer.Caching.Client</name>
  </assembly>
  <members>
    <member name="T:Microsoft.ApplicationServer.Caching.CacheOperationCompletedEventArgs"></member>
    <member name="P:Microsoft.ApplicationServer.Caching.CacheOperationCompletedEventArgs.ExceptionObject">
      <returns>Returns <see cref="T:System.Exception" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.CacheOperationCompletedEventArgs.HasSucceeded">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.CacheOperationCompletedEventArgs.OperationContext">
      <returns>Returns <see cref="T:Microsoft.ApplicationServer.Caching.CacheOperationContext" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.CacheOperationCompletedEventArgs.OperationType">
      <returns>Returns <see cref="T:Microsoft.ApplicationServer.Caching.CacheOperationType" />.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.CacheOperationContext"></member>
    <member name="T:Microsoft.ApplicationServer.Caching.CacheOperationStartedEventArgs"></member>
    <member name="P:Microsoft.ApplicationServer.Caching.CacheOperationStartedEventArgs.OperationContext">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.CacheOperationStartedEventArgs.OperationType">
      <returns>Returns <see cref="T:Microsoft.ApplicationServer.Caching.CacheOperationType" />.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.CacheOperationType"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.Add">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.AsyncGet">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.BulkGet">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.ClearCache">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.ClearRegion">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.Concatenate">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.ContainsKey">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.CreateRegion">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.Get">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.GetAllKeys">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.GetAndLock">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.GetCacheItem">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.GetIfNewer">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.Increment">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.LockedRemove">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.Put">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.PutAndUnlock">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.Remove">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.RemoveRegion">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.ResetObjectTimeout">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.Unknown">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.CacheOperationType.Unlock">
      <summary />
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.AverageCacheBulkGetLatencyBaseCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.AverageCacheBulkGetLatencyCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.AverageCacheGetLatencyBaseCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.AverageCacheGetLatencyCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.AverageCachePutLatencyBaseCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.AverageCachePutLatencyCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.AverageNetworkLatencyBaseCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.AverageNetworkLatencyCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.CounterCategoryHelp"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.CounterCategoryName"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.Dispose">
      <summary>Internal.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.FailureExceptionRate"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.IncomingDataRateCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.NetworkExceptionRate"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.OutgoingDataRateCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.PercentageLocalCacheFullBaseCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.PercentageLocalCacheFullCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.PercentageLocalCacheHitsBaseCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.PercentageLocalCacheHitsCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.RequestPerSecondCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.RetryExceptionRateCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.ServerResponseDroppedCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.TimeoutExceptionRateCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.TotalConnectionRequestsFailedCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.TotalConnectionsCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.TotalFailureExceptionsCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.TotalItemsInLocalCacheCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.TotalLocalCacheHitsCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.TotalNetworkExceptionsCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.TotalNotificationsReceivedCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.TotalOutstandingRequestsCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.TotalReadsCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.TotalRequestsCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.TotalRetryExceptionsCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.TotalTimeoutExceptionsCounterName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ClientPerformanceCounters.TotalWritesCounterName"></member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCache">
      <summary>The object that is used by cache-enabled applications for storing and retrieving objects from the cache. An instance of this object is referred to as the cache client.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.#ctor">
      <summary>Creates a DataCache that accesses the "default" cache; uses settings in the "default" cache client configuration.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.#ctor(System.String)">
      <summary>Creates a DataCache that accesses the specified cache name; uses settings in the "default" cache client configuration.</summary>
      <param name="cacheName">The named cache to use. For Windows Azure Shared Caching, this must be "default".</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.#ctor(System.String,System.String)">
      <summary>Creates a DataCache that accesses the specified cache name; uses settings in the named cache client configuration.</summary>
      <param name="cacheName">The named cache to use. For Windows Azure Shared Caching, this must be "default".</param>
      <param name="clientConfigurationName">The cache client configuration to use to initialize the DataCache object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Add(System.String,System.Object)">
      <summary>Adds an object to the cache.</summary>
      <returns>A  <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the key value.</returns>
      <param name="key">A unique value that is used to store and retrieve the object from the cache. Key names must be less than 65 KB.</param>
      <param name="value">The object saved to the cache.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Add(System.String,System.Object,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag})">
      <summary>Adds an object to the cache. This method enables associating tags with the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the key value.</returns>
      <param name="key">A unique value that is used to store and retrieve the object from the cache. Key names must be less than 65 KB.</param>
      <param name="value">The object saved to the cache.</param>
      <param name="tags">An array of <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheTag" /> objects to associate with the cached object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Add(System.String,System.Object,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag},System.String)">
      <summary>Adds an object to a region in the cache. This method enables association of tags with objects in the cache. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the key value.</returns>
      <param name="key">A unique value that is used to store and retrieve the object from the cache. Key names must be less than 65 KB.</param>
      <param name="value">The object saved to the cache.</param>
      <param name="tags">An array of <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheTag" /> objects to associate with the cached object.</param>
      <param name="region">The name of the region to save the object in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Add(System.String,System.Object,System.String)">
      <summary>Adds an object to a region in the cache. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the key value.</returns>
      <param name="key">A unique value that is used to store and retrieve the object from the cache. Key names must be less than 65 KB.</param>
      <param name="value">The object saved to the cache.</param>
      <param name="region">The name of the region to save the object in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Add(System.String,System.Object,System.TimeSpan)">
      <summary>Adds an object to the cache. This method provides the ability to specify when the object should be expired.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the key value.</returns>
      <param name="key">A unique value that is used to store and retrieve the object from the cache. Key names must be less than 65 KB.</param>
      <param name="value">The object saved to the cache.</param>
      <param name="timeout">The amount of time the object should reside in the cache before expiration.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Add(System.String,System.Object,System.TimeSpan,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag})">
      <summary>Adds an object to the cache. This method enables associating tags with the cached object and specifying when the object should be expired. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the key value.</returns>
      <param name="key">A unique value that is used to store and retrieve the object from the cache. Key names must be less than 65 KB.</param>
      <param name="value">The object saved to the cache.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
      <param name="tags">An array of <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheTag" /> objects to associate with the cached object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Add(System.String,System.Object,System.TimeSpan,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag},System.String)">
      <summary>Adds an object to a region in the cache. This method enables associating tags with the cached object and specifying when the object should be expired. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the key value.</returns>
      <param name="key">A unique value that is used to store and retrieve the object from the cache. Key names must be less than 65 KB.</param>
      <param name="value">The object saved to the cache.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
      <param name="tags">An array of <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheTag" /> objects to associate with the cached object.</param>
      <param name="region">The name of the region to save the object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Add(System.String,System.Object,System.TimeSpan,System.String)">
      <summary>Adds an object to a region in the cache. This method provides the ability to specify when the object should be expired. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" />object that represents the version of the object saved to the cache under the key value.</returns>
      <param name="key">A unique value that is used to store and retrieve the object from the cache. Key names must be less than 65 KB.</param>
      <param name="value">The object saved to the cache.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
      <param name="region">The name of the region to save the object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.AddCacheLevelBulkCallback(Microsoft.ApplicationServer.Caching.DataCacheBulkNotificationCallback)">
      <summary>Adds a bulk cache notification callback for cache operations occurring on all regions and items. Not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor" /> object used to identify the cache bulk notification callback.</returns>
      <param name="clientCallback">The name of the method you want to invoke when these notifications occur.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.AddCacheLevelCallback(Microsoft.ApplicationServer.Caching.DataCacheOperations,Microsoft.ApplicationServer.Caching.DataCacheNotificationCallback)">
      <summary>Adds a cache notification callback for cache operations occurring on all regions and items. Not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor" /> object used to identify the cache notification callback.</returns>
      <param name="filter">The type of cache operation(s) that will trigger cache notifications.</param>
      <param name="clientCallback">The name of the method you want to invoke when these notifications occur.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.AddFailureNotificationCallback(Microsoft.ApplicationServer.Caching.DataCacheFailureNotificationCallback)">
      <summary>Adds a failure notification callback, for notifications indicating that a client has missed one or more cache notifications. Not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor" /> object used to identify the cache notification callback.</returns>
      <param name="failureCallback">The name of the method that you want to invoke when the failure notification occurs.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.AddItemLevelCallback(System.String,Microsoft.ApplicationServer.Caching.DataCacheOperations,Microsoft.ApplicationServer.Caching.DataCacheNotificationCallback)">
      <summary>Adds a cache notification callback for cache operations occurring on one specific item that is not stored in a region. Not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor" /> object used to identify the cache notification callback.</returns>
      <param name="key">The key used to store the object in the cache.</param>
      <param name="filter">The type of cache operation(s) that will trigger cache notifications.</param>
      <param name="clientCallback">The name of the method you want to invoke when these notifications occur.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.AddItemLevelCallback(System.String,Microsoft.ApplicationServer.Caching.DataCacheOperations,Microsoft.ApplicationServer.Caching.DataCacheNotificationCallback,System.String)">
      <summary>Adds a cache notification callback for cache operations occurring on one specific item that is stored in region. Not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor" /> object used to identify the cache notification callback.</returns>
      <param name="key">The key used to store the object in the cache.</param>
      <param name="filter">The type of cache operation(s) that will trigger cache notifications.</param>
      <param name="clientCallback">The name of the method you want to invoke when these notifications occur.</param>
      <param name="region">The name of the region where the object is stored.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.AddRegionLevelCallback(System.String,Microsoft.ApplicationServer.Caching.DataCacheOperations,Microsoft.ApplicationServer.Caching.DataCacheNotificationCallback)">
      <summary>Adds a cache notification callback for cache operations occurring on one specific region. Not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor" /> object used to identify the cache notification callback.</returns>
      <param name="region">The name of the region for which the specified item or region operations can trigger cache notifications.</param>
      <param name="filter">The type of cache operation(s) that will trigger cache notifications.</param>
      <param name="clientCallback">The name of the method you want to invoke when these notifications occur.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Append(System.String,System.String)">
      <summary>Concatenates a string to a string object stored in the cache.</summary>
      <param name="key">The key of the object stored in the cache. The object must be a string.</param>
      <param name="value">The string to concatenate to the stored object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Append(System.String,System.String,System.String)">
      <summary>Concatenates a string to a string object stored in the cache in the specified region.</summary>
      <param name="key">The key of the object stored in the cache. The object must be a string.</param>
      <param name="value">The string to concatenate to the stored object.</param>
      <param name="region">The user-defined region in which the object is stored.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.BulkGet(System.Collections.Generic.IEnumerable{System.String})">
      <summary>Gets all of the objects associated with the specified keys across all system regions. This method does not search user-defined regions.</summary>
      <returns>Returns an <see cref="T:System.Collections.Generic.IEnumerable`1" /> object with a list of key and value pairs.</returns>
      <param name="keys">List of keys for the objects to retrieve, cannot be null.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.BulkGet(System.Collections.Generic.IEnumerable{System.String},System.String)">
      <summary>Returns objects for the specified keys from the specified region of the cache. Not supported in Windows Azure Shared Caching.</summary>
      <returns>Returns an <see cref="T:System.Collections.Generic.IEnumerable`1" /> object with a list of key and value pairs.</returns>
      <param name="keys">List of keys for the objects to retrieve, cannot be null.</param>
      <param name="region">Name of the region, cannot be null.</param>
    </member>
    <member name="E:Microsoft.ApplicationServer.Caching.DataCache.CacheOperationCompleted">
      <summary>Internal.</summary>
    </member>
    <member name="E:Microsoft.ApplicationServer.Caching.DataCache.CacheOperationStarted">
      <summary>Internal.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Clear">
      <summary>Removes all objects from the cache associated with the DataCache object.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.ClearRegion(System.String)">
      <summary>Deletes all objects in the specified region. Not supported in Windows Azure Shared Caching.</summary>
      <param name="region">The name of the region whose objects are removed.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.CreateRegion(System.String)">
      <summary>Creates a region. Not supported in Windows Azure Shared Caching.</summary>
      <returns>Returns a<see cref="T:System.Boolean" /> value..</returns>
      <param name="region">The name of the region that is created. Region names must be less than 65 KB.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Decrement(System.String,System.Int64,System.Int64)">
      <summary>Decrements a long value stored in the cache.</summary>
      <returns>Returns the decremented <see cref="T:System.Int64" /> value.</returns>
      <param name="key">The key of the object stored in the cache. The object must be a long.</param>
      <param name="value">The amount to decrease the stored value.</param>
      <param name="initialValue">The initial value to use if the object does not exist in the cache.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Decrement(System.String,System.Int64,System.Int64,System.String)">
      <summary>Decrements a long value stored in the cache in the specified region.</summary>
      <returns>Returns the decremented <see cref="T:System.Int64" /> value.</returns>
      <param name="key">The key of the object stored in the cache. The object must be a long.</param>
      <param name="value">The amount to decrease the stored value.</param>
      <param name="initialValue">The initial value to use if the object does not exist in the cache.</param>
      <param name="region">The user-defined region in which the object is stored.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Get(System.String)">
      <summary>Gets an object from the cache using the specified key.</summary>
      <returns>The object that was cached by using the specified key. Null is returned if the key does not exist.</returns>
      <param name="key">The unique value that is used to identify the object in the cache.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Get(System.String,Microsoft.ApplicationServer.Caching.DataCacheItemVersion@)">
      <summary>Gets an object from the cache using the specified key. You may also provide the version to obtain a specific version of a key, if that version is still the most current in the cache.</summary>
      <returns>The object that was cached by using the specified key. Null is returned if the key does not exist. Even if the key does exist, Null may also be returned because the object has been updated to a new version.</returns>
      <param name="key">The unique value that is used to identify the object in the cache.</param>
      <param name="version">The version of the desired object. If this parameter is null, the version of the current object is retrieved.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Get(System.String,Microsoft.ApplicationServer.Caching.DataCacheItemVersion@,System.String)">
      <summary>Gets an object from the specified region by using the specified key. You may also provide the version to obtain the specific version of a key, if that version is still the most current in the region. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>The object that was cached by using the specified key. Null is returned if the key does not exist. Even if the key does exist, Null may also be returned because the object has been updated to a new version.</returns>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="version">The version of the desired object. If this parameter is null, the version of the current object is retrieved.</param>
      <param name="region">The name of the region where the object resides.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Get(System.String,System.String)">
      <summary>Gets an object from the specified region by using the specified key. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>The object that was cached by using the specified key. Null is returned if the key does not exist.</returns>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="region">The name of the region where the object resides.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.GetAndLock(System.String,System.TimeSpan,Microsoft.ApplicationServer.Caching.DataCacheLockHandle@)">
      <summary>Locks the key if the key is present and it is not locked and returns the object corresponding to the key.</summary>
      <returns>Returns <see cref="T:System.Object" /> matching the specified <paramref name="key" /> parameter if the <see cref="T:System.Object" /> is present and is not locked. If the key does not exist, a DataCacheException object is thrown with the ErrorCode set to <see cref="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.KeyDoesNotExist" />. Create objects based on the referenced key to resolve this error.If the object is already locked by another cache client, a DataCacheException object is thrown with the ErrorCode set to <see cref="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ObjectLocked" />. The object will be inaccessible until it is unlocked by the locking client.</returns>
      <param name="key">The unique value that is used to identify the object in the cache.</param>
      <param name="timeout">The amount of time that the object should remain locked.</param>
      <param name="lockHandle">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle" /> object required to unlock the object. The <paramref name="lockHandle" /> output parameter is passed by reference.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.GetAndLock(System.String,System.TimeSpan,Microsoft.ApplicationServer.Caching.DataCacheLockHandle@,System.Boolean)">
      <summary>Lock the key if key is present and it is not locked by any clients and returns the object corresponding to the key.</summary>
      <returns>Returns <see cref="T:System.Object" /> matching the specified <paramref name="key" /> parameter if the <see cref="T:System.Object" /> is present and is not locked. If the key does not exist, a DataCacheException object is thrown with the ErrorCode set to <see cref="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.KeyDoesNotExist" />. Create objects based on the referenced key to resolve this error.If the object is already locked by another cache client, a DataCacheException object is thrown with the ErrorCode set to <see cref="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ObjectLocked" />. The object will be inaccessible until it is unlocked by the locking client.</returns>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="timeout">The amount of time that object remains locked.</param>
      <param name="lockHandle">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle" /> object required to unlock the object. The <paramref name="lockHandle" /> output parameter is passed by reference.</param>
      <param name="forceLock">If forceLock is true, key is locked irrespective of key-value pair presence in cache.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.GetAndLock(System.String,System.TimeSpan,Microsoft.ApplicationServer.Caching.DataCacheLockHandle@,System.String)">
      <summary>Locks the key if the key is present and is not locked and returns the object corresponding to the key. For objects stored in regions. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>Returns <see cref="T:System.Object" /> matching the specified <paramref name="key" /> parameter if the <see cref="T:System.Object" /> is present and is not locked.If the key does not exist, a DataCacheException object is thrown with the ErrorCode set to <see cref="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.KeyDoesNotExist" />. Create objects based on the referenced key to resolve this error.If the object is already locked by another cache client, a DataCacheException object is thrown with the ErrorCode set to <see cref="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ObjectLocked" />. The object will be inaccessible until it is unlocked by the locking client.</returns>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="timeout">The amount of time that the object remains locked.</param>
      <param name="lockHandle">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle" /> object required to unlock the object. The <paramref name="lockHandle" /> output parameter is passed by reference.</param>
      <param name="region">The name of the region where the object resides.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.GetAndLock(System.String,System.TimeSpan,Microsoft.ApplicationServer.Caching.DataCacheLockHandle@,System.String,System.Boolean)">
      <summary>Lock the key if key is present and it is not locked by any clients and returns the object corresponding to the key. For objects stored in regions. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>Returns <see cref="T:System.Object" /> matching the specified <paramref name="key" /> parameter if the <see cref="T:System.Object" /> is present and is not locked.If the key does not exist, a DataCacheException object is thrown with the ErrorCode set to <see cref="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.KeyDoesNotExist" />. Create objects based on the referenced key to resolve this error.If the object is already locked by another cache client, a DataCacheException object is thrown with the ErrorCode set to <see cref="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ObjectLocked" />. The object will be inaccessible until it is unlocked by the locking client.</returns>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="timeout">The amount of time that the object remains locked.</param>
      <param name="lockHandle">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle" /> object required to unlock the object. The <paramref name="lockHandle" /> output parameter is passed by reference.</param>
      <param name="region">The name of the region where the object resides.</param>
      <param name="forceLock">If forceLock is true, key is locked irrespective of key-value pair presence in cache.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.GetCacheItem(System.String)">
      <summary>Gets a <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItem" /> object to retrieve all information associated with your cached object in the cluster.</summary>
      <returns>A<see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItem" /> object that contains all information associated with your cached object. Null is returned if the key does not exist.</returns>
      <param name="key">The unique value that is used to identify the object in the cache.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.GetCacheItem(System.String,System.String)">
      <summary>Gets a <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItem" /> object to retrieve all information associated with your cached object in the cluster. For objects stored in regions. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItem" /> object that contains all information associated with your cached object. Null is returned if the key does not exist.</returns>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="region">The name of the region where the object resides.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.GetIfNewer(System.String,Microsoft.ApplicationServer.Caching.DataCacheItemVersion@)">
      <summary>Gets an object from the cache, but only if a newer version of the object resides in the cache.</summary>
      <returns>If the version in the cache differs from the version parameter, the corresponding cached object is returned. If the version in the cache is the same as the version parameter, null is returned.</returns>
      <param name="key">The unique value that is used to identify the object in the cache.</param>
      <param name="version">The version of the desired object, passed by reference.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.GetIfNewer(System.String,Microsoft.ApplicationServer.Caching.DataCacheItemVersion@,System.String)">
      <summary>Gets an object from the specified region, but only if a newer version of the object resides in the region. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>If the version in the region differs from the version parameter, the corresponding cached object is returned. If the version in the region is the same as the version parameter, null is returned.</returns>
      <param name="key">The unique value that is used to identify the object in the cache.</param>
      <param name="version">The version of the desired object, passed by reference.</param>
      <param name="region">The name of the region where the object resides.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.GetObjectsByAllTags(System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag},System.String)">
      <summary>Gets an enumerable list of all cached objects in the specified region that have all the same tags in common. Not supported in Windows Azure Shared Caching.</summary>
      <returns>An enumerable list of all cached objects in the specified region that have all the same tags in common.</returns>
      <param name="tags">A list of tags for which to search.</param>
      <param name="region">The name of the region to search. Tags are not supported outside regions. Therefore, a region name is required.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.GetObjectsByAnyTag(System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag},System.String)">
      <summary>Gets an enumerable list of all cached objects in the specified region that have any of the same tags in common. Not supported in Windows Azure Shared Caching.</summary>
      <returns>An enumerable list of all cached objects in the specified region that have any of the same tags in common. Null is returned if no objects in the specified region have any of the tags specified.</returns>
      <param name="tags">A list of tags for which to search.</param>
      <param name="region">The name of the region to search. Tags are not supported outside regions. Therefore, a region name is required.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.GetObjectsByTag(Microsoft.ApplicationServer.Caching.DataCacheTag,System.String)">
      <summary>Gets an enumerable list of all cached objects in the specified region that have the specified tag. Not supported in Windows Azure Shared Caching.</summary>
      <returns>An enumerable list of all cached objects in the specified region that have the specified tag. Null is returned if no objects in the specified region have the tag specified.</returns>
      <param name="tag">The tag for which to search.</param>
      <param name="region">The name of the region to search. Tags are not supported outside regions. Therefore, a region name is required.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.GetObjectsInRegion(System.String)">
      <summary>Gets an enumerable list of all cached objects in the specified region. Not supported in Windows Azure Shared Caching.</summary>
      <returns>An enumerable list of all cached objects in the specified region.</returns>
      <param name="region">The name of the region for which to return a list of all resident objects.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.GetSystemRegionName(System.String)">
      <summary>Returns the system region name for the specified key. Not supported in Windows Azure Shared Caching.</summary>
      <returns>Returns the region name as a <see cref="T:System.String" />.</returns>
      <param name="key">The key for which to find the associated system region.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.GetSystemRegions">
      <summary>Used to determine the default regions for the cache. Not supported in Windows Azure Shared Caching.</summary>
      <returns>Returns an enumerable list of default regions as an <see cref="T:System.Collections.Generic.IEnumerable`1" /> object.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Increment(System.String,System.Int64,System.Int64)">
      <summary>Increments a long value stored in the cache.</summary>
      <returns>Returns the incremented <see cref="T:System.Int64" /> value.</returns>
      <param name="key">The key of the object stored in the cache. The object must be a long.</param>
      <param name="value">The amount to increase the stored value.</param>
      <param name="initialValue">The initial value to use if the object does not exist in the cache.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Increment(System.String,System.Int64,System.Int64,System.String)">
      <summary>Increments a long value stored in the cache in the specified region.</summary>
      <returns>Returns the incremented <see cref="T:System.Int64" /> value.</returns>
      <param name="key">The key of the object stored in the cache. The object must be a long.</param>
      <param name="value">The amount to increase the stored value.</param>
      <param name="initialValue">The initial value to use if the object does not exist in the cache.</param>
      <param name="region">The user-defined region in which the object is stored.</param>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCache.Item(System.String)">
      <summary>Allows for the use of array notation to access cached objects.</summary>
      <returns>A deserialized object that was saved to the cache that uses the key.</returns>
      <param name="key">The key that is used to save the cached object.</param>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCache.Name">
      <summary>The name of the cache associated with the DataCache object.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Prepend(System.String,System.String)">
      <summary>Prepends a string to a string object stored in the cache.</summary>
      <param name="key">The key of the object stored in the cache. The object must be a string.</param>
      <param name="value">The string to prepend to the stored object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Prepend(System.String,System.String,System.String)">
      <summary>Prepends a string to a string object stored in the cache in the specified region.</summary>
      <param name="key">The key of the object stored in the cache. The object must be a string.</param>
      <param name="value">The string to prepend to the stored object.</param>
      <param name="region">The user-defined region in which the object is stored.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Put(System.String,System.Object)">
      <summary>Adds or replaces an object in the cache.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the key value.</returns>
      <param name="key">The unique value that is used to identify the object in the cache. Key names must be less than 65 KB.</param>
      <param name="value">The object to add or replace.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Put(System.String,System.Object,Microsoft.ApplicationServer.Caching.DataCacheItemVersion)">
      <summary>Adds or replaces an object in the cache if it is at the specified version.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the <paramref name="key" /> value.</returns>
      <param name="key">The unique value that is used to identify the object in the cache. Key names must be less than 65 KB.</param>
      <param name="value">The object to add or replace.</param>
      <param name="oldVersion">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object that is to be replaced.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Put(System.String,System.Object,Microsoft.ApplicationServer.Caching.DataCacheItemVersion,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag})">
      <summary>Adds or replaces an object in the cache if it is at the specified version. Associates tags with the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the <paramref name="key" /> value.</returns>
      <param name="key">The unique value that is used to identify the object in the cache. Key names must be less than 65 KB.</param>
      <param name="value">The object to add or replace.</param>
      <param name="oldVersion">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object that is to be replaced.</param>
      <param name="tags">A list of tags to associate with the object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Put(System.String,System.Object,Microsoft.ApplicationServer.Caching.DataCacheItemVersion,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag},System.String)">
      <summary>Adds or replaces an object in the specified region if it is at the specified version. Associates tags with the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the <paramref name="key" /> value.</returns>
      <param name="key">The unique value that is used to identify the object in the region. Key names must be less than 65 KB.</param>
      <param name="value">The object to add or replace.</param>
      <param name="oldVersion">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object that is to be replaced.</param>
      <param name="tags">A list of tags to associate with the object.</param>
      <param name="region">The name of the region the object resides in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Put(System.String,System.Object,Microsoft.ApplicationServer.Caching.DataCacheItemVersion,System.String)">
      <summary>Adds or replaces an object in the specified region if it is at the specified version. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the <paramref name="key" /> value.</returns>
      <param name="key">The unique value that is used to identify the object in the region. Key names must be less than 65 KB.</param>
      <param name="value">The object to add or replace.</param>
      <param name="oldVersion">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object that is to be replaced.</param>
      <param name="region">The name of the region the object resides in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Put(System.String,System.Object,Microsoft.ApplicationServer.Caching.DataCacheItemVersion,System.TimeSpan)">
      <summary>Adds or replaces an object in the cache if it is at the specified version. Specifies the timeout value of the cached object.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the key value.</returns>
      <param name="key">The unique value that is used to identify the object in the region. Key names must be less than 65 KB.</param>
      <param name="value">The object to add or replace.</param>
      <param name="oldVersion">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object that is to be replaced.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Put(System.String,System.Object,Microsoft.ApplicationServer.Caching.DataCacheItemVersion,System.TimeSpan,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag})">
      <summary>Adds or replaces an object in the cache if it is at the specified version. Specifies the timeout value and associates tags with the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the key value.</returns>
      <param name="key">The unique value that is used to identify the object in the cache. Key names must be less than 65 KB.</param>
      <param name="value">The object to add or replace.</param>
      <param name="oldVersion">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object that is to be replaced.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
      <param name="tags">A list of tags to associate with the object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Put(System.String,System.Object,Microsoft.ApplicationServer.Caching.DataCacheItemVersion,System.TimeSpan,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag},System.String)">
      <summary>Adds or replaces an object in the specified region if it is at the specified version. Specifies the timeout value and associates tags with the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the key value.</returns>
      <param name="key">The unique value that is used to identify the object in the region. Key names must be less than 65 KB.</param>
      <param name="value">The object to add or replace.</param>
      <param name="oldVersion">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object that is to be replaced.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
      <param name="tags">A list of tags to associate with the object.</param>
      <param name="region">The name of the region the object resides in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Put(System.String,System.Object,Microsoft.ApplicationServer.Caching.DataCacheItemVersion,System.TimeSpan,System.String)">
      <summary>Adds or replaces an object in the specified region if it is at the specified version. Specifies the timeout value of the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the <paramref name="key" /> value.</returns>
      <param name="key">The unique value that is used to identify the object in the region. Key names must be less than 65 KB.</param>
      <param name="value">The object to add or replace.</param>
      <param name="oldVersion">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object that is to be replaced.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
      <param name="region">The name of the region the object resides in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Put(System.String,System.Object,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag})">
      <summary>Adds or replaces an object in the cache. Associates tags with the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the key value.</returns>
      <param name="key">The unique value that is used to identify the object in the cache. Key names must be less than 65 KB.</param>
      <param name="value">The object to add or replace.</param>
      <param name="tags">A list of tags to associate with the object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Put(System.String,System.Object,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag},System.String)">
      <summary>Adds or replaces an object in the specified region. Associates tags with the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the <paramref name="key" /> value.</returns>
      <param name="key">The unique value that is used to identify the object in the region. Key names must be less than 65 KB.</param>
      <param name="value">The object to add or replace.</param>
      <param name="tags">A list of tags to associate with the object.</param>
      <param name="region">The name of the region the object resides in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Put(System.String,System.Object,System.String)">
      <summary>Adds or replaces an object in the specified region. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the <paramref name="key" /> value.</returns>
      <param name="key">The unique value that is used to identify the object in the region. Key names must be less than 65 KB.</param>
      <param name="value">The object to add or replace.</param>
      <param name="region">The name of the region the object resides in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Put(System.String,System.Object,System.TimeSpan)">
      <summary>Adds or replaces an object in the cache. Specifies the timeout value of the cached object.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the <paramref name="key" /> value.</returns>
      <param name="key">The unique value that is used to identify the object in the cache. Key names must be less than 65 KB.</param>
      <param name="value">The object to add or replace.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Put(System.String,System.Object,System.TimeSpan,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag})">
      <summary>Adds or replaces an object in the cache. Specifies the timeout value and associates tags with the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the <paramref name="key" /> value.</returns>
      <param name="key">The unique value that is used to identify the object in the cache. Key names must be less than 65 KB.</param>
      <param name="value">The object to add or replace.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
      <param name="tags">A list of tags to associate with the object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Put(System.String,System.Object,System.TimeSpan,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag},System.String)">
      <summary>Adds or replaces an object in the specified region. Specifies the timeout value and associates tags with the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the <paramref name="key" /> value.</returns>
      <param name="key">The unique value that is used to identify the object in the region. Key names must be less than 65 KB.</param>
      <param name="value">The object to add or replace.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
      <param name="tags">A list of tags to associate with the object.</param>
      <param name="region">The name of the region the object resides in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Put(System.String,System.Object,System.TimeSpan,System.String)">
      <summary>Adds or replaces an object in the specified region. Specifies the timeout value of the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the object saved to the cache under the <paramref name="key" /> value.</returns>
      <param name="key">Adds or replaces an object in the specified region. Specifies the timeout value of the cached object. Key names must be less than 65 KB.</param>
      <param name="value">The object to add or replace.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
      <param name="region">The name of the region the object resides in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.PutAndUnlock(System.String,System.Object,Microsoft.ApplicationServer.Caching.DataCacheLockHandle)">
      <summary>Replaces and unlocks an object in the cache.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object after it has been updated by this method. </returns>
      <param name="key">The unique value that is used to identify the object in the cache.</param>
      <param name="value">The object to add or replace.</param>
      <param name="lockHandle">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle" /> object that was returned when the object was locked.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.PutAndUnlock(System.String,System.Object,Microsoft.ApplicationServer.Caching.DataCacheLockHandle,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag})">
      <summary>Replaces and unlocks an object in the cache. Associates tags with the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object after it has been updated by this method.</returns>
      <param name="key">The unique value that is used to identify the object in the cache.</param>
      <param name="value">The object to add or replace.</param>
      <param name="lockHandle">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle" /> object that was returned when the object was locked.</param>
      <param name="tags">A list of tags to associate with the object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.PutAndUnlock(System.String,System.Object,Microsoft.ApplicationServer.Caching.DataCacheLockHandle,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag},System.String)">
      <summary>Replaces and unlocks an object in the specified region. Associates tags with the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object after it has been updated by this method.</returns>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="value">The object to add or replace.</param>
      <param name="lockHandle">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle" /> object that was returned when the object was locked.</param>
      <param name="tags">A list of tags to associate with the object.</param>
      <param name="region">The name of the region the object resides in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.PutAndUnlock(System.String,System.Object,Microsoft.ApplicationServer.Caching.DataCacheLockHandle,System.String)">
      <summary>Replaces and unlocks an object in the specified region. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object after it has been updated by this method.</returns>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="value">The object to add or replace.</param>
      <param name="lockHandle">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle" /> object that was returned when the object was locked.</param>
      <param name="region">The name of the region the object resides in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.PutAndUnlock(System.String,System.Object,Microsoft.ApplicationServer.Caching.DataCacheLockHandle,System.TimeSpan)">
      <summary>Replaces and unlocks an object in the cache. Specifies the timeout value of the cached object.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object after it has been updated by this method.</returns>
      <param name="key">The unique value that is used to identify the object in the cache.</param>
      <param name="value">The object to add or replace.</param>
      <param name="lockHandle">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle" /> object that was returned when the object was locked.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.PutAndUnlock(System.String,System.Object,Microsoft.ApplicationServer.Caching.DataCacheLockHandle,System.TimeSpan,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag})">
      <summary>Replaces and unlocks an object in the cache. Specifies the timeout value and associates tags with the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object after it has been updated by this method.</returns>
      <param name="key">The unique value that is used to identify the object in the cache.</param>
      <param name="value">The object to add or replace.</param>
      <param name="lockHandle">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle" /> object that was returned when the object was locked.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
      <param name="tags">A list of tags to associate with the object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.PutAndUnlock(System.String,System.Object,Microsoft.ApplicationServer.Caching.DataCacheLockHandle,System.TimeSpan,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag},System.String)">
      <summary>Replaces and unlocks an object in the specified region. Specifies the timeout value and associates tags with the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object after it has been updated by this method.</returns>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="value">The object to add or replace.</param>
      <param name="lockHandle">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle" /> object that was returned when the object was locked.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
      <param name="tags">A list of tags to associate with the object.</param>
      <param name="region">The name of the region the object resides in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.PutAndUnlock(System.String,System.Object,Microsoft.ApplicationServer.Caching.DataCacheLockHandle,System.TimeSpan,System.String)">
      <summary>Replaces and unlocks an object in the specified region. Specifies the timeout value of the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object after it has been updated by this method. The timeout value associated with the cached object overrides expiration settings specified in the named cache configuration.</returns>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="value">The object to add or replace.</param>
      <param name="lockHandle">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle" /> object that was returned when the object was locked.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
      <param name="region">The name of the region the object resides in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Remove(System.String)">
      <summary>Removes an object from the cache.</summary>
      <returns>True if the object identified by the key is removed; otherwise, false.</returns>
      <param name="key">The unique value that is used to identify the object in the region.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Remove(System.String,Microsoft.ApplicationServer.Caching.DataCacheItemVersion)">
      <summary>Removes an object with a specific version from the cache.</summary>
      <returns>True if the object is removed; otherwise, false.</returns>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="version">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object that is to be removed.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Remove(System.String,Microsoft.ApplicationServer.Caching.DataCacheItemVersion,System.String)">
      <summary>Removes an object with a specific version from the cache in a region. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>True if the object is removed; otherwise, false.</returns>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="version">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object that represents the version of the cached object that is to be removed.</param>
      <param name="region">The name of the region the object resides in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Remove(System.String,Microsoft.ApplicationServer.Caching.DataCacheLockHandle)">
      <summary>Removes an object from the cache with an associated lock handle.</summary>
      <returns>True if an object from the cache was removed; otherwise, false.</returns>
      <param name="key">The unique value that is used to identify the object in the cache.</param>
      <param name="lockHandle">The object that was returned when the object is removed.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Remove(System.String,Microsoft.ApplicationServer.Caching.DataCacheLockHandle,System.String)">
      <summary>Removes an object from the cache with an associated lock handle in a region. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>True if the object is removed; otherwise, false.</returns>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="lockHandle">The object that was returned when the object was removed.</param>
      <param name="region">The name of the region the object resides in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Remove(System.String,System.String)">
      <summary>Removes an object from the cache in a region. This overload is not supported in Windows Azure Shared Caching.</summary>
      <returns>True if the object is removed; otherwise, false.</returns>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="region">The name of the region the object resides in.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.RemoveCallback(Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor)">
      <summary>Removes a cache notification callback. Not supported in Windows Azure Shared Caching.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.RemoveRegion(System.String)">
      <summary>Deletes a region. All cached objects inside the region are also removed. Not supported in Windows Azure Shared Caching.</summary>
      <returns>Returns true if the region is removed. Returns false if the region does not exist.</returns>
      <param name="region">The name of the region.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.ResetObjectTimeout(System.String,System.TimeSpan)">
      <summary>Resets the object timeout value, defining how long objects reside in the cache before expiring. The value specified for the object overrides the default settings for the cache.</summary>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="newTimeout">The amount of time that the object should reside in the cache before expiration.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.ResetObjectTimeout(System.String,System.TimeSpan,System.String)">
      <summary>Resets the object timeout value, defining how long objects reside in the region before expiring. The value specified for the object overrides the default settings for the cache.</summary>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="newTimeout">The amount of time that the object should reside in the cache before expiration.</param>
      <param name="region">The name of the region where the object resides.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Unlock(System.String,Microsoft.ApplicationServer.Caching.DataCacheLockHandle)">
      <summary>Releases objects locked in the cache. This method supports pessimistic concurrency by making sure that the appropriate DataCacheLockHandle is used for unlocking the object.</summary>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="lockHandle">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle" /> object that was returned when the object was locked</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Unlock(System.String,Microsoft.ApplicationServer.Caching.DataCacheLockHandle,System.String)">
      <summary>Releases objects locked in the specified region. This method supports pessimistic concurrency by making sure that the appropriate DataCacheLockHandle is used for unlocking the object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="lockHandle">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle" /> object that was returned when the object was locked.</param>
      <param name="region">The name of the region where the object resides.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Unlock(System.String,Microsoft.ApplicationServer.Caching.DataCacheLockHandle,System.TimeSpan)">
      <summary>Releases objects locked in the cache. This method supports pessimistic concurrency by making sure that the appropriate DataCacheLockHandle is used for unlocking the object. Specifies a new timeout value for the cached object.</summary>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="lockHandle">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle" /> object that was returned when the object was locked.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCache.Unlock(System.String,Microsoft.ApplicationServer.Caching.DataCacheLockHandle,System.TimeSpan,System.String)">
      <summary>Releases objects locked in the specified region. This method supports pessimistic concurrency by making sure that the appropriate DataCacheLockHandle is used for unlocking the object. Specifies a new timeout value for the cached object. This overload is not supported in Windows Azure Shared Caching.</summary>
      <param name="key">The unique value that is used to identify the object in the region.</param>
      <param name="lockHandle">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle" /> object that was returned when the object was locked.</param>
      <param name="timeout">The amount of time that the object should reside in the cache before expiration.</param>
      <param name="region">The name of the region where the object resides.</param>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheAutoDiscoverProperty">
      <summary>Specifies whether automatic discovery is enabled for the role or endpoint that hosts Windows Azure Caching.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheAutoDiscoverProperty.#ctor(System.Boolean)">
      <summary>Creates a new DataCacheAutoDiscoverProperty object.</summary>
      <param name="enable">A value of true enables automatic discovery.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheAutoDiscoverProperty.#ctor(System.Boolean,System.String)">
      <summary>Creates a new DataCacheAutoDiscoverProperty object; specifies the web role, worker role or endpoint that hosts Caching.</summary>
      <param name="enable">A value of true enables automatic discovery.</param>
      <param name="identifier">The name of the web role, worker role or endpoint that hosts Caching in the cloud service deployment.</param>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheBulkNotificationCallback">
      <summary>Specifies a callback which is called with a list of cache operations. Not supported in Windows Azure Shared Caching.</summary>
      <param name="cacheName">The name of the cache where the object is stored.</param>
      <param name="operations">A list of <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor" /> cache operations.</param>
      <param name="nd">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor" /> associated with the cache notification that invoked the delegate method.</param>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheFactory">
      <summary>Provides methods to return <see cref="T:Microsoft.ApplicationServer.Caching.DataCache" /> objects that are mapped to a named cache. This class also enables programmatic configuration of the cache client.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheFactory.#ctor">
      <summary>Used for configuring a cache client based on the application configuration file.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheFactory.#ctor(Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration)">
      <summary>Used for programmatically configuring a cache client.</summary>
      <param name="configuration">A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration" /> object that specifies the cache client configuration settings for the DataCacheFactory instance.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheFactory.Dispose">
      <summary>Closes the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheFactory" /> object and releases all associated resources.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheFactory.GetCache(System.String)">
      <summary>Returns the cache client, an instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCache" /> object.</summary>
      <returns>The cache client, an instance of the DataCache object.</returns>
      <param name="cacheName">The named cache to use for cache operations. For , the cache name must be “default”, which is the same as calling <see cref="M:Microsoft.ApplicationServer.Caching.DataCacheFactory.GetDefaultCache" />.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheFactory.GetDefaultCache">
      <summary>Returns the cache client corresponding to the default cache, an instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCache" /> object.</summary>
      <returns>The cache client, an instance of the DataCache object.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration">
      <summary>Specifies the configuration settings for a new cache client.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration" /> class.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration" /> class. This overload specifies a specific name for the cache client.</summary>
      <param name="clientName">The name of the cache client. If the application configuration file specifies multiple named dataCacheClient sections, this parameter can be used to specify which dataCacheClient section to use.</param>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.AutoDiscoverProperty">
      <summary>Specifies whether automatic discovery is enabled for the role or endpoint that hosts Windows Azure Caching.</summary>
      <returns>Returns <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheAutoDiscoverProperty" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.CacheReadyRetryPolicy">
      <summary>Specifies the retry policy for the cache client.</summary>
      <returns>Returns <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheReadyRetryPolicy" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.ChannelOpenTimeout">
      <summary>Gets or sets the length of time that the cache client waits to establish a network connection with the server.</summary>
      <returns>A <see cref="T:System.TimeSpan" /> object..</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.Clone">
      <summary>Create a duplicate DataCacheFactoryConfiguration object.</summary>
      <returns>Returns a<see cref="T:System.Object" /> that can be cast to a <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration" /> object.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.CreateNamedConfiguration(System.String,Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration,System.Boolean)">
      <summary>Create a new cache client configuration.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
      <param name="clientName">The name of the new cache client configuration.</param>
      <param name="config">A DataCacheFactoryConfiguration object that contains the settings for the new client configuration.</param>
      <param name="useConnectionPool">A value of true enables connection pooling.</param>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.DataCacheServiceAccountType">
      <summary>The type of account that runs the Caching service.</summary>
      <returns>Returns <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheServiceAccountType" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.IsCompressionEnabled">
      <summary>Specifies whether compression is enabled. A value of true enables compression. The default is false.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.LocalCacheProperties">
      <summary>Gets or sets the local cache settings for the cache client.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLocalCacheProperties" /> object.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.MaxConnectionsToServer">
      <summary>Specifies the maximum number of channels to open to the cache cluster.</summary>
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.NotificationProperties">
      <summary>Gets or sets the notification settings for the cache client. Not supported in Windows Azure Shared Caching.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationProperties" /> object.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.RemoveNamedConfiguration(System.String)">
      <summary>Removes a cache client configuration and its settings from memory.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
      <param name="clientName">The name of the cache client configuration.</param>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.RequestTimeout">
      <summary>Gets or sets the length of time that the cache client waits for a response from the server for each request.</summary>
      <returns>A <see cref="T:System.TimeSpan" />object.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.SecurityProperties">
      <summary>Gets or sets the security properties for the cache client.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheSecurity" /> object.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.SerializationProperties">
      <summary>Specifies the type of serialization to use.</summary>
      <returns>Returns <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheSerializationProperties" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.Servers">
      <summary>Gets or sets an array of <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheServerEndpoint" /> objects.</summary>
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.TransportProperties">
      <summary>Gets or sets the transport properties for the cache client.</summary>
      <returns>A <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheTransportProperties" />object.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheFactoryConfiguration.UseLegacyProtocol">
      <summary>A value of true specifies that the legacy protocol should be used for connecting to the cache.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheFailureNotificationCallback">
      <summary>Specifies the parameters required for a method to be invoked by a failure notification when the cache client misses cache notifications. Not supported in Windows Azure Shared Caching.</summary>
      <param name="cacheName">The name of the cache associated with the missing notifications.</param>
      <param name="nd">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor" /> object associated with the failure notification that invoked the delegate method.</param>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheLocalCacheProperties">
      <summary>Specifies the local cache settings for a cache client.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheLocalCacheProperties.#ctor">
      <summary>Creates a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLocalCacheProperties" /> class.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheLocalCacheProperties.#ctor(System.Int64,System.TimeSpan,Microsoft.ApplicationServer.Caching.DataCacheLocalCacheInvalidationPolicy)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLocalCacheProperties" /> class. This overloaded constructor accepts parameters that configure the properties of local cache.</summary>
      <param name="objectCount">The maximum number of objects in the local cache.</param>
      <param name="defaultTimeout">The time that objects reside in the local cache before invalidation.</param>
      <param name="invalidationPolicy">The invalidation policy for the local cache. This can be set to either <see cref="F:Microsoft.ApplicationServer.Caching.DataCacheLocalCacheInvalidationPolicy.NotificationBased" /> or <see cref="F:Microsoft.ApplicationServer.Caching.DataCacheLocalCacheInvalidationPolicy.TimeoutBased" />.</param>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheLocalCacheProperties.DefaultTimeout">
      <summary>Gets the default timeout for items in the local cache.</summary>
      <returns>Returns <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheLocalCacheProperties.InvalidationPolicy">
      <summary>Gets the invalidation policy for items in the local cache.</summary>
      <returns>Returns <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheLocalCacheInvalidationPolicy" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheLocalCacheProperties.IsEnabled">
      <summary>Gets a Boolean value indicating whether local cache is enabled for the cache client.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheLocalCacheProperties.ObjectCount">
      <summary>Gets the maximum number of objects permitted in the local cache.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationCallback">
      <summary>Represents a callback method that is to be invoked by a cache notification when one or more cache operations take place. Applies only to the server versions of AppFabric.</summary>
      <param name="regionName">The name of the region associated with the cache operation. A zero-length string indicates that a specific region is not associated with the cache operation.</param>
      <param name="key">The name of the key associated with the cache operation. A zero-length string indicates that a specific cached item is not associated with the cache operation.</param>
      <param name="version">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> of the cached object associated with the cache operation that triggered the notification. A null version indicates that a specific cached item is not associated with the cache operation.</param>
      <param name="cacheOperation">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheOperations" /> enumeration specifying which cache event triggered the cache notification.</param>
      <param name="nd">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor" /> object associated with the cache notification that invoked the delegate method.</param>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor">
      <summary>Identifies a cache notification callback. This identifier is required to remove the corresponding cache notification callback. Applies only to the server versions of AppFabric.</summary>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor.CacheName">
      <summary>The name of the cache triggering the cache notification.</summary>
      <returns>A string value represents the name of the cache triggering the cache notification.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor.DelegateId">
      <summary>Identifier for the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor" /> object. Used to distinguish between DataCacheNotificationDescriptor objects.</summary>
      <returns>A <see cref="T:System.Int64" /> value used to identify DataCacheNotificationDescriptor objects.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor.ToString">
      <summary>Creates a copy of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor" /> object.</summary>
      <returns>A string value representing the <see cref="P:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor.CacheName" /> and <see cref="P:Microsoft.ApplicationServer.Caching.DataCacheNotificationDescriptor.DelegateID" /> property values.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationProperties">
      <summary>Specifies the notification settings for a cache client. Applies only to the server versions of AppFabric.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheNotificationProperties.#ctor(System.Int64,System.TimeSpan)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheNotificationProperties" /> class.</summary>
      <param name="maxQueueLength">The maximum queue length for stored notifications on the cache cluster.</param>
      <param name="pollInterval">The frequency with which the cache client communicates with the server to check for notifications.</param>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheNotificationProperties.MaxQueueLength">
      <summary>Gets the maximum queue length for notifications on the cache cluster.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheNotificationProperties.PollInterval">
      <summary>Gets the polling interval that the cache client uses to check for notifications on the cache cluster.</summary>
      <returns>Returns <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheReadyRetryPolicy">
      <summary>Controls the retry policy that is used at startup of a Windows Azure cloud service.</summary>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheReadyRetryPolicy.MaximumRetryIntervalInSeconds">
      <summary>The maximum number of seconds between retries.</summary>
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheReadyRetryPolicy.RetryCount">
      <summary>The number of retry attempts.</summary>
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheServerEndpoint">
      <summary>Used to specify an individual cache host when programmatically configuring the cache client.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheServerEndpoint.#ctor(System.String,System.Int32)">
      <summary>Used to define a cache host in the cache cluster for programmatic configuration of the cache client.</summary>
      <param name="hostName">The computer name of the cache server or the service URL.</param>
      <param name="cachePort">The cache port number of the cache host.</param>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheServerEndpoint.CachePort">
      <summary>The cache port on the cache server.</summary>
      <returns>A <see cref="T:System.Int32" /> value that represents the cache port number on the cache server.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheServerEndpoint.Equals(System.Object)">
      <summary>Returns true if the DataCacheServerEndpoint objects are equal.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
      <param name="obj">The DataCacheServerEndpoint object to compare.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheServerEndpoint.GetHashCode">
      <summary>Returns the hash code for the DataCacheServerEndpoint.</summary>
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheServerEndpoint.HostName">
      <summary>The computer name or service URL of the cache server.</summary>
      <returns>A string representing the computer name or service URL of the cache server.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider">
      <summary>A session storage provider that enables Web applications to store session-state data to a distributed cache system.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider" /> class.</summary>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider.ApplicationName">
      <summary>The application name. This is used to differentiate sessions in the data source by application.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider.CreateNewStoreData(System.Web.HttpContext,System.Int32)">
      <summary>Creates a new data storage object for the current request.</summary>
      <returns>A SessionStateStoreData object, used for storing session data in the distributed cache.</returns>
      <param name="context">The HttpContext for the current request.</param>
      <param name="timeout">The session state Timeout value for the new SessionStateStoreData object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider.CreateUninitializedItem(System.Web.HttpContext,System.String,System.Int32)">
      <summary>Adds a new session state to the distributed cache. </summary>
      <param name="context">The HttpContext for the current request.</param>
      <param name="id">The session identifier for the current request.</param>
      <param name="timeout">The session Timeout for the current request.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider" /> class.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider.EndRequest(System.Web.HttpContext)">
      <summary>Called by the SessionStateModule class at the end of a request</summary>
      <param name="context">The HttpContext for the current request.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider.GetItem(System.Web.HttpContext,System.String,System.Boolean@,System.TimeSpan@,System.Object@,System.Web.SessionState.SessionStateActions@)">
      <summary>Returns read-only session-state data from the cache.</summary>
      <returns>A SessionStateStoreData object populated with session values and information from the cache</returns>
      <param name="context">The HttpContext of the current request.</param>
      <param name="id">The session identifier for the current request.</param>
      <param name="locked">When this method returns, contains a Boolean value that is set to true if the requested session item is locked in the cache; otherwise, false.</param>
      <param name="lockAge">When this method returns, contains a TimeSpan object that is set to the period of time that an item in the cache has been locked.</param>
      <param name="lockId">When this method returns, contains an object that is set to the lock identifier for the current request.</param>
      <param name="actions">When this method returns, contains one of the SessionStateActions values. This indicates whether the current session is an uninitialized, cookieless session.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider.GetItemExclusive(System.Web.HttpContext,System.String,System.Boolean@,System.TimeSpan@,System.Object@,System.Web.SessionState.SessionStateActions@)">
      <summary>Returns and locks the read-only session-state data from the cache.</summary>
      <returns>A SessionStateStoreData object populated with session values and information from the cache.</returns>
      <param name="context">The HttpContext of the current request.</param>
      <param name="id">The session identifier for the current request.</param>
      <param name="locked">When this method returns, contains a Boolean value that is set to true if a lock in the cache is successfully obtained; otherwise, false.</param>
      <param name="lockAge">When this method returns, contains a TimeSpan object that is set to the period of time that an item in the cache has been locked.</param>
      <param name="lockId">When this method returns, contains an object that is set to the lock identifier for the current request.</param>
      <param name="actions">When this method returns, contains one of the SessionStateActions values. This indicates whether the current session is an uninitialized, cookieless session.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider.Initialize(System.String,System.Collections.Specialized.NameValueCollection)">
      <summary>Initializes the distributed cache provider.</summary>
      <param name="name">The name of the application.</param>
      <param name="config">The configuration details specified in the application configuration file for cacheName and regionName.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider.InitializeRequest(System.Web.HttpContext)">
      <summary>Initializes the outgoing HTTP request.</summary>
      <param name="context">The context for the current request.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider.ReleaseItemExclusive(System.Web.HttpContext,System.String,System.Object)">
      <summary>Releases a lock on the session data in the cache.</summary>
      <param name="context">The HttpContext of the current request.</param>
      <param name="id">The session identifier of the current request.</param>
      <param name="lockId">The lock identifier of the current request.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider.RemoveItem(System.Web.HttpContext,System.String,System.Object,System.Web.SessionState.SessionStateStoreData)">
      <summary>Deletes session data from the cache.</summary>
      <param name="context">The HttpContext of the current request.</param>
      <param name="id">The session identifier of the current request.</param>
      <param name="lockId">The lock identifier of the current request.</param>
      <param name="item">The SessionStateStoreData that represents the item to delete from the cache.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider.ResetItemTimeout(System.Web.HttpContext,System.String)">
      <summary>Updates the expiration date and time of session data in the cache.</summary>
      <param name="context">The context for the current request.</param>
      <param name="id">The session identifier of the current request.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider.SetAndReleaseItemExclusive(System.Web.HttpContext,System.String,System.Web.SessionState.SessionStateStoreData,System.Object,System.Boolean)">
      <summary>Updates the session item information in the cache with values from the current request and clears the lock on the session item.</summary>
      <param name="context">The HttpContext of the current request.</param>
      <param name="id">The session identifier of the current request..</param>
      <param name="item">The SessionStateStoreData object that contains the current session values to be stored.</param>
      <param name="lockId">The lock identifier for the current request.</param>
      <param name="newItem">If true, identifies the session item as a new item if true; otherwise, identifies the session item as an existing item.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSessionStoreProvider.SetItemExpireCallback(System.Web.SessionState.SessionStateItemExpireCallback)">
      <summary>Sets a reference to the SessionStateItemExpireCallback delegate for the Session_OnEnd event defined in the Global.asax file.</summary>
      <returns>Cache does not support notification of the expiration; therefore this method always returns false.</returns>
      <param name="expireCallback">The SessionStateItemExpireCallback delegate for the Session_OnEnd event defined in the Global.asax file.</param>
    </member>
  </members>
</doc>