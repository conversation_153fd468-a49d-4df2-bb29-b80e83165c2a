<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Practices.EnterpriseLibrary.Common</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.TimeSpanOrInfiniteConverter">
            <summary>
            Converts a <see cref="T:System.TimeSpan"/> expressed in as formatted string or as a standard infinite timeout.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.TimeSpanOrInfiniteConverter.Infinite">
            <summary>
            The string representation of an infinite timeout.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.TimeSpanOrInfiniteConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts the given object to a <see cref="T:System.TimeSpan" />.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
            <param name="culture">An optional <see cref="T:System.Globalization.CultureInfo" />. If not supplied, the current culture is assumed.</param>
            <param name="value">The <see cref="T:System.Object" /> to convert.</param>
            <returns>
            An <see cref="T:System.Object" /> that represents the converted value.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.TimeSpanOrInfiniteConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <summary>
            Converts the given object to another type.
            </summary>
            <param name="context">A formatter context.</param>
            <param name="culture">The culture into which <paramref name="value" /> will be converted.</param>
            <param name="value">The object to convert.</param>
            <param name="destinationType">The type to convert the object to.</param>
            <returns>
            The converted object.
            </returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NonNegativeOrInfiniteTimeSpanValidator">
            <summary>
            Provides validation for a <see cref="T:System.TimeSpan"/> object allowing non-negative spans and 
            the value for <see cref="F:System.Threading.Timeout.InfiniteTimeSpan"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NonNegativeOrInfiniteTimeSpanValidator.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NonNegativeOrInfiniteTimeSpanValidator"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NonNegativeOrInfiniteTimeSpanValidator.Validate(System.Object)">
            <summary>
            Determines whether the value of an object is valid.
            </summary>
            <param name="value">The value of an object.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NonNegativeOrInfiniteTimeSpanValidatorAttribute">
            <summary>
            Declaratively instructs the .NET Framework to perform time validation on a configuration property. This class cannot be inherited.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NonNegativeOrInfiniteTimeSpanValidatorAttribute.ValidatorInstance">
            <summary>
            Gets the validator attribute instance.
            </summary>
            <returns>The current <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NonNegativeOrInfiniteTimeSpanValidator"/>.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.AppDomainNameFormatter">
            <summary>
            Provides the friendly name of the application domain as the prefix in formatting a 
            particular instance of a performance counter.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.IPerformanceCounterNameFormatter">
            <summary>
            Provides a pluggable way to format the name given to a particular instance of a performance counter.
            Each instance of a performance counter in Enterprise Library is given a name of the format
            "Name prefix - counter name"
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.IPerformanceCounterNameFormatter.CreateName(System.String)">
            <summary>
            Creates the formatted instance name for a performance counter, providing the prefix for the
            instance.
            </summary>
            <param name="nameSuffix">Performance counter name, as defined during installation of the counter</param>
            <returns>Formatted instance name in form of "prefix - nameSuffix"</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.AppDomainNameFormatter.#ctor">
            <summary>
            Creates an instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.AppDomainNameFormatter"/>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.AppDomainNameFormatter.#ctor(System.String)">
            <summary>
            Creates an instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.AppDomainNameFormatter"/> with an Application Instance Name
            </summary>
            <param name="applicationInstanceName"></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.AppDomainNameFormatter.CreateName(System.String)">
            <summary>
            Creates the formatted instance name for a performance counter, providing the Application
            Domain friendly name for the prefix for the instance.
            </summary>
            <param name="nameSuffix">Performance counter name, as defined during installation of the counter</param>
            <returns>Formatted instance name in form of "appDomainFriendlyName - nameSuffix"</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounter">
            <summary>
            Provides a virtual performance counter interface that enables an application to maintain both individually 
            named counter instances and a single counter total instance.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounter.#ctor(System.String,System.String)">
            <summary>
            Initializes a single performance counter instance named "Total".
            </summary>
            <param name="counterCategoryName">Performance counter category name, as defined during installation.</param>
            <param name="counterName">Performance counter name, as defined during installation.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounter.#ctor(System.String,System.String,System.String[])">
            <summary>
            Initializes multiple instances of performance counters to be managed by this object. 
            </summary>
            <param name="counterCategoryName">Performance counter category name, as defined during installation.</param>
            <param name="counterName">Performance counter name, as defined during installation.</param>
            <param name="instanceNames">Instance names to be managed.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounter.#ctor(System.Diagnostics.PerformanceCounter[])">
            <summary>
            Initializes this object with performance counters that are created externally. It is the responsibility of the external
            counter factory to create an instance for the "Total" counter.
            </summary>
            <param name="counters">Array of already initialized <see cref="T:System.Diagnostics.PerformanceCounter"></see> objects to be managed 
            by this instance.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounter.Clear">
            <summary>
            Clears the raw count associated with all managed performance counters
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounter.Increment">
            <summary>
            Increments each performance counter managed by this instance.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounter.IncrementBy(System.Int64)">
            <summary>
            Increments each performance counter managed by this instance by the given <paramref name="value"/>.
            </summary>
            <param name="value">Amount by which to increment each counter.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounter.GetValueFor(System.String)">
            <summary>
            Gets the current value of the given performance counter instance.
            </summary>
            <param name="instanceName">Instance name of counter whose value will be retrieved.</param>
            <returns>Value of the given performance counter.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounter.SetValueFor(System.String,System.Int64)">
            <summary>
            Sets the value of the given performance counter instance.
            </summary>
            <param name="instanceName">Instance name of counter whose value will be set.</param>
            <param name="value">Value to set the given instance to.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounter.InstantiateCounter(System.String)">
            <summary>
            Initializes a performance counter, giving it the specified <paramref name="instanceName"></paramref>.
            </summary>
            <param name="instanceName">Instance name to be given to the instantiated <see cref="T:System.Diagnostics.PerformanceCounter"></see></param>.
            <returns>An initialized <see cref="T:System.Diagnostics.PerformanceCounter"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounter.Increment(System.String)">
            <summary>
            Increments the associated performance counters by one.
            </summary>
            <param name="instanceName">The instance to be incremented.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounter.Counters">
            <summary>
            Gets the list of performance counter instances managed by this object.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounter.Value">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.HasInstallableResourcesAttribute">
            <summary>
            Defines those classes and structs that have some sort of resources that need to be installed.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterAttribute">
            <summary>
            Defines a <see cref="T:System.Diagnostics.PerformanceCounter"></see>. Used by the reflection-based installers to 
            prepare a performance counter for installation.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterAttribute.#ctor(System.String,System.String,System.Diagnostics.PerformanceCounterType)">
            <summary>
            Initializes this object with all data needed to install a <see cref="T:System.Diagnostics.PerformanceCounter"></see>.
            </summary>
            <param name="counterName">Performance counter name.</param>
            <param name="counterHelp">Name of Help resource string. This is not the help text itself, 
            but is the resource name used to look up the internationalized help text at install-time.</param>
            <param name="counterType">Performance Counter type.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterAttribute.HasBaseCounter">
            <summary>
            Used to determine if the counter being installed has a base counter associated with it.
            </summary>
            <returns>True if counter being installed has a base counter associated with it.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterAttribute.CounterType">
            <summary>
            Gets the <see cref="T:System.Diagnostics.PerformanceCounter"></see> type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterAttribute.CounterHelp">
            <summary>
            Get the name of Help resource string. This is not the help text itself, 
            but is the resource name used to look up the internationalized help text at install-time.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterAttribute.CounterName">
            <summary>
            Gets the <see cref="T:System.Diagnostics.PerformanceCounter"></see> name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterAttribute.BaseCounterType">
            <summary>
            Gets and sets the base <see cref="T:System.Diagnostics.PerformanceCounter"></see> type. This is an optional 
            property used when the counter being defined requires a base counter to operate, such as for 
            averages.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterAttribute.BaseCounterHelp">
            <summary>
            Gets and sets the base <see cref="T:System.Diagnostics.PerformanceCounter"></see> help resource name. 
            This is not the help text itself, 
            but is the resource name used to look up the internationalized help text at install-time.
            This is an optional 
            property used when the counter being defined requires a base counter to operate, such as for 
            averages.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterAttribute.BaseCounterName">
            <summary>
            Gets and sets the base <see cref="T:System.Diagnostics.PerformanceCounter"></see> name. This is an optional 
            property used when the counter being defined requires a base counter to operate, such as for 
            averages.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.NoPrefixNameFormatter">
            <summary>
            Provides a pluggable way to format the name given to a particular instance of a performance counter.
            This class does no formatting, returning the provided name suffix as the counter name.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.NoPrefixNameFormatter.CreateName(System.String)">
            <summary>
            Returns the given <paramref name="nameSuffix"></paramref> as the created name.
            </summary>
            <param name="nameSuffix">Performance counter name, as defined during installation of the counter</param>
            <returns>Formatted instance name in form of "<paramref name="nameSuffix"/>"</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.CannotCreateDesignSource">
            <summary>
              Looks up a localized string similar to Cannot create design source for rootSource other than DesignConfigurationSource..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ConfigurationSourceInvalidTypeErrorMessage">
            <summary>
              Looks up a localized string similar to The type &apos;{0}&apos; defined in the &apos;{1}&apos; configuration source is invalid.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.EventLogEntryExceptionTemplate">
            <summary>
              Looks up a localized string similar to The exception that occured was: {0}.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.EventLogEntryHeaderTemplate">
            <summary>
              Looks up a localized string similar to An error occurred in application {0} in the {1}..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionArgumentShouldDeriveFromIDictionary">
            <summary>
              Looks up a localized string similar to The type &apos;{0}&apos; does not derive from IDictionary..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionArgumentShouldDeriveFromIList">
            <summary>
              Looks up a localized string similar to The type &apos;{0}&apos; does not derive from IList..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionAssemblerAttributeNotSet">
            <summary>
              Looks up a localized string similar to The [Assembler] attribute is not set in the configuration object type {0}..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionAssemblerTypeNotCompatible">
            <summary>
              Looks up a localized string similar to The assembler configured for type {0} has type {2} which is not compatible with type {1}..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionBaseConfigurationSourceElementIsInvalid">
            <summary>
              Looks up a localized string similar to The base ConfigurationSourceElement configuration type can not be used as a concrete configuration element..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionCannotAddParametersAfterDispose">
            <summary>
              Looks up a localized string similar to Cannot add new paramters after Finish() or Dispose()..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionCanNotConvertType">
            <summary>
              Looks up a localized string similar to The AssemblyQualifiedTypeNameConverter can only convert values of type &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionCannotFinish">
            <summary>
              Looks up a localized string similar to Builder has already added policies..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionConfigurationFileNotFound">
            <summary>
              Looks up a localized string similar to The section {0} could not be saved because the file does not exist..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionConfigurationLoadFileNotFound">
            <summary>
              Looks up a localized string similar to The configuration file {0} could not be found..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionConfigurationObjectIsNotCustomProviderData">
            <summary>
              Looks up a localized string similar to The configuration object for type &apos;{0}&apos; with name &apos;{1}&apos; has type &apos;{2}&apos; which is does not implement ICustomProviderData..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionConfigurationObjectWithTypeDoesNotHaveTypeSet">
            <summary>
              Looks up a localized string similar to The concrete type for polymorphic object named &apos;{1}&apos; in hierarchy {2} is not defined in configuration object {0}..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionConfigurationSectionPointsToNonExistingSource">
            <summary>
              Looks up a localized string similar to The configuration section &apos;{0}&apos; specifies Source &apos;{1}&apos;, which is not declared in the configuration sources section..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionConfigurationSourceNotFound">
            <summary>
              Looks up a localized string similar to A configuration source named &apos;{0}&apos; was not found in the {1} section..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionConfigurationSourceSectionNotFound">
            <summary>
              Looks up a localized string similar to The configuration source section is not found in the application configuration file..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionContainerPolicyCreatorAttributeNotPresent">
            <summary>
              Looks up a localized string similar to The required &quot;ContainerPolicyCreatorAttribute&quot; is not present in the supplied type &quot;{0}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionCustomFactoryAttributeNotFound">
            <summary>
              Looks up a localized string similar to The [CustomFactory] attribute was not found on type {0} while processing request for id &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionCustomProviderTypeDoesNotHaveTheRequiredConstructor">
            <summary>
              Looks up a localized string similar to Type &apos;{0}&apos; specified as a custom provider does not have the required public constructor with a single NameValueCollection parameter..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionEventRaisingFailed">
            <summary>
              Looks up a localized string similar to There was an error raising an event in .
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionFactoryMethodHasInvalidReturnType">
            <summary>
              Looks up a localized string similar to The method with signature {0} is not a valid factory method to build type {1}..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionIncompaitbleMergeElementType">
             <summary>
               Looks up a localized string similar to Cannot merge the configuration element.
            The local elements type &apos;{0}&apos; is  incompatible with the parent elements type &apos;{1}&apos;..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionInvalidType">
            <summary>
              Looks up a localized string similar to The type &apos;{0}&apos; cannot be resolved. Please verify the spelling is correct or that the full type name is provided..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionMustBeNameTypeConfigurationElement">
            <summary>
              Looks up a localized string similar to The supplied configuration object has type &apos;{0}&apos;, which is not a descendant of &apos;NameTypeConfigurationElement&apos; as required..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionMustHaveNoArgsConstructor">
            <summary>
              Looks up a localized string similar to The required zero argument constructor is not available for the supplied type &quot;{0}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionMustImplementIContainerPolicyCreator">
            <summary>
              Looks up a localized string similar to The required interface &quot;IContainerPolicyCreator&quot; is not implemented by the supplied type &quot;{0}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionNamedConfigurationNotFound">
            <summary>
              Looks up a localized string similar to The configuration could not be found for name &apos;{0}&apos; in factory {1}..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionNoConfigurationElementAttribute">
            <summary>
              Looks up a localized string similar to The type {0} does not contain the ConfigurationElementTypeAttribute..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionNoConfigurationObjectPolicySet">
            <summary>
              Looks up a localized string similar to No policy specifying the configuration source for the container has been set. The EnterpriseLibraryCoreExtension is probably missing..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionNoMatchingConstructorFound">
            <summary>
              Looks up a localized string similar to No public constructor with {1} arguments was found for type &quot;{0}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionNoMethodAnnotatedForInjectionFound">
            <summary>
              Looks up a localized string similar to The type {0} does not have a public method annotated as an injection target as required by the use of injection..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionNoSuitableFactoryMethodFound">
            <summary>
              Looks up a localized string similar to The type {0} does not have a static method with a TargetConstructorAttribuite suitable to create an object of type {1}..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionNoTypeAttribute">
            <summary>
              Looks up a localized string similar to The type attribute does not exist on the element {0}..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionParameterNotAnnotatedForInjection">
            <summary>
              Looks up a localized string similar to The parameter &apos;{0}&apos; for injection target &apos;{1}&apos; in type &apos;{2}&apos; is missing the injection interpretation attribute..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionPerformanceCounterRedefined">
            <summary>
              Looks up a localized string similar to The performance counter &apos;{0}&apos; in category &apos;{1}&apos; is redefined in type {2} with a different configuration..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionPolicyBuilderFinished">
            <summary>
              Looks up a localized string similar to Attempt to continue working with a PolicyBuilder after the policies have been added to a policy list for type &apos;{1}&apos; with key &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionPolicyBuilderStillWaitingForPropertyPolicy">
            <summary>
              Looks up a localized string similar to The specified policies cannot be added: a property policy mapping is still taking place..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionPropertyAccessExpressionNotPropertyAccess">
            <summary>
              Looks up a localized string similar to The supplied expression is not a valid property access expression: &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionPropertyNotFound">
            <summary>
              Looks up a localized string similar to e {2}..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionRedirectedConfigurationSectionNotFound">
            <summary>
              Looks up a localized string similar to Unable to find the redirected section {0} in the specified configuration source {1}..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionRegistrationServiceTypeIsNotCompatible">
            <summary>
              Looks up a localized string similar to Service type of {0} is not compatible with supplied expression type of {1}.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionRegistrationTypeExpressionMustBeNewLambda">
            <summary>
              Looks up a localized string similar to Lambda expression must construct a new instance of a type..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionRetrievalAttributeNotFound">
            <summary>
              Looks up a localized string similar to The type {0} does not contain the ConfigurationDataRetrievalAttribute required to resolve named references..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionShouldBeImplementedBySubclass">
            <summary>
              Looks up a localized string similar to Method must be overriden by subclass..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionSourcePropertyDoesNotExist">
            <summary>
              Looks up a localized string similar to Could not retrieve parameter value. The property {0} does not exist for type {1}..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionStringEmpty">
            <summary>
              Looks up a localized string similar to The argument cannot be empty..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionStringNullOrEmpty">
            <summary>
              Looks up a localized string similar to The value can not be null or string or empty..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionSuppliedCreationExpressionIsNotNewExpression">
            <summary>
              Looks up a localized string similar to A creation expression must be a constructor call, but the supplied expression was &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionSystemSourceNotDefined">
            <summary>
              Looks up a localized string similar to The system configuration source is not defined in the configuration file..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionTheSpecifiedDefaultProviderDoesNotExistInConfiguration">
            <summary>
              Looks up a localized string similar to The configuration object for default provider named &apos;{0}&apos; for type &apos;{1}&apos; was not found in the supplied list..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionTypeCouldNotBeCreated">
            <summary>
              Looks up a localized string similar to The type {0} from configuration could not be created..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionTypeDoesNotProvideCorrectConstructor">
            <summary>
              Looks up a localized string similar to Type does not provide a constructor taking a single parameter type of NameValueCollection.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionTypeMustDeriveFromType">
            <summary>
              Looks up a localized string similar to Type must be derived from &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionTypeMustImplementInterface">
            <summary>
              Looks up a localized string similar to Type must implement interface &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionTypeNotCustomFactory">
            <summary>
              Looks up a localized string similar to Type {0} is not an implementation of ICustomFactory for CustomFactoryAttribute..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionTypeNotNameMapper">
            <summary>
              Looks up a localized string similar to Type {0} is not an implementation of IConfigurationNameMapper for ConfigurationNameMapperAttribute..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionTypeNotRetriever">
            <summary>
              Looks up a localized string similar to Type {0} is not an implementation of IConfigurationDataRetriever for ConfigurationDataRetrievalAttribute..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionUnableToMatchConstructorToConfigurationObject">
            <summary>
              Looks up a localized string similar to Default policy creation failed: The properties in the supplied configuration object of type {0} cannot be matched to any constructor on type {1}..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionUnexpectedType">
            <summary>
              Looks up a localized string similar to The expected type &apos;{0}&apos; was not provided..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionUnknownResolveMethod">
            <summary>
              Looks up a localized string similar to An call to an unknown method named &apos;{0}&apos; in the Resolve class was found in the supplied argument expression: &apos;{1}&apos;. Cannot create policies for this expression..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionUnrecognizedContainerMarkerMethod">
            <summary>
              Looks up a localized string similar to Unrecognized Container marker method..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionUnrecognizedDependencyParameterType">
            <summary>
              Looks up a localized string similar to Unrecognized DependencyParameter type: {0}.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.ExceptionUnsupportedBindingExpressionType">
            <summary>
              Looks up a localized string similar to The initialization expression for property {0} is not supported: only simple bindings are supported..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.FileConfigurationSourceName">
            <summary>
              Looks up a localized string similar to File Configuration Source.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Properties.Resources.SystemConfigurationSourceName">
            <summary>
              Looks up a localized string similar to System Configuration Source.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSectionCloner">
            <summary>
            Clones a <see cref="T:System.Configuration.ConfigurationSection"/>.
            </summary>
            <remarks>
            This will perform a clone of a <see cref="T:System.Configuration.ConfigurationSection"/> by evaluating each element in
            the <see cref="P:System.Configuration.ElementInformation.Properties"/>.  If these properties are themselves <see cref="T:System.Configuration.ConfigurationElement"/> they will 
            be cloned as well.
            
            As <see cref="T:System.Configuration.ConfigurationElementCollection"/> items do not expose the ability to add and remove, any configuration collections
            must implement <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IMergeableConfigurationElementCollection"/> to be cloned.  If they do not implement this, they will be skipped
            during cloning. The enterprise library collections implement this interface and the cloner will properly handle the cloning 
            of <see cref="T:System.Configuration.ConnectionStringSettingsCollection"/> and <see cref="T:System.Configuration.KeyValueConfigurationCollection"/> with an internal wrapper that 
            implements <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IMergeableConfigurationElementCollection"/> for these collections.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSectionCloner.Clone(System.Configuration.ConfigurationSection)">
            <summary>
             Clones a <see cref="T:System.Configuration.ConfigurationSection"/>
            </summary>
            <param name="section">The <see cref="T:System.Configuration.ConfigurationSection"/> to clone.</param>
            <returns>A new, cloned <see cref="T:System.Configuration.ConfigurationSection"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSectionCloner.CloneElement(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement)">
            <summary>
            
            </summary>
            <param name="sourceElement"></param>
            <param name="targetElement"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler">
            <summary>
            Implements common behavior for classes that add extended functionality to <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> implementations.<br/>
            This class can create subordinate sources based on the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceSection"/> configuration and propagates change events 
            From these sources to the main source.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CompositeConfigurationSourceHandler"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.HierarchicalConfigurationSourceHandler"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler"/> passing the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> implementation
            That contains the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceSection"/> configuration.
            </summary>
            <param name="configurationSource">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> implementation that should be extended.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.CheckGetSection(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            Checks whether a call to <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.GetSection(System.String)"/> should be extended.<br/>
            If the call should be extended performs the extended behavior and returns the modified <see cref="T:System.Configuration.ConfigurationSection"/> intance.<br/>
            If the call should not be extended returns <paramref name="configurationSection"/>.
            </summary>
            <param name="sectionName">The name of the section that was retrieved from configuration.</param>
            <param name="configurationSection">The section that was retrieved from configuration.</param>
            <returns>The resulting <see cref="T:System.Configuration.ConfigurationSection"/> instance.</returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.GetSection(System.String)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.DoCheckGetSection(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            When overridden in a derived class, checks whether a call to <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.GetSection(System.String)"/> should be extended.<br/>
            If the call should be extended performs the extended behavior and returns the modified <see cref="T:System.Configuration.ConfigurationSection"/> intance.<br/>
            If the call should not be extended returns <paramref name="configurationSection"/>.
            </summary>
            <param name="sectionName">The name of the section that was retrieved from configuration.</param>
            <param name="configurationSection">The section that was retrieved from configuration.</param>
            <returns>The <see cref="T:System.Configuration.ConfigurationSection"/> instance passed as <paramref name="configurationSection"/>.</returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.GetSection(System.String)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.CheckAddSection(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            Checks whether a call to <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Add(System.String,System.Configuration.ConfigurationSection)"/> should be extended.<br/>
            If the call should be extended performs the extended behavior.
            </summary>
            <param name="sectionName">The name of the section that should be stored in configuration.</param>
            <param name="configurationSection">The section that should be stored in configuration.</param>
            <returns><see langword="true"/> if the call to <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Add(System.String,System.Configuration.ConfigurationSection)"/> was handled by the extension.</returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Add(System.String,System.Configuration.ConfigurationSection)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.DoCheckAddSection(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            When overridden in a derived class, checks whether a call to <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Add(System.String,System.Configuration.ConfigurationSection)"/> should be extended.<br/>
            If the call should be extended performs the extended behavior.
            </summary>
            <param name="sectionName">The name of the section that should be stored in configuration.</param>
            <param name="configurationSection">The section that should be stored in configuration.</param>
            <returns><see langword="false"/></returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Add(System.String,System.Configuration.ConfigurationSection)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.CheckRemoveSection(System.String)">
            <summary>
            Checks whether a call to <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Remove(System.String)"/> should be extended.<br/>
            If the call should be extended performs the extended behavior.
            </summary>
            <param name="sectionName">The name of the section that should be removed from configuration.</param>
            <returns><see langword="true"/> if the call to <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Remove(System.String)"/> was handled by the extension.</returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Remove(System.String)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.DoCheckRemoveSection(System.String)">
            <summary>
            When overridden in a derived class, checks whether a call to <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Remove(System.String)"/> should be extended.<br/>
            If the call should be extended performs the extended behavior.
            </summary>
            <param name="sectionName">The name of the section that should be removed from configuration.</param>
            <returns><see langword="false"/></returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Remove(System.String)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.Initialize">
            <summary>
            Performs intialization logic for this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.Refresh">
            <summary>
            Performs re-intialization logic for this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.DoRefresh">
            <summary>
            Perform required refresh actions as needed when source changes.
            </summary>
            <returns>Sequence of changed sections</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.DoConfigurationSourceChanged(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Raises the <see cref="E:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.ConfigurationSectionChanged"/> event.
            </summary>
            <param name="affectedSections">The names of the sections that are changed.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.DoConfigurationSectionChanged(System.String)">
            <summary>
            Raises the <see cref="E:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.ConfigurationSectionChanged"/> event.
            </summary>
            <param name="sectionName">The name of the section that was changed.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.AddCustomSubordinateSource(System.String,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Adds a subordinate <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> to the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler"/>.
            This <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> will not be refreshed or disposed.
            </summary>
            <param name="sourceName">The name under which the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> will be added.</param>
            <param name="configurationSource">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> that will be added.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.GetSubordinateSource(System.String)">
            <summary>
            Returns a subordinate <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> with the specified name.<br/>
            Throws <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException"/> if the source was not found.
            </summary>
            <param name="sourceName">The name of the source that should be returned.</param>
            <returns>The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> instance.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.EnsurePropagatingSectionChangeEvents(System.String,System.String)">
            <summary>
            Ensures <see cref="E:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.ConfigurationSourceChanged"/> events are raised for 
            Changes in a subordinate section.
            </summary>
            <param name="sourceName">The name of the subordinate configuration source that contains the section.</param>
            <param name="sectionName">The name of the section events should be propagated for.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.StopPropagatingSectionChangeEvents(System.String)">
            <summary>
            Stops raising <see cref="E:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.ConfigurationSourceChanged"/> events for 
            Changes in a subordinate section.
            </summary>
            <param name="sectionName">The name of the section events are propagated for.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.Dispose">
            <summary>
            Releases resources managed by this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="E:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.ConfigurationSectionChanged">
            <summary>
            Indicate that a mapped section from one of the subordinate <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/>'s was changed.
            </summary>
        </member>
        <member name="E:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler.ConfigurationSourceChanged">
            <summary>
            Indicate a subordinate <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/>'s was changed.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CompositeConfigurationSourceHandler">
            <summary>
            Implements compositional merge behavior to <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> implementations.<br/>
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CompositeConfigurationSourceHandler.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CompositeConfigurationSourceHandler"/>.
            </summary>
            <param name="mainConfigurationSource">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> instance that should be extended.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CompositeConfigurationSourceHandler.Initialize">
            <summary>
            Performs intialization logic for this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CompositeConfigurationSourceHandler"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CompositeConfigurationSourceHandler.DoRefresh">
            <summary>
            Performs re-intialization logic for this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CompositeConfigurationSourceHandler"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CompositeConfigurationSourceHandler.DoCheckGetSection(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            Checks whether the result of a call to <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.GetSection(System.String)"/> should be deferred to a subordinate source.<br/>
            If the call should be deferred, returns the <see cref="T:System.Configuration.ConfigurationSection"/> intance from the approriate source.<br/>
            If the call should not be deferred returns <paramref name="configurationSection"/>.
            </summary>
            <param name="sectionName">The name of the section that was retrieved from configuration.</param>
            <param name="configurationSection">The section that was retrieved from configuration.</param>
            <returns>The resulting <see cref="T:System.Configuration.ConfigurationSection"/> instance.</returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.GetSection(System.String)"/>
            <exception cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException">Thrown if a section does not exist in a registered source.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CompositeConfigurationSourceHandler.DoCheckAddSection(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            Checks whether a call to <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Add(System.String,System.Configuration.ConfigurationSection)"/> should be deferred to a subordinate source.<br/>
            If the call should be deferred, adds the <paramref name="configurationSection"/> to the appropriate source and returns <see langword="true"/>.<br/>
            If the call should not be deferred returns <see langword="true"/>.
            </summary>
            <param name="sectionName">The name of the section that should be added to configuration.</param>
            <param name="configurationSection">The section that should be added to configuration.</param>
            <returns><see langword="true"/> if the section was added in a subordinate source, otherwise <see langword="false"/>.</returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Add(System.String,System.Configuration.ConfigurationSection)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CompositeConfigurationSourceHandler.DoCheckRemoveSection(System.String)">
            <summary>
            Checks whether a call to <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Remove(System.String)"/> should be deferred to a subordinate source.<br/>
            If the call should be deferred, removes the section from the appropriate source and returns <see langword="true"/>.<br/>
            If the call should not be deferred returns <see langword="true"/>.
            </summary>
            <param name="sectionName">The name of the section that should be removed from configuration.</param>
            <returns><see langword="true"/> if the section was removed from a subordinate source, otherwise <see langword="false"/>.</returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Remove(System.String)"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceBuilder">
            <summary>
            Entry point that is used for programatically building up a configution source.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceBuilder">
            <summary>
            Defines a configuration source builder.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.IFluentInterface">
            <summary>
            Interface that is used to build fluent interfaces and hides methods declared by <see cref="T:System.Object"/> from IntelliSense.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.IFluentInterface.GetType">
            <summary>
            Redeclaration that hides the <see cref="M:System.Object.GetType"/> method from IntelliSense.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.IFluentInterface.GetHashCode">
            <summary>
            Redeclaration that hides the <see cref="M:System.Object.GetHashCode"/> method from IntelliSense.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.IFluentInterface.ToString">
            <summary>
            Redeclaration that hides the <see cref="M:System.Object.ToString"/> method from IntelliSense.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.IFluentInterface.Equals(System.Object)">
            <summary>
            Redeclaration that hides the <see cref="M:System.Object.Equals(System.Object)"/> method from IntelliSense.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceBuilder.AddSection(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            Adds a <see cref="T:System.Configuration.ConfigurationSection"/> to the builder.
            </summary>
            <param name="sectionName">Name of section to add.</param>
            <param name="section">Configuration section to add.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceBuilder.Contains(System.String)">
            <summary>
            Determines if a section name is contained in the builder.
            </summary>
            <param name="sectionName"></param>
            <returns>True if contained in the builder, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceBuilder.Get(System.String)">
            <summary>
            Returns a configuration section with the given name, if present in the builder.
            </summary>
            <param name="sectionName">Name of section to return.</param>
            <returns>A valid configuration section or null.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceBuilder.Get``1(System.String)">
            <summary>
             Returns a configuration section of type <typeparamref name="T"/>, if present in the builder.
            </summary>
            <param name="sectionName">Section name to retrieve</param>
            <typeparam name="T"><see cref="T:System.Configuration.ConfigurationSection"/> type to return.</typeparam>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceBuilder.UpdateConfigurationWithReplace(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Updates a configuration source replacing any existing sections with those 
            built up with the builder.
            </summary>
            <param name="source"></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceBuilder.AddSection(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            Adds a <see cref="T:System.Configuration.ConfigurationSection"/> to the builder.
            </summary>
            <param name="sectionName">Name of section to add.</param>
            <param name="configurationSection">Configuration section to add.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceBuilder.Contains(System.String)">
            <summary>
            Determines if a section name is contained in the builder.
            </summary>
            <param name="sectionName"></param>
            <returns>True if contained in the builder, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceBuilder.Get(System.String)">
            <summary>
            Returns a configuration section with the given name, if present in the builder.
            </summary>
            <param name="sectionName">Name of section to return.</param>
            <returns>A valid configuration section or null.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceBuilder.Get``1(System.String)">
            <summary>
             Returns a configuration section of type <typeparamref name="T"/>, if present in the builder.
            </summary>
            <param name="sectionName">Section name to retrieve</param>
            <typeparam name="T"><see cref="T:System.Configuration.ConfigurationSection"/> type to return.</typeparam>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceBuilder.UpdateConfigurationWithReplace(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Updates a configuration source replacing any existing sections with those 
            built up with the builder.
            </summary>
            <param name="source"></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceBuilder.ToString">
            <summary/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomConfigurationElementCollection`2">
            <summary>
            Represents a collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement"/> objects.
            </summary>
            <typeparam name="T">The type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement"/> object this collection contains.</typeparam>
            <typeparam name="TCustomElementData">The type used for Custom configuration elements in this collection.</typeparam>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElementCollection`2">
            <summary>
            Represents a collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement"/> objects.
            </summary>
            <typeparam name="T">The type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement"/> object this collection contains.</typeparam>
            <typeparam name="TCustomElementData">The type used for Custom configuration elements in this collection.</typeparam>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.PolymorphicConfigurationElementCollection`1">
            <summary>
            Represents the base class from which all implementations of polymorphic configuration collections must derive. 
            </summary>
            <typeparam name="T">The type contained in the collection.</typeparam>	
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedElementCollection`1">
            <summary>
            Represents a collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedConfigurationElement"/> objects.
            </summary>
            <typeparam name="T">A newable object that inherits from <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedConfigurationElement"/>.</typeparam>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IMergeableConfigurationElementCollection">
            <summary>
            Interface that allows a <see cref="T:System.Configuration.ConfigurationElementCollection"/> to be merged.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IMergeableConfigurationElementCollection.ResetCollection(System.Collections.Generic.IEnumerable{System.Configuration.ConfigurationElement})">
            <summary>
            Resets the elements in the <see cref="T:System.Configuration.ConfigurationElementCollection"/> to the <see cref="T:System.Configuration.ConfigurationElement"/>s passed as <paramref name="configurationElements"/>.
            </summary>
            <param name="configurationElements">The new contents of this <see cref="T:System.Configuration.ConfigurationElementCollection"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IMergeableConfigurationElementCollection.CreateNewElement(System.Type)">
            <summary>
            Creates a new <see cref="T:System.Configuration.ConfigurationElement"/> for the specifies <paramref name="configurationType"/>.
            </summary>
            <param name="configurationType">The type of <see cref="T:System.Configuration.ConfigurationElement"/> that should be created.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedElementCollection`1.ForEach(System.Action{`0})">
            <summary>
            Performs the specified action on each element of the collection.
            </summary>
            <param name="action">The action to perform.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedElementCollection`1.Get(System.Int32)">
            <summary>
            Gets the configuration element at the specified index location. 
            </summary>
            <param name="index">The index location of the <see name="T"/> to return. </param>
            <returns>The <see name="T"/> at the specified index. </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedElementCollection`1.Add(`0)">
            <summary>
            Add an instance of <typeparamref name="T"/> to the collection.
            </summary>
            <param name="element">An instance of <typeparamref name="T"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedElementCollection`1.Get(System.String)">
            <summary>
            Gets the named instance of <typeparamref name="T"/> from the collection.
            </summary>
            <param name="name">The name of the <typeparamref name="T"/> instance to retrieve.</param>
            <returns>The instance of <typeparamref name="T"/> with the specified key; otherwise, <see langword="null"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedElementCollection`1.Contains(System.String)">
            <summary>
            Determines if the name exists in the collection.
            </summary>
            <param name="name">The name to search.</param>
            <returns><see langword="true"/> if the name is contained in the collection; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedElementCollection`1.Remove(System.String)">
            <summary>
            Remove the named element from the collection.
            </summary>
            <param name="name">The name of the element to remove.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedElementCollection`1.Clear">
            <summary>
            Clear the collection.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedElementCollection`1.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection. 
            </summary>
            <returns>An enumerator that iterates through the collection.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedElementCollection`1.CreateNewElement">
            <summary>
            Creates a new instance of a <typeparamref name="T"/> object.
            </summary>
            <returns>A new <see cref="T:System.Configuration.ConfigurationElement"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedElementCollection`1.GetElementKey(System.Configuration.ConfigurationElement)">
            <summary>
            Gets the element key for a specified configuration element when overridden in a derived class. 
            </summary>
            <param name="element">The <see cref="T:System.Configuration.ConfigurationElement"/> to return the key for. </param>
            <returns>An <see cref="T:System.Object"/> that acts as the key for the specified <see cref="T:System.Configuration.ConfigurationElement"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.PolymorphicConfigurationElementCollection`1.Reset(System.Configuration.ConfigurationElement)">
            <summary>
            Resets the internal state of the <see cref="T:System.Configuration.ConfigurationElement"/> object, including the locks and the properties collections.
            </summary>
            <param name="parentElement">The parent node of the configuration element.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.PolymorphicConfigurationElementCollection`1.OnDeserializeUnrecognizedElement(System.String,System.Xml.XmlReader)">
            <summary>
            Called when an unknown element is encountered while deserializing the <see cref="T:System.Configuration.ConfigurationElement"/> object.
            </summary>
            <param name="elementName">The name of the element.</param>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> used to deserialize the element.</param>
            <returns><see langword="true"/> if the element was handled; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.PolymorphicConfigurationElementCollection`1.RetrieveConfigurationElementType(System.Xml.XmlReader)">
            <summary>
            When overriden in a class, get the configuration object for each <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement"/> object in the collection.
            </summary>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> that is deserializing the element.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.PolymorphicConfigurationElementCollection`1.CreateNewElement">
            <summary>
            Creates a new <see cref="T:System.Configuration.ConfigurationElement"/>. 
            </summary>
            <returns>A new <see cref="T:System.Configuration.ConfigurationElement"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.PolymorphicConfigurationElementCollection`1.CreateNewElement(System.String)">
            <summary>
            Creates a new named <see cref="T:System.Configuration.ConfigurationElement"/>.
            </summary>
            <param name="elementName">The name of the element to create.</param>
            <returns>A new <see cref="T:System.Configuration.ConfigurationElement"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.PolymorphicConfigurationElementCollection`1.Unmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)">
            <summary>
            Reverses the effect of merging configuration information from different levels of the configuration hierarchy.
            </summary>
            <param name="sourceElement">A <see cref="T:System.Configuration.ConfigurationElement"/> object at the current level containing a merged view of the properties.</param>
            <param name="parentElement">The parent <see cref="T:System.Configuration.ConfigurationElement"/> object of the current element, or a <see langword="null"/> reference (Nothing in Visual Basic) if this is the top level.</param>		
            <param name="saveMode">One of the <see cref="T:System.Configuration.ConfigurationSaveMode"/> values.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElementCollection`2.RetrieveConfigurationElementType(System.Xml.XmlReader)">
            <summary>
            Get the configuration object for each <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement"/> object in the collection.
            </summary>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> that is deserializing the element.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomConfigurationElementCollection`2.RetrieveConfigurationElementType(System.Xml.XmlReader)">
            <summary>
            Get the configuration object for each <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement"/> object in the collection.
            </summary>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> that is deserializing the element.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.AddApplicationSettingsTitle">
            <summary>
              Looks up a localized string similar to Add Application Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.AddConfigurationSourcesTitle">
            <summary>
              Looks up a localized string similar to Add Configuration Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.AddInstrumentationSettingsTitle">
            <summary>
              Looks up a localized string similar to Add Instrumentation Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.AppSettingsSectionMetadataDescription">
            <summary>
              Looks up a localized string similar to Application Settings .
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.AppSettingsSectionMetadataDisplayName">
            <summary>
              Looks up a localized string similar to Application Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.CategoryGeneral">
            <summary>
              Looks up a localized string similar to General.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.CategoryLocalization">
            <summary>
              Looks up a localized string similar to Localization.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.CategoryName">
            <summary>
              Looks up a localized string similar to (name).
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationElementManageabilityProviderDataDescription">
            <summary>
              Looks up a localized string similar to A manageability provider for a configuration element..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationElementManageabilityProviderDataDisplayName">
            <summary>
              Looks up a localized string similar to Configuration Element Manageability Provider.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationElementManageabilityProviderDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Configuration Element Manageability Provider..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationElementManageabilityProviderDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationElementManageabilityProviderDataTargetTypeDescription">
            <summary>
              Looks up a localized string similar to The type that is managed by the manageability provider..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationElementManageabilityProviderDataTargetTypeDisplayName">
            <summary>
              Looks up a localized string similar to Target Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationElementManageabilityProviderDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Configuration Element Manageability Provider..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationElementManageabilityProviderDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSectionManageabilityProviderDataDescription">
            <summary>
              Looks up a localized string similar to A manageability provider for a configuration section..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSectionManageabilityProviderDataDisplayName">
            <summary>
              Looks up a localized string similar to Configuration Section Manageability Provider.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSectionManageabilityProviderDataManageabilityProvidersDescription">
             <summary>
               Looks up a localized string similar to A collection of Manageability Providers that the receiver might require to provide 
            manageability to configuration elements..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSectionManageabilityProviderDataManageabilityProvidersDisplayName">
            <summary>
              Looks up a localized string similar to Manageability Providers.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSectionManageabilityProviderDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Manageability Provider..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSectionManageabilityProviderDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSectionManageabilityProviderDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Manageability Provider..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSectionManageabilityProviderDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSourceElementDescription">
            <summary>
              Looks up a localized string similar to The element that represents a configuration source ..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSourceElementDisplayName">
            <summary>
              Looks up a localized string similar to An element that contains configurtain information..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSourceElementNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Configuration Source Element..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSourceElementNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSourceElementTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Configuration Source Element..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSourceElementTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSourceSectionDescription">
            <summary>
              Looks up a localized string similar to Configuration settings that determine the sources of configuration information for an application..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSourceSectionDisplayName">
            <summary>
              Looks up a localized string similar to Configuration Sources.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSourceSectionParentSourceDescription">
            <summary>
              Looks up a localized string similar to The name of the Configuration Source containing the section that this Redirected Section will override..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSourceSectionParentSourceDisplayName">
            <summary>
              Looks up a localized string similar to Parent Source.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSourceSectionRedirectedSectionsDescription">
            <summary>
              Looks up a localized string similar to The list of Redirected Sections for this application configuration..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSourceSectionRedirectedSectionsDisplayName">
            <summary>
              Looks up a localized string similar to Redirected Sections.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSourceSectionSelectedSourceDescription">
            <summary>
              Looks up a localized string similar to The name of the default Configuration Source..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSourceSectionSelectedSourceDisplayName">
            <summary>
              Looks up a localized string similar to Selected Source.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSourceSectionSourcesDescription">
            <summary>
              Looks up a localized string similar to The collection of defined configuration sources..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ConfigurationSourceSectionSourcesDisplayName">
            <summary>
              Looks up a localized string similar to Sources.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.FileConfigurationSourceElementDescription">
             <summary>
               Looks up a localized string similar to A Configuration Source that reads configuration information from a disk file. 
            The file must contain XML data in the standard .NET configuration format..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.FileConfigurationSourceElementDisplayName">
            <summary>
              Looks up a localized string similar to File-based Configuration Source.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.FileConfigurationSourceElementFilePathDescription">
            <summary>
              Looks up a localized string similar to The full path and name of the configuration file..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.FileConfigurationSourceElementFilePathDisplayName">
            <summary>
              Looks up a localized string similar to File Path.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.FileConfigurationSourceElementFilePathFilter">
            <summary>
              Looks up a localized string similar to Configuration files (*.config)|*.config|All Files (*.*)|*.*.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.FileConfigurationSourceElementNameDescription">
            <summary>
              Looks up a localized string similar to The name of the File-based Configuration Source..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.FileConfigurationSourceElementNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.FileConfigurationSourceElementTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the File-based Configuration Source..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.FileConfigurationSourceElementTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.InstrumentationConfigurationSectionApplicationInstanceNameDescription">
            <summary>
              Looks up a localized string similar to The unique name for the Application Instance to be used by the Instrumentation providers..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.InstrumentationConfigurationSectionApplicationInstanceNameDisplayName">
            <summary>
              Looks up a localized string similar to Application Instance Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.InstrumentationConfigurationSectionDescription">
            <summary>
              Looks up a localized string similar to Configuration settings for Instrumentation for the entire application..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.InstrumentationConfigurationSectionDisplayName">
            <summary>
              Looks up a localized string similar to Instrumentation Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.InstrumentationConfigurationSectionEventLoggingEnabledDescription">
             <summary>
               Looks up a localized string similar to Specifies if logging to Windows Event Log will take place when instrumented 
            events occur within Enterprise Library..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.InstrumentationConfigurationSectionEventLoggingEnabledDisplayName">
            <summary>
              Looks up a localized string similar to Event Logging Enabled.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.InstrumentationConfigurationSectionPerformanceCountersEnabledDescription">
             <summary>
               Looks up a localized string similar to Specifies if the Enterprise Library Performance Counters will be exposed and updated 
            when instrumented events occur within Enterprise Library..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.InstrumentationConfigurationSectionPerformanceCountersEnabledDisplayName">
            <summary>
              Looks up a localized string similar to Performance Counters Enabled.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.KeyValueConfigurationCollectionMetadataDescription">
            <summary>
              Looks up a localized string similar to Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.KeyValueConfigurationCollectionMetadataDisplayName">
            <summary>
              Looks up a localized string similar to Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.KeyValueConfigurationElementMetadataDescription">
            <summary>
              Looks up a localized string similar to A custom application settings, such as file paths or XML Web service URLs..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.KeyValueConfigurationElementMetadataDisplayName">
            <summary>
              Looks up a localized string similar to Setting.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.KeyValueConfigurationElementMetadataKeyDescription">
            <summary>
              Looks up a localized string similar to Key.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.KeyValueConfigurationElementMetadataKeyDisplayName">
            <summary>
              Looks up a localized string similar to Key.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.KeyValueConfigurationElementMetadataValueDescription">
            <summary>
              Looks up a localized string similar to Value.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.KeyValueConfigurationElementMetadataValueDisplayName">
            <summary>
              Looks up a localized string similar to Value.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ManageableConfigurationSourceElementApplicationNameDescription">
             <summary>
               Looks up a localized string similar to The name of the application. 
            This used to determine the Registry path for the configuration information. 
            This value is required..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ManageableConfigurationSourceElementApplicationNameDisplayName">
            <summary>
              Looks up a localized string similar to Application Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ManageableConfigurationSourceElementConfigurationManageabilityProvidersDescription">
            <summary>
              Looks up a localized string similar to The collection of registered types necessary to provide manageability by the configuration source..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ManageableConfigurationSourceElementConfigurationManageabilityProvidersDisplayName">
            <summary>
              Looks up a localized string similar to Configuration Manageability Providers.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ManageableConfigurationSourceElementDescription">
             <summary>
               Looks up a localized string similar to A configuration source that integrates with Group Policy to apply domain-wide or 
            local machine policy overrides to provide centralized configuration management capabilities..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ManageableConfigurationSourceElementDisplayName">
            <summary>
              Looks up a localized string similar to Manageable Configuration Source.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ManageableConfigurationSourceElementEnableGroupPoliciesDescription">
             <summary>
               Looks up a localized string similar to Specifies if the configuration source will take account of domain-wide or 
            local machine policy overrides configured in the Registry through Group Policy settings..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ManageableConfigurationSourceElementEnableGroupPoliciesDisplayName">
            <summary>
              Looks up a localized string similar to Group Policy Enabled.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ManageableConfigurationSourceElementFilePathDescription">
            <summary>
              Looks up a localized string similar to The full path and name of the configuration file..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ManageableConfigurationSourceElementFilePathDisplayName">
            <summary>
              Looks up a localized string similar to File Path.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ManageableConfigurationSourceElementNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Manageable Configuration Source..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ManageableConfigurationSourceElementNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ManageableConfigurationSourceElementTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Manageable Configuration Source..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.ManageableConfigurationSourceElementTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.NamedConfigurationElementDescription">
            <summary>
              Looks up a localized string similar to A configuration element in a collection that is keyed by the name..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.NamedConfigurationElementDisplayName">
            <summary>
              Looks up a localized string similar to Named Configuration Element.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.NamedConfigurationElementNameDescription">
            <summary>
              Looks up a localized string similar to The name used to identify this item..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.NamedConfigurationElementNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.NamedElementCollectionDescription">
            <summary>
              Looks up a localized string similar to A collection of Named Configuration elements..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.NamedElementCollectionDisplayName">
            <summary>
              Looks up a localized string similar to Named Element Collection.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.NameTypeConfigurationElementCollectionDescription">
            <summary>
              Looks up a localized string similar to A collection of Name Type Configuration elements..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.NameTypeConfigurationElementCollectionDisplayName">
            <summary>
              Looks up a localized string similar to Name Type Configuration Element Collection.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.NameTypeConfigurationElementDescription">
            <summary>
              Looks up a localized string similar to A configuration element in a collection that contains both a type and a name..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.NameTypeConfigurationElementDisplayName">
            <summary>
              Looks up a localized string similar to Name Type Configuration Element.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.NameTypeConfigurationElementNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Name Type Configuration Element..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.NameTypeConfigurationElementNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.NameTypeConfigurationElementTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Name Type Configuration Element..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.NameTypeConfigurationElementTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.PolymorphicConfigurationElementCollectionDescription">
            <summary>
              Looks up a localized string similar to A base class from which all implementations of polymorphic configuration collections derive..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.PolymorphicConfigurationElementCollectionDisplayName">
            <summary>
              Looks up a localized string similar to Polymorphic Configuration Element Collection.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.RedirectedSectionElementDescription">
             <summary>
               Looks up a localized string similar to A redirected section for the application configuration. 
            Each section of the configuration can be loaded from a different configuration source if required. 
            Each Redirected Section configured in this list will override the same entire section in 
            the default Configuration Source..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.RedirectedSectionElementDisplayName">
            <summary>
              Looks up a localized string similar to Redirected Section.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.RedirectedSectionElementNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Redirected Section..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.RedirectedSectionElementNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.RedirectedSectionElementSourceNameDescription">
             <summary>
               Looks up a localized string similar to The name of the Configuration Source that will handle the redirected section. 
            Must be one of the Configuration Sources defined for the application..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.RedirectedSectionElementSourceNameDisplayName">
            <summary>
              Looks up a localized string similar to Configuration Source.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.SerializableConfigurationSectionDescription">
            <summary>
              Looks up a localized string similar to Represents a configuration section that can be serialized and deserialized to XML..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.SerializableConfigurationSectionDisplayName">
            <summary>
              Looks up a localized string similar to Serializable Configuration Section.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.SystemConfigurationSourceElementDescription">
             <summary>
               Looks up a localized string similar to A configuration source that reads configuration data from the default configuration file. 
            Typically this is App.config or Web.config, depending on the application type..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.SystemConfigurationSourceElementDisplayName">
            <summary>
              Looks up a localized string similar to System Configuration Source.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.SystemConfigurationSourceElementNameDescription">
            <summary>
              Looks up a localized string similar to The name of the System Configuration Source..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.SystemConfigurationSourceElementNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.SystemConfigurationSourceElementTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the System Configuration Source..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.SystemConfigurationSourceElementTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.TypeRegistrationProviderElementCollectionDescription">
            <summary>
              Looks up a localized string similar to A collection of Type Registration Provider elements..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.TypeRegistrationProviderElementCollectionDisplayName">
            <summary>
              Looks up a localized string similar to Type Registration Provider Element Collection.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.TypeRegistrationProviderElementDescription">
            <summary>
              Looks up a localized string similar to A provider for registering types..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.TypeRegistrationProviderElementDisplayName">
            <summary>
              Looks up a localized string similar to Type Registration Provider Element.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.TypeRegistrationProviderElementNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Type Registration Provider Element..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.TypeRegistrationProviderElementNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.TypeRegistrationProviderElementProviderTypeNameDescription">
            <summary>
              Looks up a localized string similar to The name of the type that implements the Type Registration Provider Element..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.TypeRegistrationProviderElementProviderTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Provider Type Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.TypeRegistrationProviderElementSectionNameDescription">
            <summary>
              Looks up a localized string similar to The name of the configuration section within the configuration file..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.TypeRegistrationProviderElementSectionNameDisplayName">
            <summary>
              Looks up a localized string similar to Section Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.TypeRegistrationProvidersConfigurationSectionDescription">
            <summary>
              Looks up a localized string similar to The section of the configuration that specifies the Type Registration Providers for the application..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.TypeRegistrationProvidersConfigurationSectionDisplayName">
            <summary>
              Looks up a localized string similar to Type Registration Providers Configuration Section.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.TypeRegistrationProvidersConfigurationSectionTypeRegistrationProvidersDescription">
            <summary>
              Looks up a localized string similar to A collection of Type Registration providers..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DesignResources.TypeRegistrationProvidersConfigurationSectionTypeRegistrationProvidersDisplayName">
            <summary>
              Looks up a localized string similar to Type Registration Providers.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AddApplicationBlockCommandAttribute">
            <summary>
            Attribute class used to decorate the design time view model with a Add Application Block command. <br/>
            Add Application Block commands are added to the configuration tools main menu, underneath the 'Blocks' menu item.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandAttribute">
            <summary>
            Attribute used to decorate a designtime View Model element with an executable command. E.g. a context menu item that allows
            the user to perform an action in the elements context.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandAttribute"/> class, specifying the Command Model Type.
            </summary>
            <remarks>
            The Command Model Type should derive from the CommandModel class in the Configuration.Design assembly. <br/>
            As this attribute can be applied to the configuration directly and we dont want to force a dependency on the Configuration.Design assembly <br/>
            You can specify the Command Model Type in a loosy coupled fashion.
            </remarks>
            <param name="commandModelTypeName">The fully qualified name of the Command Model Type.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandAttribute.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandAttribute"/> class, specifying the Command Model Type.
            </summary>
            <remarks>
            The Command Model Type should derive from the CommandModel class in the Configuration.Design assmbly. <br/>
            As this attribute can be applied to the configuration directly and we dont want to force a dependency on the Configuration.Design assembly <br/>
            You can specify the Command Model Type in a loosy coupled fashion.
            </remarks>
            <param name="commandModelType">The Command Model Type.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandAttribute.TitleResourceName">
            <summary>
            Gets or sets the name of the resource, used to return a localized title that will be shown for this command in the UI (User Interface).
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandAttribute.TitleResourceType">
            <summary>
            Gets or sets the type of the resource, used to return a localized title that will be shown for this command in the UI (User Interface).
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandAttribute.Title">
            <summary>
            Gets the title that will be shown for this command in the UI (User Interface).
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandAttribute.Replace">
            <summary>
            Gets or sets the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandReplacement"/> options for this command.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandAttribute.CommandPlacement">
            <summary>
            Gets or sets the <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandAttribute.CommandPlacement"/> options for this command.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandAttribute.CommandModelTypeName">
            <summary>
            Gets or Sets the Command Model Type Name for this command. <br/>
            The Command Model Type will be used at runtime to display and execute the command.<br/>
            Command Model Types should derive from the CommandModel class in the Configuration.Design assembly. 
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandAttribute.CommandModelType">
            <summary>
            Gets the Command Model Type for this command. <br/>
            The Command Model Type will be used at runtime to display and execute the command.<br/>
            Command Model Types should derive from the CommandModel class in the Configuration.Design assembly. 
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandAttribute.KeyGesture">
            <summary>
            Defines the keyboard gesture for this command.
            </summary>
            <example>
                command.KeyGesture = "Ctrl+1";
            </example>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandAttribute.TypeId">
            <summary>
            When implemented in a derived class, gets a unique identifier for this <see cref="T:System.Attribute"/>.
            </summary>
            <returns>
            An <see cref="T:System.Object"/> that is a unique identifier for the attribute.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AddApplicationBlockCommandAttribute.#ctor(System.String,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AddApplicationBlockCommandAttribute"/> class.
            </summary>
            <param name="sectionName">The name of the configuration section that belongs to the application block that will be added.</param>
            <param name="configurationSectionType">The type of the configuration section that belongs to the application block that will be added.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AddApplicationBlockCommandAttribute.SectionName">
            <summary>
            Gets the name of the configuration section that belongs to the application block that will be added.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AddApplicationBlockCommandAttribute.ConfigurationSectionType">
            <summary>
            Gets the type of the configuration section that belongs to the application block that will be added.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AddSateliteProviderCommandAttribute">
            <summary>
            Attribute used to overwrite the Add Command for providers that depend on the availability of another block (Sattelite Providers).
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AddSateliteProviderCommandAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AddSateliteProviderCommandAttribute"/> specifying the block dependency by its configuration section name.<br/>
            </summary>
            <param name="sectionName">The name of the configuran section, used to identify the block dependency.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AddSateliteProviderCommandAttribute.#ctor(System.String,System.Type,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AddSateliteProviderCommandAttribute"/> specifying the block dependency by its configuration section name and will assign the value of a default provider to the added element.<br/>
            </summary>
            <param name="sectionName">The name of the configuran section, used to identify the block dependency.</param>
            <param name="defaultProviderConfigurationType">The configuration type of the element that declares the default proviiders name.</param>
            <param name="defaultProviderConfigurationPropertyName">The property that will be used to determine the name of the default provider.</param>
            <param name="sateliteProviderReferencePropertyName">The property on the created element that will be assigned the name of the default provider.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AddSateliteProviderCommandAttribute.SectionName">
            <summary>
            Gets the section name of the block dependency.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AddSateliteProviderCommandAttribute.DefaultProviderConfigurationType">
            <summary>
            If a configuration element exists that specifies a default property, gets the configuration type of the declaring element.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AddSateliteProviderCommandAttribute.DefaultProviderConfigurationPropertyName">
            <summary>
            If a configuration element exists that specifies a default property, gets the property that contains the name of the default value.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AddSateliteProviderCommandAttribute.SateliteProviderReferencePropertyName">
            <summary>
            If the provider has a property that should be assigned the name of the default provider, gets the name of the property.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AppSettingsDesignTime">
            <summary>
            Container class for types and identifiers used to decorate the appSettings configuration schema with designtime information.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AppSettingsDesignTime.AppSettingsSectionName">
            <summary>
            Name of appSettings section.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AppSettingsDesignTime.ViewModelTypeNames">
            <summary>
            Container class for View Model Types used to decorate the appSettings configuration schema with designtime information.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AppSettingsDesignTime.ViewModelTypeNames.AppSettingsSectionViewModel">
            <summary>
            Type Name of the Section View Model used to display application settings.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AppSettingsDesignTime.MetadataTypes">
            <summary>
            This class supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AppSettingsDesignTime.MetadataTypes.AppSettingsSectionMetadata">
            <summary>
            This class supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AppSettingsDesignTime.MetadataTypes.KeyValueConfigurationCollectionMetadata">
            <summary>
            This class supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AppSettingsDesignTime.MetadataTypes.KeyValueConfigurationElementMetadata">
            <summary>
            This class supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AppSettingsDesignTime.MetadataTypes.KeyValueConfigurationElementMetadata.Key">
            <summary>
            This property supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.AppSettingsDesignTime.MetadataTypes.KeyValueConfigurationElementMetadata.Value">
            <summary>
            This property supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.BaseTypeAttribute">
            <summary>
            Indicates the base class or interface that must be assignable from the type specified in the property that this attribute decorates.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.BaseTypeAttribute.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the  <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.BaseTypeAttribute"/> class with the specified <see cref="T:System.Type"/> object.
            </summary>
            <param name="baseType">
            The <see cref="T:System.Type"/> to filter selections.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.BaseTypeAttribute.#ctor(System.Type,System.Type)">
            <summary>
            Initializes a new instance of the  <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.BaseTypeAttribute"/> class with the specified base <see cref="T:System.Type"/> object and configuration <see cref="T:System.Type"/>.
            </summary>
            <param name="baseType">The base <see cref="T:System.Type"/> to filter.</param>
            <param name="configurationType">The configuration object <see cref="T:System.Type"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.BaseTypeAttribute.#ctor(System.Type,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.TypeSelectorIncludes)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.BaseTypeAttribute"/> class with the specified <see cref="T:System.Type"/> object and <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.BaseTypeAttribute.TypeSelectorIncludes"/>.
            </summary>
            <param name="baseType">
            The <see cref="T:System.Type"/> to filter selections.
            </param>
            <param name="typeSelectorIncludes">
            One of the <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.BaseTypeAttribute.TypeSelectorIncludes"/> values.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.BaseTypeAttribute.#ctor(System.Type,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.TypeSelectorIncludes,System.Type)">
            <summary>
            Initializes a new instance of the  <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.BaseTypeAttribute"/> class with the specified base <see cref="T:System.Type"/> object and configuration <see cref="T:System.Type"/>.
            </summary>
            <param name="typeSelectorIncludes">
            One of the <see cref="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.BaseTypeAttribute.typeSelectorIncludes"/> values.
            </param>
            <param name="baseType">The base <see cref="T:System.Type"/> to filter.</param>
            <param name="configurationType">The configuration object <see cref="T:System.Type"/>.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.BaseTypeAttribute.TypeSelectorIncludes">
            <summary>
            Gets the includes for the type selector.
            </summary>
            <value>
            The includes for the type selector.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.BaseTypeAttribute.BaseType">
            <summary>
            Gets the <see cref="T:System.Type"/> to filter selections.
            </summary>
            <value>
            The <see cref="T:System.Type"/> to filter selections.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.BaseTypeAttribute.ConfigurationType">
            <summary>
            Gets the configuration object <see cref="T:System.Type"/>.
            </summary>
            <value>
            The configuration object <see cref="T:System.Type"/>.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CloneableConfigurationElementTypeAttribute">
            <summary>
            Attribute class used to associate a <see cref="T:System.Configuration.ConfigurationElement"/> class with an <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ICloneableConfigurationElement"/> implementation.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ICloneableConfigurationElement"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CloneableConfigurationElementTypeAttribute.#ctor(System.Type)">
            <summary>
            Creates a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CloneableConfigurationElementTypeAttribute"/> class.
            </summary>
            <param name="cloneableConfigurationElementType">The type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ICloneableConfigurationElement"/> that should be associated with the target <see cref="T:System.Configuration.ConfigurationElement"/> class.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CloneableConfigurationElementTypeAttribute.CloneableConfigurationElementType">
            <summary>
            Gets the type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ICloneableConfigurationElement"/> that should be associated with the target <see cref="T:System.Configuration.ConfigurationElement"/> class.
            </summary>
            <value>
            The type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ICloneableConfigurationElement"/> that should be associated with the target <see cref="T:System.Configuration.ConfigurationElement"/> class.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesigntimeDefaultAttribute">
            <summary>
            Specifies a default value for a configuration property.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesigntimeDefaultAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesigntimeDefaultAttribute"/> class.
            </summary>
            <remarks>
            The default value is a string representation which will be converted using <see cref="P:System.Globalization.CultureInfo.InvariantCulture"/>.
            </remarks>
            <param name="bindableDefaultValue">The string representation of the default value.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesigntimeDefaultAttribute.BindableDefaultValue">
            <summary>
            Gets the string reprentation of the default value.
            </summary>
            <value>
            The string reprentation of the default value.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ConfigurationSourcesDesignTime">
            <summary>
            This class supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ConfigurationSourcesDesignTime.ViewModelTypeNames">
            <summary>
            This class supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ConfigurationSourcesDesignTime.ViewModelTypeNames.ConfigurationSourcesSectionViewModel">
            <summary>
            This field supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ConfigurationSourcesDesignTime.ViewModelTypeNames.ConfigurationSourceSectionViewModel">
            <summary>
            This field supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ConfigurationSourcesDesignTime.CommandTypeNames">
            <summary>
            This class supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ConfigurationSourcesDesignTime.CommandTypeNames.AddConfigurationSourcesBlockCommand">
            <summary>
            This field supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ConfigurationSourcesDesignTime.CommandTypeNames.ConfigurationSourceElementDeleteCommand">
            <summary>
            This field supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesignConfigurationSource">
            <summary>
            configuration source to support design-time configuration of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource"/>
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource">
            <summary>
            Represents a configuration source that retrieves configuration information from an arbitrary file.
            </summary>
            <remarks>
            This configuration source uses a <see cref="T:System.Configuration.Configuration"/> object to deserialize 
            configuration, so the configuration file must be a valid .NET Framework configuration file.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource">
            <summary>
            Represents the implementation details for file-based configuration sources.
            </summary>
            <remarks>
            This implementation deals with setting up the watcher over the configuration files to detect changes and update
            the configuration representation. It also manages the change notification features provided by the file based 
            configuration sources.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceTest">
            <summary>
            This interface supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            Augmented version of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> interface intended to be used by unit tests.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource">
            <summary>
            Represents a source for getting configuration information.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.GetSection(System.String)">
            <summary>
            Retrieves the specified <see cref="T:System.Configuration.ConfigurationSection"/>.
            </summary>
            <param name="sectionName">The name of the section to be retrieved.</param>
            <returns>The specified <see cref="T:System.Configuration.ConfigurationSection"/>, or <see langword="null"/> (<b>Nothing</b> in Visual Basic)
            if a section by that name is not found.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Add(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            Adds a <see cref="T:System.Configuration.ConfigurationSection"/> to the configuration source and saves the configuration source.
            </summary>
            <remarks>
            If a configuration section with the specified name already exists it will be replaced.
            </remarks>
            <param name="sectionName">The name by which the <paramref name="configurationSection"/> should be added.</param>
            <param name="configurationSection">The configuration section to add.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Remove(System.String)">
            <summary>
            Removes a <see cref="T:System.Configuration.ConfigurationSection"/> from the configuration source.
            </summary>
            <param name="sectionName">The name of the section to remove.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.AddSectionChangeHandler(System.String,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventHandler)">
            <summary>
            Adds a handler to be called when changes to the section named <paramref name="sectionName"/> are detected.
            </summary>
            <param name="sectionName">The name of the section to watch for.</param>
            <param name="handler">The handler for the change event to add.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.RemoveSectionChangeHandler(System.String,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventHandler)">
            <summary>
            Removes a handler to be called when changes to section <code>sectionName</code> are detected.
            </summary>
            <param name="sectionName">The name of the watched section.</param>
            <param name="handler">The handler for the change event to remove.</param>
        </member>
        <member name="E:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.SourceChanged">
            <summary>
            Event raised when any section in this configuration source changes.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceTest.ConfigSourceChanged(System.String)">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceTest.ExternalConfigSourceChanged(System.String)">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceTest.ConfigSourceWatcherMappings">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceTest.WatchedConfigSources">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceTest.WatchedSections">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.NullConfigSource">
            <summary>
            ConfigSource value for sections that existed in configuration but were later removed.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.#ctor(System.String,System.Boolean,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource"/> class.
            </summary>
            <param name="configurationFilepath">The path for the main configuration file.</param>
            <param name="refresh"><b>true</b>if runtime changes should be refreshed, <b>false</b> otherwise.</param>
            <param name="refreshInterval">The poll interval in milliseconds.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.GetSection(System.String)">
            <summary>
            Retrieves the specified <see cref="T:System.Configuration.ConfigurationSection"/> from the configuration file, and starts watching for 
            its changes if not watching already.
            </summary>
            <param name="sectionName">The section name.</param>
            <returns>The section, or <see langword="null"/> if it doesn't exist.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.AddSectionChangeHandler(System.String,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventHandler)">
            <summary>
            Adds a handler to be called when changes to section <code>sectionName</code> are detected.
            </summary>
            <param name="sectionName">The name of the section to watch for.</param>
            <param name="handler">The handler.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.RemoveSectionChangeHandler(System.String,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventHandler)">
            <summary>
            Remove a handler to be called when changes to section <code>sectionName</code> are detected.
            </summary>
            <param name="sectionName">The name of the section to watch for.</param>
            <param name="handler">The handler.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.Add(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            Adds a <see cref="T:System.Configuration.ConfigurationSection"/> to the configuration and saves the configuration source.
            </summary>
            <remarks>
            If a configuration section with the specified name already exists it will be replaced.
            If a configuration section was retrieved from an instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource"/>, a <see cref="T:System.InvalidOperationException"/> will be thrown.
            </remarks>
            <param name="sectionName">The name by which the <paramref name="configurationSection"/> should be added.</param>
            <param name="configurationSection">The configuration section to add.</param>
            <exception cref="T:System.InvalidOperationException">The configuration section was retrieved from an instance of  <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource"/> or <see cref="N:Microsoft.Practices.EnterpriseLibrary.Common.Configuration"/> and cannot be added to the current source.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.DoAdd(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            When implemented in a derived class, adds a <see cref="T:System.Configuration.ConfigurationSection"/> to the configuration and saves the configuration source.
            </summary>
            <remarks>
            If a configuration section with the specified name already exists it should be replaced.
            </remarks>
            <param name="sectionName">The name by which the <paramref name="configurationSection"/> should be added.</param>
            <param name="configurationSection">The configuration section to add.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.Remove(System.String)">
            <summary>
            Removes a <see cref="T:System.Configuration.ConfigurationSection"/> from the configuration and saves the configuration source.
            </summary>
            <param name="sectionName">The name of the section to remove.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.DoRemove(System.String)">
            <summary>
            When implemented in a derived class, removes a <see cref="T:System.Configuration.ConfigurationSection"/> from the configuration and saves the configuration source.
            </summary>
            <param name="sectionName">The name of the section to remove.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.ConfigSourceChanged(System.String)">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
            <param name="configSource">The name of the updated configuration source.</param>
            <devdoc>
            Only needs to deal with concurrency to get the current sections and to update the watchers.
            
            Rationale:
            - Sections' are only added or updated.
            - For this notification, all sections in the configuration file must be updated, and sections in external 
            files must be refreshed only if the config source changed.
            - why not check after the original set of sections is retrieved?
            -- Sections might have been added to the listener set after the original set is retrieved, but...
            -- If they were added after the original set was retrieved, then they are up to date.
            --- For this to happen, they couldn't have been read before the o.s., otherwise they would be a listener for them.
            --- So, the retrieved information must be fresh (checked this with a test). 
            -- What about other changes?
            --- Erased sections: only tested in the configuration file watching thread, because the meta configuration 
            is kept in the configuration file.
            ---- Even if the external file an external is deleted because of the deletion, and this change is processed
            before the configuration file change, the refresh for the external section will refresh all the sections for the file and 
            notify a change, without need for checking the change. The change would later be picked up by the configuration file watcher 
            which will notify again. This shouldn't be a problem.
            --- External sections with changed sources. If they existed before, they must have been in the configuration file and there 
            was an entry in the bookeeping data structures.
            - Concurrent updates for sections values should be handled by the system.config fx
            </devdoc>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.Dispose(System.Boolean)">
            <summary>
            Releases the resources used by the change watchers.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.Dispose">
            <summary>
            Releases the resources used by the change watchers.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.NotifyUpdatedSections(System.Collections.Generic.IEnumerable{System.String})">
            <summary/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.DoGetSection(System.String)">
            <summary>
            Retrieves the specified <see cref="T:System.Configuration.ConfigurationSection"/> from the configuration file.
            </summary>
            <param name="sectionName">The section name.</param>
            <returns>The section, or <see langword="null"/> if it doesn't exist.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.OnSourceChanged(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceChangedEventArgs)">
            <summary>
            Raises the <see cref="E:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.SourceChanged"/> event.
            </summary>
            <param name="configurationSourceChangedEventArgs">The argument for the raised event.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.RefreshAndValidateSections(System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.ICollection{System.String}@,System.Collections.Generic.IDictionary{System.String,System.String}@)">
            <summary>
            Refreshes the configuration sections from the main configuration file and determines which sections have suffered notifications
            and should be notified to registered handlers.
            </summary>
            <param name="localSectionsToRefresh">A dictionary with the configuration sections residing in the main configuration file that must be refreshed.</param>
            <param name="externalSectionsToRefresh">A dictionary with the configuration sections residing in external files that must be refreshed.</param>
            <param name="sectionsToNotify">A new collection with the names of the sections that suffered changes and should be notified.</param>
            <param name="sectionsWithChangedConfigSource">A new dictionary with the names and file names of the sections that have changed their location.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.RefreshExternalSections(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Refreshes the configuration sections from an external configuration file.
            </summary>
            <param name="sectionsToRefresh">A collection with the names of the sections that suffered changes and should be refreshed.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.ValidateArgumentsAndFileExists(System.String,System.String,System.Configuration.ConfigurationSection)">
            <summary>
            Validates the parameters required to save a configuration section.
            </summary>
        </member>
        <member name="E:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.SourceChanged">
            <summary>
            Event raised when any section in this configuration source has changed.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource.ConfigurationFilePath">
            <summary>
            Gets the path of the configuration file for the configuration source.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IProtectedConfigurationSource">
            <summary>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IProtectedConfigurationSource.Add(System.String,System.Configuration.ConfigurationSection,System.String)">
            <summary>
            </summary>
            <param name="sectionName"></param>
            <param name="configurationSection"></param>
            <param name="protectionProviderName"></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource"/> class.
            </summary>
            <param name="configurationFilepath">The configuration file path. The path can be absolute or relative.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource.#ctor(System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource"/> class that will refresh changes
            according to the value of the <paramref name="refresh"/> parameter.
            </summary>
            <param name="configurationFilepath">The configuration file path. The path can be absolute or relative.</param>
            <param name="refresh"><see langword="true"/> if changes to the configuration file should be notified.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource.#ctor(System.String,System.Boolean,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource"/> that will refresh changes
            according to the value of the <paramref name="refresh"/> parameter, polling every 
            <paramref name="refreshInterval"/> milliseconds.
            </summary>
            <param name="configurationFilepath">The configuration file path. The path can be absolute or relative.</param>
            <param name="refresh"><see langword="true"/> if changes to the configuration file should be notified.</param>
            <param name="refreshInterval">The poll interval in milliseconds.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource.DoAdd(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            Adds a <see cref="T:System.Configuration.ConfigurationSection"/> to the configuration and saves the configuration source.
            </summary>
            <remarks>
            If a configuration section with the specified name already exists it will be replaced.
            </remarks>
            <param name="sectionName">The name by which the <paramref name="configurationSection"/> should be added.</param>
            <param name="configurationSection">The configuration section to add.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource.DoRemove(System.String)">
            <summary>
            Removes a <see cref="T:System.Configuration.ConfigurationSection"/> from the configuration and saves the configuration source.
            </summary>
            <param name="sectionName">The name of the section to remove.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource.Add(System.String,System.Configuration.ConfigurationSection,System.String)">
            <summary>
            Adds a <see cref="T:System.Configuration.ConfigurationSection"/> to the configuration and saves the configuration source using encryption.
            </summary>
            <remarks>
            If a configuration section with the specified name already exists it will be replaced.<br/>
            If a configuration section was retrieved from an instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource"/>, a <see cref="T:System.InvalidOperationException"/> will be thrown.
            </remarks>
            <param name="sectionName">The name by which the <paramref name="configurationSection"/> should be added.</param>
            <param name="configurationSection">The configuration section to add.</param>
            <param name="protectionProviderName">The name of the protection provider to use when encrypting the section.</param>
            <exception cref="T:System.InvalidOperationException">The configuration section was retrieved from an instance of  <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileBasedConfigurationSource"/> or <see cref="N:Microsoft.Practices.EnterpriseLibrary.Common.Configuration"/> and cannot be added to the current source.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource.Save(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            Adds or replaces <paramref name="configurationSection"/> under name <paramref name="section"/> in the configuration and saves the configuration file.
            </summary>
            <param name="section">The name for the section.</param>
            <param name="configurationSection">The configuration section to add or replace.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource.Save(System.String,System.Configuration.ConfigurationSection,System.String)">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            Adds or replaces <paramref name="configurationSection"/> under name <paramref name="section"/> in the configuration 
            file and saves the configuration file using encryption.
            </summary>
            <param name="section">The name for the section.</param>
            <param name="configurationSection">The configuration section to add or replace.</param>
            <param name="protectionProvider">The name of the protection provider to use when encrypting the section.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource.DoGetSection(System.String)">
            <summary>
            Retrieves the specified <see cref="T:System.Configuration.ConfigurationSection"/> from the configuration file.
            </summary>
            <param name="sectionName">The section name.</param>
            <returns>The section, or <see langword="null"/> if it doesn't exist.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource.RefreshAndValidateSections(System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.ICollection{System.String}@,System.Collections.Generic.IDictionary{System.String,System.String}@)">
            <summary>
            Refreshes the configuration sections from the main configuration file and determines which sections have 
            suffered notifications and should be notified to registered handlers.
            </summary>
            <param name="localSectionsToRefresh">A dictionary with the configuration sections residing in the main 
            configuration file that must be refreshed.</param>
            <param name="externalSectionsToRefresh">A dictionary with the configuration sections residing in external 
            files that must be refreshed.</param>
            <param name="sectionsToNotify">A new collection with the names of the sections that suffered changes and 
            should be notified.</param>
            <param name="sectionsWithChangedConfigSource">A new dictionary with the names and file names of the sections 
            that have changed their location.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource.RefreshExternalSections(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Refreshes the configuration sections from an external configuration file.
            </summary>
            <param name="sectionsToRefresh">A collection with the names of the sections that suffered changes and should 
            be refreshed.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.IDesignConfigurationSource">
            <summary>
             Supports Enterprise Library design-time by providing ability to 
             retrieve, add, and remove sections.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.IDesignConfigurationSource.GetLocalSection(System.String)">
            <summary>
             Retrieves a local section from the configuration source.
            </summary>
            <param name="sectionName"></param>
            <returns>The configuration section or null if it does not contain the section.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.IDesignConfigurationSource.AddLocalSection(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            Adds a local section to the configuration source.
            </summary>
            <param name="sectionName"></param>
            <param name="section"></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.IDesignConfigurationSource.RemoveLocalSection(System.String)">
            <summary>
             Removes a local section from the configuration source.
            </summary>
            <param name="sectionName"></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesignConfigurationSource.#ctor(System.String)">
            <summary>
             Initializes a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesignConfigurationSource"/> based on file path.
            </summary>
            <param name="configurationFilePath"></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesignConfigurationSource.GetLocalSection(System.String)">
            <summary>
             Retrieves a local section from the configuration source.
            </summary>
            <param name="sectionName"></param>
            <returns>The configuration section or null if it does not contain the section.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesignConfigurationSource.AddLocalSection(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            Adds a local section to the configuration source.
            </summary>
            <param name="sectionName"></param>
            <param name="section"></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesignConfigurationSource.RemoveLocalSection(System.String)">
            <summary>
             Removes a local section from the configuration source.
            </summary>
            <param name="sectionName"></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesignConfigurationSource.CreateDesignSource(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.IDesignConfigurationSource,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesignConfigurationSource"/> based on <paramref name="rootSource"/> and <paramref name="filePath"/>.
            </summary>
            <param name="rootSource">The source that was used to open the main conifguration file.</param>
            <param name="filePath">An absolute of relative path to the file to which the source should be created.</param>
            <returns>A new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesignConfigurationSource"/>.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesignConfigurationSource.ConfigurationFilePath">
            <summary>
            Gets the path of the configuration file for the configuration source.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.MergeableConfigurationCollectionTypeAttribute">
            <summary>
            Attribute class used to associate a <see cref="T:System.Configuration.ConfigurationElementCollection"/> class with an <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IMergeableConfigurationElementCollection"/> implementation.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IMergeableConfigurationElementCollection"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.MergeableConfigurationCollectionTypeAttribute.#ctor(System.Type)">
            <summary>
            Creates a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.MergeableConfigurationCollectionTypeAttribute"/> class.
            </summary>
            <param name="mergeableConfigurationCollectionType">The type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IMergeableConfigurationElementCollection"/> that should be associated with the target <see cref="T:System.Configuration.ConfigurationElementCollection"/> class.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.MergeableConfigurationCollectionTypeAttribute.MergeableConfigurationCollectionType">
            <summary>
            Gets the type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IMergeableConfigurationElementCollection"/> that should be associated with the target <see cref="T:System.Configuration.ConfigurationElementCollection"/> class.
            </summary>
            <value>
            The type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IMergeableConfigurationElementCollection"/> that should be associated with the target <see cref="T:System.Configuration.ConfigurationElementCollection"/> class.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.NamePropertyAttribute">
            <summary>
            Attribute class that allows to specify a property that should be used as the Element View Model's name.<br/>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.NamePropertyAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.NamePropertyAttribute"/> class.
            </summary>
            <param name="propertyName">The reflection name of the property that will be used as the Element View Model's name.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.NamePropertyAttribute.PropertyName">
            <summary>
            Gets the reflection name of the property that will be used as the Element View Model's name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.NamePropertyAttribute.NamePropertyDisplayFormat">
            <summary>
            Gets the Display Format that will be used to display the name property.<br/>
            The Display Format should be a Format-string with 1 argument:<Br/>
            The token '{0}' will be replaced with the Name Properties value.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.OmitCustomAttributesPropertyAttribute">
            <summary>
            This attribute supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.PromoteCommandsAttribute">
            <summary>
            Attribute class that can be oved to offer a properties add-commands to the containing Element View Model.<br/>
            This can be usefull for properties that contain a collection of providers, of which the Element Collection View Model is not shown in the UI (User Interface).
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.EditorWithReadOnlyTextAttribute">
            <summary>
            Attribute that instructs the designtime to make the textbox for a property readonly. <br/>
            This property can is used together with an <see cref="T:System.ComponentModel.EditorAttribute"/>, in which the created text box is readonly, 
            though the property can be edited by the editor.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.EditorWithReadOnlyTextAttribute.#ctor(System.Boolean)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.EditorWithReadOnlyTextAttribute"/>.
            </summary>
            <param name="readonlyText"><see langword="true"/> if the textbox created for this property should be readonly, otherwise <see langword="false"/>.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.EditorWithReadOnlyTextAttribute.ReadonlyText">
            <summary>
            Returns <see langword="true"/> if the textbox created for this property should be readonly, otherwise <see langword="false"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.RegisterAsMetadataTypeAttribute">
            <summary>
            Registers a class as a metadata body class for another class.
            </summary>
            <remarks>
            When applying metadata attributes to classes, the target class might not always allow itself to be anotated. <br/>
            This attribute can be used to nominate another class to contain the metadata attributes. <br/>
            The metadata type should follow the same structure as the target type and its members cab be decorated with the metadata attributes.<br/>
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.RegisterAsMetadataTypeAttribute.#ctor(System.Type)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.RegisterAsMetadataTypeAttribute"/>.
            </summary>
            <param name="targetType">The type for which this class should contain metadata attributes.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.RegisterAsMetadataTypeAttribute.TargetType">
            <summary>
            Gets the type for which this class should contain metadata attributes.
            </summary>
            <value>
            The type for which this class should contain metadata attributes.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.TypePickingCommandAttribute">
            <summary>
            Attribute class that is used to add a custom add command for a Element View Model.<br/>
            The Type Picking Command displays a type picker prior to adding the target element and can use its result to initialize the added element.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.TypePickingCommandAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.TypePickingCommandAttribute"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.TypePickingCommandAttribute.#ctor(System.String)">
            <summary>
             Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.TypePickingCommandAttribute"/> class.
            </summary>
            <param name="property">The clr-name of the property to which the selected type should be assigned. This property is expected to be of type <see cref="T:System.String"/>.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.TypePickingCommandAttribute.Property">
            <summary>
            Gets the clr-name of the property to which the selected type should be assigned.
            </summary>
            <value>
            The clr-name of the property to which the selected type should be assigned.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandReplacement">
            <summary>
            Specifies whether a command replaces a default command.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandReplacement.DefaultAddCommandReplacement">
            <summary>
            Specifies that the command should be used to replace the default add command.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandReplacement.DefaultDeleteCommandReplacement">
            <summary>
            Specifies that the command should be used to replace the default delete command.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandReplacement.NoCommand">
            <summary>
            Specifies that the command should not be used to replace any default command.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandPlacement">
            <summary>
            Specifies the placement of a command. This can be either a top level menu, e.g.: <see cref="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandPlacement.FileMenu"/> or <see cref="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandPlacement.BlocksMenu"/> or
            a context menu, e.g.: <see cref="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandPlacement.ContextAdd"/>,  <see cref="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandPlacement.ContextCustom"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandPlacement.FileMenu">
            <summary>
            Specifies placement of the command in the top level file menu.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandPlacement.BlocksMenu">
            <summary>
            Specifies placement of the command in the top level blocks menu.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandPlacement.WizardMenu">
            <summary>
            Specifies placement of the command in the top level wizards menu.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandPlacement.ContextAdd">
            <summary>
            Specifies placement of the command in the contextual add menu for an configuration element.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandPlacement.ContextCustom">
            <summary>
            Specifies placement of the command in the custom commands menu for an configuration element.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommandPlacement.ContextDelete">
            <summary>
            Specifies placement of the command in the delete commands menu for an configuration element.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesignTimeReadOnlyAttribute">
            <summary>
             Determines if the corresponding property is read-only at designtime.
            </summary>
            <remarks>
             This attribute is used to mark properties that should be presented as read-only, but underlying code may change the value on.
             <seealso cref="T:System.ComponentModel.ReadOnlyAttribute"/></remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesignTimeReadOnlyAttribute.#ctor(System.Boolean)">
            <summary>
             Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesignTimeReadOnlyAttribute"/> class.
            </summary>
            <param name="readOnly"><see langword="true"/> if the property should be read-only at designtime.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.DesignTimeReadOnlyAttribute.ReadOnly">
            <summary>
             Determines if the property is read-only by design-time.
             Returns <see langword="true" /> if the property is read-only at design-time
             and <see langword="false" /> otherwise.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.EnvironmentalOverridesAttribute">
            <summary>
            Attribute class used to indicate whether a property can be overwritten per environment.<br/>
            The default behavior is that any property can be overwritten.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.EnvironmentalOverridesAttribute.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.EnvironmentalOverridesAttribute"/> class.
            </summary>
            <param name="canOverride"><see langword="true"/> to specify the property can be overwritten per environment. Otherwise <see langword="false"/>.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.EnvironmentalOverridesAttribute.CanOverride">
            <summary>
            <see langword="true"/> if the property can be overwritten per environment. Otherwise <see langword="false"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.EnvironmentalOverridesAttribute.CustomOverridesPropertyType">
            <summary>
            Specifies a custom property type for the overrides property.<br/>
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.EnvironmentalOverridesAttribute.StorageConverterType">
            <summary>
            Specifies a <see cref="T:System.ComponentModel.TypeConverter"/> that should be used to serialize the overriden value to the delta configuration file. <br/>
            This can be used to overwrite a property that doesnt implement <see cref="T:System.IConvertible"/>.  <br/>
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.FilteredFileNameEditorAttribute">
            <summary>
            Specifies additional metadata for the FilteredFileNameEditor editor.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.FilteredFileNameEditorAttribute.#ctor(System.Type,System.String)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.FilteredFileNameEditorAttribute"/> class with the <see cref="T:System.Type"/> containing the resources and the resource key.
            </summary>
            <param name="resourceType">The <see cref="T:System.Type"/> containing the resources.</param>
            <param name="resourceKey">The resource key.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.FilteredFileNameEditorAttribute.Filter">
            <summary>
            Gets the filter for the dialog.
            </summary>
            <value>
            The filter for the dialog.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.FilteredFileNameEditorAttribute.CheckFileExists">
            <summary>
            Gets or sets whether the Open File Dialog should only allow existing files to be selected.
            </summary>
            <value>
            <see langword="true"/> if the Open File Dialog is used to open existing files. Otherwise <see langword="false"/>.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceCategoryAttribute">
            <summary>
            Represents a localized <see cref="T:System.ComponentModel.CategoryAttribute"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceCategoryAttribute.#ctor(System.Type,System.String)">
            <summary>
            Initialize a new instance of the <see cref="T:System.SRCategoryAttribute"/> class with the <see cref="T:System.Type"/> containing the resources and the resource name.
            </summary>
            <param name="category">The resources string name.</param>
            <param name="resourceType">The <see cref="T:System.Type"/> containing the resource strings.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceCategoryAttribute.GetLocalizedString(System.String)">
            <summary>
            Gets the localized string based on the key.
            </summary>
            <param name="value">The key to the string resources.</param>
            <returns>The localized string.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceCategoryAttribute.ResourceType">
            <summary>
            Gets the type that contains the resources.
            </summary>
            <value>
            The type that contains the resources.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceCategoryAttribute.General">
            <summary>
            Returns a localized <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceCategoryAttribute"/> for the General category.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime">
            <summary>
            Class that contains common type names and metadata used by the designtime.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.CommandTypeNames">
            <summary>
            Class that contains common command types used by the designtime.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.CommandTypeNames.AddSatelliteProviderCommand">
            <summary>
            Type name of the AddSatelliteProviderCommand class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.CommandTypeNames.AddApplicationBlockCommand">
            <summary>
            Type name of the AddApplicationBlockCommand class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.CommandTypeNames.AddProviderUsingTypePickerCommand">
            <summary>
            Type name of the TypePickingCollectionElementAddCommand class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.CommandTypeNames.ExportAdmTemplateCommand">
            <summary>
            Type name of the ExportAdmTemplateCommand class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.CommandTypeNames.HiddenCommand">
            <summary>
            Type name of the HiddenCommand class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.CommandTypeNames.AddInstrumentationApplicationBlockCommand">
            <summary>
            Type name of the AddInstrumentationBlockCommand class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.CommandTypeNames.WizardCommand">
            <summary>
            Type name of the WizardCommand class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.EditorTypes">
            <summary>
            Class that contains common editor types used by the designtime.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.EditorTypes.DatePickerEditor">
            <summary>
            Type name of the DatePickerEditor class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.EditorTypes.CollectionEditor">
            <summary>
            Type name of the ElementCollectionEditor, declared class in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.EditorTypes.UITypeEditor">
            <summary>
            Type name of the UITypeEditor class, declared in the System.Drawing Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.EditorTypes.TypeSelector">
            <summary>
            Type name of the TypeSelectionEditor, declared class in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.EditorTypes.FilteredFilePath">
            <summary>
            Type name of the FilteredFileNameEditor, declared class in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.EditorTypes.FrameworkElement">
            <summary>
            Type name of the FrameworkElement, declared class in the PresentationFramework Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.EditorTypes.MultilineText">
            <summary>
            Type name of the MultilineTextEditor class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.EditorTypes.PopupTextEditor">
            <summary>
            Type name of the PopupTextEditor class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.EditorTypes.Flags">
            <summary>
            Type name of the FlagsEditor class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.EditorTypes.RegexTypeEditor">
            <summary>
            Type name of the RegexTypeEditor class, declared in the System.Design Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.EditorTypes.ConnectionStringEditor">
            <summary>
            Type name of the ConnectionStringEditor class, declared in the System.Design Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.EditorTypes.TemplateEditor">
            <summary>
            Type name of the TemplateEditor class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.EditorTypes.OverridesEditor">
            <summary>
            Type name of the IEnvironmentalOverridesEditor interface, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ViewModelTypeNames">
            <summary>
            Class that contains common view model types used by the designtime.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ViewModelTypeNames.TypeNameProperty">
            <summary>
            Type name of the TypeNameProperty class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ViewModelTypeNames.ConfigurationPropertyViewModel">
            <summary>
            Type name of the ConfigurationProperty class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ViewModelTypeNames.SectionViewModel">
            <summary>
            Type name of the SectionViewModel class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ViewModelTypeNames.CollectionEditorContainedElementProperty">
            <summary>
            Type name of the CollectionEditorContainedElementProperty class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ViewModelTypeNames.CollectionEditorContainedElementReferenceProperty">
            <summary>
            Type name of the CollectionEditorContainedElementReferenceProperty class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ViewModelTypeNames.RedirectedSectionSourceProperty">
            <summary>
            Type name of the RedirectedSectionSourceProperty class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ConverterTypeNames">
            <summary>
            Class that contains common converter types used by the designtime runtime.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ConverterTypeNames.RedirectedSectionNameConverter">
            <summary>
            Type name of the RedirectedSectionNameConverter class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.MetadataTypes">
            <summary>
            Class that contains common metadata classes used by the designtime.<br/>
            This class supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.MetadataTypes.RedirectedSectionElementMetadata">
            <summary>This class supports the Enterprise Library infrastructure and is not intended to be used directly from your code.</summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.MetadataTypes.RedirectedSectionElementMetadata.Name">
            <summary>This property supports the Enterprise Library infrastructure and is not intended to be used directly from your code.</summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ValidationTypeNames">
            <summary>
            Class that contains common validation types used by the designtime.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ValidationTypeNames.FileWritableValidator">
            <summary>
            Type name of the FileWritableValidator class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ValidationTypeNames.FileValidator">
            <summary>
            Type name of the FilePathValidator class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ValidationTypeNames.PathExistsValidator">
            <summary>
            Type name of the FilePathExistsValidator class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ValidationTypeNames.RequiredFieldValidator">
            <summary>
            Type name of the RequiredFieldValidator class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ValidationTypeNames.TypeValidator">
            <summary>
            Type name of the TypeValidator class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ValidationTypeNames.SelectedSourceValidator">
            <summary>
            Type name of the SelectedSourceValidator class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.ValidationTypeNames.NameValueCollectionValidator">
            <summary>
            Type name of the NameValueCollectionValidator class, declared in the Configuration.DesignTime Assembly.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.SectionType">
            <summary>
            Type names for well known Enterprise Library <see cref="T:System.Configuration.ConfigurationSection"/> elements.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.SectionType.LoggingSettings">
            <summary>
            Type name for the LoggingSettings section.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.SectionType.DatabaseSettings">
            <summary>
            Type name for the DatabaseSettings section.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.CommonDesignTime.SectionType.ExceptionHandlingSettings">
            <summary>
            Type name for the ExceptionHandlingSettings section.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ReferenceAttribute">
            <summary>
            Attribute class used to indicate that the property is a reference to provider. <br/>
            Reference properties will show an editable dropdown that allows the referred element to be selected.<br/>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ReferenceAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ReferenceAttribute"/> class.
            </summary>
            <param name="targetTypeName">The configuration type name of the provider that used as a reference.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ReferenceAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ReferenceAttribute"/> class.
            </summary>
            <param name="scopeTypeName">The name of a configuration type that contains the references.</param>
            <param name="targetTypeName">The configuration type name of the provider that used as a reference.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ReferenceAttribute.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ReferenceAttribute"/> class.
            </summary>
            <param name="targetType">The configuration type of the provider that used as a reference.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ReferenceAttribute.#ctor(System.Type,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ReferenceAttribute"/> class.
            </summary>
            <param name="scopeType">The configuration type that contains the references.</param>
            <param name="targetType">The configuration type of the provider that used as a reference.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ReferenceAttribute.ScopeType">
            <summary>
            Gets the configuration type that contains the references.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ReferenceAttribute.ScopeIsDeclaringElement">
            <summary>
            Gets or sets a boolean indicating whether only providers can be used that are contained in the current Element View Model.
            </summary>
            <value>
            <see langword="true"/> if only providers can be used that are contained in the current Element View Model. Otherwise <see langword="false"/>.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ReferenceAttribute.TargetType">
            <summary>
            Gets the configuration type of the provider that used as a reference.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceDescriptionAttribute">
            <summary>
            A customized version of <see cref="T:System.ComponentModel.DescriptionAttribute"/> that can
            load the string from assembly resources instead of just a hard-wired
            string.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceDescriptionAttribute.#ctor">
            <summary>
            Create a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceDescriptionAttribute"/> where
            the type and name of the resource is set via properties.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceDescriptionAttribute.#ctor(System.Type,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceDescriptionAttribute"/> class.
            </summary>
            <param name="resourceType">Type used to locate the assembly containing the resources.</param>
            <param name="resourceName">Name of the entry in the resource table.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceDescriptionAttribute.ResourceType">
            <summary>
            A type contained in the assembly we want to get our display name from.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceDescriptionAttribute.ResourceName">
            <summary>
            Name of the string resource containing our display name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceDescriptionAttribute.Description">
            <summary>
            Gets the description for a property, event, or public void method that takes no arguments stored in this attribute.
            </summary>
            <returns>
            The display name.
            </returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceDisplayNameAttribute">
            <summary>
            A customized version of <see cref="T:System.ComponentModel.DisplayNameAttribute"/> that can
            load the string from assembly resources instead of just a hard-wired
            string.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceDisplayNameAttribute.#ctor">
            <summary>
            Create a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceDisplayNameAttribute"/> where
            the type and name of the resource is set via properties.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceDisplayNameAttribute.#ctor(System.Type,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceDisplayNameAttribute"/> class.
            </summary>
            <param name="resourceType">Type used to locate the assembly containing the resources.</param>
            <param name="resourceName">Name of the entry in the resource table.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceDisplayNameAttribute.ResourceType">
            <summary>
            A type contained in the assembly we want to get our display name from.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceDisplayNameAttribute.ResourceName">
            <summary>
            Name of the string resource containing our display name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ResourceDisplayNameAttribute.DisplayName">
            <summary>
            Gets the display name for a property, event, or public void method that takes no arguments stored in this attribute.
            </summary>
            <returns>
            The display name.
            </returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.HandlesSectionAttribute">
            <summary>
             Indicates that this assembly handles the <see cref="T:System.Configuration.ConfigurationSection"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.HandlesSectionAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.HandlesSectionAttribute"/> class.
            </summary>
            <param name="sectionName"></param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.HandlesSectionAttribute.SectionName">
            <summary>
             Name of the section handled by this assembly.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.HandlesSectionAttribute.ClearOnly">
            <summary>
            Indicates this section should be cleared during save, but there is no 
            direct handler for it.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.TypeSelectorIncludes">
            <summary>
            Provides attributes for the filter of types.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.TypeSelectorIncludes.None">
            <summary>
            No filter are applied to types.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.TypeSelectorIncludes.AbstractTypes">
            <summary>
            Inclue abstract types in the filter.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.TypeSelectorIncludes.Interfaces">
            <summary>
            Inclue interfaces in the filter.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.TypeSelectorIncludes.BaseType">
            <summary>
            Inclue base types in the filter.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.TypeSelectorIncludes.NonpublicTypes">
            <summary>
            Inclue non public types in the filter.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.TypeSelectorIncludes.All">
            <summary>
            Include all types in the filter.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.Validation.ValidationAttribute">
            <summary>
             Defines the type of attribute to apply this configuration property or field.
            </summary>
             <remarks>
             This attribute is applied to create validators for use in the configuration design-time.
             </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.Validation.ValidationAttribute.#ctor(System.String)">
            <summary>
             Creates an instance of ValidationAttribute with the validator type specified by <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.Validation.ValidationAttribute.#ctor(System.Type)">
            <summary>
             Creates an instance of the ValidationAttribute with the validator type specified by <see cref="T:System.Type"/>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.Validation.ValidationAttribute.CreateValidator">
            <summary>
             Creates a validator objects.   This is expected to return a Validator type from
             the Microsoft.Practices.EnterpriseLibrary.Configuration.Design namespace.  
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.Validation.ValidationAttribute.ValidatorType">
            <summary>
             Retrieves the validator <see cref="T:System.Type"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.Validation.ValidationAttribute.TypeId">
            <summary>
            When implemented in a derived class, gets a unique identifier for this <see cref="T:System.Attribute"/>.
            </summary>
            <returns>
            An <see cref="T:System.Object"/> that is a unique identifier for the attribute.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.Validation.ElementValidationAttribute">
            <summary>
            Indicates an element level validator.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.Validation.ElementValidationAttribute.#ctor(System.String)">
            <summary>
             Creates an instance of ElementValidationAttribute with the validator type specified by <see cref="T:System.String"/>.
            </summary>
            <param name="validatorTypeName"></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.Validation.ElementValidationAttribute.#ctor(System.Type)">
            <summary>
             Creates an instance of the ElementValidationAttribute with the validator type specified by <see cref="T:System.Type"/>
            </summary>
            <param name="validatorType"></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.Validation.ElementValidationAttribute.CreateValidator">
            <summary>
             Creates a validator objects.   This is expected to return a Validator type from
             the Microsoft.Practices.EnterpriseLibrary.Configuration.Design namespace.  
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.Validation.ElementValidationAttribute.ValidatorType">
            <summary>
             Retrieves the validator <see cref="T:System.Type"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.Validation.ElementValidationAttribute.TypeId">
            <summary>
            When implemented in a derived class, gets a unique identifier for this <see cref="T:System.Attribute"/>.
            </summary>
            <returns>
            An <see cref="T:System.Object"/> that is a unique identifier for the attribute.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ViewModelAttribute">
             <summary>
             Attribute class used to specify a specific View Model derivement or visual representation to be used on the target element.
             </summary>
             <remarks>
             
             <para>The View Model Type should derive from the ElementViewModel or Property class in the Configuration.Design assembly. <br/>
             As this attribute can be applied to the configuration directly and we dont want to force a dependency on the Configuration.Design assembly <br/>
             You can specify the View Model Type in a loosy coupled fashion, passing a qualified name of the type.</para>
            
             </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ViewModelAttribute.#ctor(System.Type)">
            <summary>
             Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ViewModelAttribute"/> class.
            </summary>
            <param name="modelType">The type of the View Model that should be used for the annotated element.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ViewModelAttribute.#ctor(System.String)">
            <summary>
             Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ViewModelAttribute"/> class.
            </summary>
            <param name="modelTypeName">The type name of the View Model that should be used for the annotated element.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.ViewModelAttribute.ModelType">
            <summary>
             Gets the View Model Type that should be used to bind the annotated element to its view.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.WizardCommandAttribute">
            <summary>
            Marks the annotated class as a configuration wizard that can be found
            by the configuration design time tools.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.WizardCommandAttribute.#ctor">
            <summary>
             Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.WizardCommandAttribute"/>
             with the default wizard command model type specified.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.WizardCommandAttribute.#ctor(System.String)">
            <summary>
             Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.WizardCommandAttribute"/>
             with the command model type specified as a string.
            </summary>
            <param name="commandModelTypeName"></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.WizardCommandAttribute.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.WizardCommandAttribute"/> with 
            the command model type specified by <see cref="T:System.Type"/>.
            </summary>
            <param name="commandModelType"></param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.WizardCommandAttribute.WizardType">
            <summary>
            The type of the wizard to instantiate must derive from WizardModel or will result on an error at runtime.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.WizardCommandAttribute.WizardTypeName">
            <summary>
            The name of the type of the wizard to instantiate.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.EnumConverterWithExclusiveStandardValues`1">
            <summary>
            Type converter used to work around enums with enums wrongly marked as "flags".
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.EnumConverterWithExclusiveStandardValues`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.EnumConverterWithExclusiveStandardValues`1"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.EnumConverterWithExclusiveStandardValues`1.GetStandardValuesExclusive(System.ComponentModel.ITypeDescriptorContext)">
            <summary>
            Indicates where the standard values are exclusive.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException">
            <summary>
            Exception class for exceptions that occur when reading configuration metadata from a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceSection"/>.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceSection"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException"/> class.
            </summary>
            <param name="message">A message that describes why this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException"/> exception was thrown.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException"/> class.
            </summary>
            <param name="message">A message that describes why this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException"/> exception was thrown.</param>
            <param name="innerException">The inner exception that caused this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException"/> exception to be thrown.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException.#ctor(System.String,System.Exception,System.String,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException"/> class.
            </summary>
            <param name="message">A message that describes why this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException"/> exception was thrown.</param>
            <param name="innerException">The inner exception that caused this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException"/> exception to be thrown.</param>
            <param name="filename">The path to the configuration file that caused this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException"/> exception to be thrown.</param>
            <param name="line">The line number within the configuration file at which this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException"/> exception was thrown.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceErrorsException"/> class.
            </summary>
            <param name="info">The object that holds the information to be serialized.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.HierarchicalConfigurationSourceHandler">
            <summary>
            Implements hierarchical merge behavior to <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> implementations.<br/>
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceHandler"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.HierarchicalConfigurationSourceHandler.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.HierarchicalConfigurationSourceHandler"/>.
            </summary>
            <param name="localSource">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> instance that should be extended.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.HierarchicalConfigurationSourceHandler.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.HierarchicalConfigurationSourceHandler"/>.
            </summary>
            <param name="localSource">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> instance that should be extended.</param>
            <param name="parentSource">An <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> instance the <paramref name="localSource"/> should be merged with.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.HierarchicalConfigurationSourceHandler.Initialize">
            <summary>
            Performs intialization logic for this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.HierarchicalConfigurationSourceHandler"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.HierarchicalConfigurationSourceHandler.DoRefresh">
            <summary>
            Performs re-intialization logic for this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.HierarchicalConfigurationSourceHandler"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.HierarchicalConfigurationSourceHandler.DoCheckGetSection(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            Checks whether the result of a call to <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.GetSection(System.String)"/> should be merged.<br/>
            If the call should be merged, performs the merge behavior and returns the resulting <see cref="T:System.Configuration.ConfigurationSection"/> intance.<br/>
            If the call should not be merged returns <paramref name="configurationSection"/>.
            </summary>
            <param name="sectionName">The name of the section that was retrieved from configuration.</param>
            <param name="configurationSection">The section that was retrieved from configuration.</param>
            <returns>The resulting <see cref="T:System.Configuration.ConfigurationSection"/> instance.</returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.GetSection(System.String)"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ICloneableConfigurationElement">
            <summary>
            Interface used to implement custom clone behavior for a <see cref="T:System.Configuration.ConfigurationElement"/>.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSectionCloner"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ICloneableConfigurationElement.CreateFullClone">
            <summary>
            Creates a deep clone of the current <see cref="T:System.Configuration.ConfigurationElement"/> instance.
            </summary>
            <returns>
            A deep clone of the current <see cref="T:System.Configuration.ConfigurationElement"/> instance.
            </returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.MergeableConfigurationCollectionFactory">
            <summary>
             Factory for creating <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IMergeableConfigurationElementCollection"/>s.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.MergeableConfigurationCollectionFactory.GetCreateMergeableCollection(System.Configuration.ConfigurationElementCollection)">
            <summary>
             Creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IMergeableConfigurationElementCollection"/> based on a ConfigurationElementCollection type.
            </summary>
            <param name="collection"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.RedirectedSectionElement">
            <summary>
            Configuration element for a redirected section.<br/>
            The <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedConfigurationElement.Name"/> property is used to identify the redireced section, based on its section name.<br/>
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceSection"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedConfigurationElement">
            <summary>
            Represents a named <see cref="T:System.Configuration.ConfigurationElement"/> where the name is the key to a collection.
            </summary>
            <remarks>
            This class is used in conjunction with a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedElementCollection`1"/>.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IObjectWithName">
            <summary>
            Represents the abstraction of an object with a name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IObjectWithName.Name">
            <summary>
            Gets the name.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedConfigurationElement.nameProperty">
            <summary>
            Name of the property that holds the name of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedConfigurationElement"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedConfigurationElement.#ctor">
            <summary>
            Initialize a new instance of a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedConfigurationElement"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedConfigurationElement.#ctor(System.String)">
            <summary>
            Intialize a new instance of a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedConfigurationElement"/> class with a name.
            </summary>
            <param name="name">The name of the element.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedConfigurationElement.DeserializeElement(System.Xml.XmlReader)">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            Updates the configuration properties of the receiver with the information in the current element in the <paramref name="reader"/>.
            </summary>
            <param name="reader">The reader over the configuration file.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NamedConfigurationElement.Name">
            <summary>
            Gets or sets the name of the element.
            </summary>
            <value>
            The name of the element.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.RedirectedSectionElement.SourceName">
            <summary>
            Gets the name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceElement"/> which contains the configuration section.
            </summary>
            <value>
            The name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceElement"/> which contains the configuration section.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.AssemblyQualifiedTypeNameConverter">
            <summary>
            Represents a configuration converter that converts a string to <see cref="T:System.Type"/> based on a fully qualified name.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.AssemblyQualifiedTypeNameConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <summary>
            Returns the assembly qualified name for the passed in Type.
            </summary>
            <param name="context">The container representing this System.ComponentModel.TypeDescriptor.</param>
            <param name="culture">Culture info for assembly</param>
            <param name="value">Value to convert.</param>
            <param name="destinationType">Type to convert to.</param>
            <returns>Assembly Qualified Name as a string</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.AssemblyQualifiedTypeNameConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Returns a type based on the assembly qualified name passed in as data.
            </summary>
            <param name="context">The container representing this System.ComponentModel.TypeDescriptor.</param>
            <param name="culture">Culture info for assembly.</param>
            <param name="value">Data to convert.</param>
            <returns>Type of the data</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.BlockSectionNames">
            <summary>
            A set of string constants listing the names of the configuration
            sections used by the standard set of Entlib blocks.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.BlockSectionNames.Data">
            <summary>
            Data Access Application Block custom settings
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.BlockSectionNames.Logging">
            <summary>
            Logging Application Block section name
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.BlockSectionNames.ExceptionHandling">
            <summary>
            Exception Handling Application Block section name
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.BlockSectionNames.PolicyInjection">
            <summary>
            Policy injection section name
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.BlockSectionNames.Validation">
            <summary>
             Validation section name
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ByteArrayTypeConverter">
            <summary>
            Represents a configuration converter that converts a byte array to and from a string representation by using base64 encoding.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ByteArrayTypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Returns whether this converter can convert an object of the given type to the type of this converter. 
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext"/> object.</param>
            <param name="sourceType">A <see cref="T:System.Type"/> that represents the type you want to convert from. </param>
            <returns><see langword="true"/> if this converter can perform the conversion; otherwise, <see langword="falase"/>. </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ByteArrayTypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts the given value to the type of this converter.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext"/> object.</param>
            <param name="culture">A <see cref="T:System.Globalization.CultureInfo"/> object.</param>
            <param name="value">An <see cref="T:System.Object"/> that represents the converted value. </param>
            <returns>An <see cref="T:System.Object"/> that represents the converted value. </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ByteArrayTypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Returns whether this converter can convert the object to the specified type. 
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext"/> object.</param>
            <param name="destinationType">A <see cref="T:System.Type"/> that represents the type you want to convert to..</param>
            <returns><b>true</b> if the converter can convert to the specified type, <b>false</b> otherwise.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ByteArrayTypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <summary>
            Converts the given value object to the specified type, using the arguments. 
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext"/> object.</param>
            <param name="culture">A <see cref="T:System.Globalization.CultureInfo"/> object.</param>
            <param name="value">The <see cref="T:System.Object"/> to convert.</param>
            <param name="destinationType">The <see cref="T:System.Type"/> to convert the value parameter to.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationFileSourceWatcher">
            <summary>
            Watcher for configuration sections in configuration files.
            </summary>
            <remarks>
            This implementation uses a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeFileWatcher"/> to watch for changes 
            in the configuration files.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceWatcher">
            <summary>
            Reacts to changes on the medium on which a set of configuration sections are serialized.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceWatcher.#ctor(System.String,System.Boolean,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceWatcher"/> class.
            </summary>
            <param name="configSource">The identification of the medium.</param>
            <param name="refresh"><b>true</b> if changes should be notified, <b>false</b> otherwise.</param>
            <param name="changed">The callback for changes notification.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceWatcher.StartWatching">
            <summary>
            Starts watching for changes on the serialization medium.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceWatcher.StopWatching">
            <summary>
            Stops watching for changes on the serialization medium.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceWatcher.ConfigSource">
            <summary>
            Gets or sets the identification of the medium where the watched set of configuration sections is stored.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceWatcher.WatchedSections">
            <summary>
            Gets or sets the collection of watched sections.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceWatcher.Watcher">
            <summary>
            Gets the watcher over the serialization medium.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationFileSourceWatcher.#ctor(System.String,System.String,System.Boolean,System.Int32,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationFileSourceWatcher"/> class.
            </summary>
            <param name="configurationFilepath">The path for the configuration file to watch.</param>
            <param name="configSource">The identification of the configuration source.</param>
            <param name="refresh"><b>true</b> if changes should be notified, <b>false</b> otherwise.</param>
            <param name="refreshInterval">The poll interval in milliseconds.</param>
            <param name="changed">The callback for changes notification.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationFileSourceWatcher.GetFullFileName(System.String,System.String)">
            <summary>
            Gets the full file name associated to the configuration source.
            </summary>
            <param name="configurationFilepath">The path for the main configuration file.</param>
            <param name="configSource">The configuration source to watch.</param>
            <returns>The path to the configuration file to watch. It will be the same as <paramref name="configurationFilepath"/>
            if <paramref name="configSource"/> is empty, or the full path for <paramref name="configSource"/> considered as a 
            file name relative to the main configuration file.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationFileSourceWatcher.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationFileSourceWatcher.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationFileSourceWatcher.Watcher">
            <summary>
            Gets the watcher over the serialization medium.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceChangedEventArgs">
            <summary>
            Event arguments describing which sections have changed in a configuration source.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceChangedEventArgs.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceChangedEventArgs"/> class.
            </summary>
            <param name="configurationSource">Configuration source that changed.</param>
            <param name="changedSectionNames">Sequence of the section names in <paramref name="configurationSource"/>
            that have changed.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceChangedEventArgs.ConfigurationSource">
            <summary>
            The configuration source that has changed.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceChangedEventArgs.ChangedSectionNames">
            <summary>
            The set of section names that have changed.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceElement">
            <summary>
            Represents the configuration settings that describe an <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement">
            <summary>
            Represents a <see cref="T:System.Configuration.ConfigurationElement"/> that has a name and type.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IObjectWithNameAndType">
            <summary>
            Represents the abstraction of an object with a name and a type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IObjectWithNameAndType.Type">
            <summary>
            Gets the type.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement.typeProperty">
            <summary>
            Name of the property that holds the type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement.#ctor">
            <summary>
            Intialzie an instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement.#ctor(System.String,System.Type)">
            <summary>
            Initialize an instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement"/> class
            </summary>
            <param name="name">The name of the element.</param>
            <param name="type">The <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement.Type"/> that this element is the configuration for.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement.Type">
            <summary>
            Gets or sets the <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement.Type"/> the element is the configuration for.
            </summary>
            <value>
            The <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement.Type"/> the element is the configuration for.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement.TypeName">
            <summary>
            Gets or sets the fully qualified name of the <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement.Type"/> the element is the configuration for.
            </summary>
            <value>
            the fully qualified name of the <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NameTypeConfigurationElement.Type"/> the element is the configuration for.
            </value>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceElement.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceElement"/> class with default values.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceElement.#ctor(System.String,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceElement"/> class with a name and an type.
            </summary>
            <param name="name">The instance name.</param>
            <param name="type">The type for the represented <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceElement.CreateSource">
            <summary>
            Returns a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> configured with the receiver's settings.
            </summary>
            <returns>A new configuration source.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceElement.CreateDesignSource(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.IDesignConfigurationSource)">
            <summary>
             Returns a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.IDesignConfigurationSource"/> configured based on this configuration element.
            </summary>
            <returns>Returns a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.IDesignConfigurationSource"/> or null if this source does not have design-time support.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceFactory">
            <summary>
            Contains factory methods to create configuration sources.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceFactory.Create(System.String)">
            <summary>
            Creates a new configuration sources based on the configuration information from the application's default
            configuration file.
            </summary>
            <param name="name">The name for the desired configuration source.</param>
            <returns>The new configuration source instance described in the configuration file.</returns>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">when no configuration information is found for name <paramref name="name"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="name"/> is null or empty.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceFactory.Create">
            <summary>
            Creates a new configuration sources based on the default configuration information from the 
            application's default configuration file.
            </summary>
            <returns>The new configuration source instance described as the default in the configuration file,
            or a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource"/> if the is no configuration sources configuration.</returns>
            <exception cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceSection">when there is a configuration section but it does not define
            a default configurtion source, or when the configuration for the defined default configuration source is not found.</exception>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceSection">
            <summary>
            Configuration section for the configuration sources.
            </summary>
            <remarks>
            This configuration must reside in the application's default configuration file.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SerializableConfigurationSection">
            <summary>
            Represents a configuration section that can be serialized and deserialized to XML.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SerializableConfigurationSection.GetSchema">
            <summary>
            Returns the XML schema for the configuration section.
            </summary>
            <returns>A string with the XML schema, or <see langword="null"/> (<b>Nothing</b> 
            in Visual Basic) if there is no schema.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SerializableConfigurationSection.ReadXml(System.Xml.XmlReader)">
            <summary>
            Updates the configuration section with the values from an <see cref="T:System.Xml.XmlReader"/>.
            </summary>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> that reads the configuration source located at the element that describes the configuration section.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SerializableConfigurationSection.WriteXml(System.Xml.XmlWriter)">
            <summary>
            Writes the configuration section values as an XML element to an <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <param name="writer">The <see cref="T:System.Xml.XmlWriter"/> that writes to the configuration store.</param>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceSection.SectionName">
            <summary>
            This field supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceSection.GetConfigurationSourceSection">
            <summary>
            Returns the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceSection"/> from the application's default configuration file.
            </summary>
            <returns>The section from the configuration file, or <see langword="null"/> (<b>Nothing</b> in Visual Basic) if the section is not present in the configuration file.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceSection.SelectedSource">
            <summary>
            Gets or sets the name for the default configuration source.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceSection.ParentSource">
            <summary>
            Gets or sets the name for the parent configuration source.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceSection.Sources">
            <summary>
            Gets the collection of defined configuration sources.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceSection.RedirectedSections">
            <summary>
            Gets the collection of redirected sections.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventHandler">
            <summary>
            Event handler called after a configuration has changed.
            </summary>
            <param name="sender">
            <para>The source of the event.</para>
            </param>
            <param name="e">
            <para>A <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventArgs"/> that contains the event data.</para>
            </param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventArgs">
            <summary>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventArgs.#ctor(System.String)">
            <summary>
            <para>Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventArgs"/> class with the section name</para>
            </summary>
            <param name="sectionName"><para>The section name of the changes.</para></param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventArgs.SectionName">
            <summary>
            <para>Gets the section name where the changes occurred.</para>
            </summary>
            <value>
            <para>The section name where the changes occurred.</para>
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationElementTypeAttribute">
            <summary>
            Indicates the configuration object type that is used for the attributed object.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationElementTypeAttribute.#ctor">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationElementTypeAttribute"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationElementTypeAttribute.#ctor(System.Type)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationElementTypeAttribute"/> class with the configuration object type.
            </summary>
            <param name="configurationType">The <see cref="T:System.Type"/> of the configuration object.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationElementTypeAttribute.#ctor(System.String)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationElementTypeAttribute"/> class with the configuration object type.
            </summary>
            <param name="typeName">The <see cref="T:System.Type"/> name of the configuration object.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationElementTypeAttribute.ConfigurationType">
            <summary>
            Gets the <see cref="T:System.Type"/> of the configuration object.
            </summary>
            <value>
            The <see cref="T:System.Type"/> of the configuration object.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationElementTypeAttribute.TypeName">
            <summary>
            Gets <see cref="T:System.Type"/> name of the configuration object.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationFileChangedEventArgs">
            <summary>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationFileChangedEventArgs.#ctor(System.String,System.String)">
            <summary>
            <para>Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventArgs"/> class with the 
            configuration file and the section name.</para>
            </summary>
            <param name="configurationFile"><para>The configuration file where the change occured.</para></param>
            <param name="sectionName"><para>The section name of the changes.</para></param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationFileChangedEventArgs.ConfigurationFile">
            <summary>
            <para>Gets the configuration file of the data that changed.</para>
            </summary>
            <value>
            <para>The configuration file of the data that changed.</para>
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomProviderDataHelper`1">
            <summary>
            Generic helper class for custom provider configuration objects.
            </summary>
            <remarks>
            The helper class encapsulates the logic to manage an unknown set of properties in <see cref="T:System.Configuration.ConfigurationElement"/>s.
            This logic cannot be inherited by the configuration objects because these objects must inherit from the base configuration 
            object type for the hierarchy of providers the configuration object represents.
            </remarks>
            <typeparam name="T">The type of the custom provider configuration object.</typeparam>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomProviderDataHelper`1.propertiesCollection">
            <summary>
            Collection of managed properties
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomProviderDataHelper`1.#ctor(`0)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomProviderDataHelper`1"/> class for a configuration object.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomProviderDataHelper`1.HandleIsModified">
            <summary>
            Concrete implementation of <see cref="M:System.Configuration.ConfigurationElement.IsModified"/>.
            </summary>
            <returns><b>true</b> if the managed element has been modified.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomProviderDataHelper`1.HandleOnDeserializeUnrecognizedAttribute(System.String,System.String)">
            <summary>
            Concrete implementation of <see cref="M:System.Configuration.ConfigurationElement.OnDeserializeUnrecognizedAttribute(System.String,System.String)"/>.
            </summary>
            <param name="name">The name of the unrecognized attribute.</param>
            <param name="value">The value of the unrecognized attribute.</param>
            <returns><code>true</code> when an unknown attribute is encountered while deserializing.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomProviderDataHelper`1.HandleReset(System.Configuration.ConfigurationElement)">
            <summary>
            Concrete implementation of <see cref="M:System.Configuration.ConfigurationElement.Reset(System.Configuration.ConfigurationElement)"/>.
            </summary>
            <param name="parentElement">The parent node of the configuration element.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomProviderDataHelper`1.HandleSetAttributeValue(System.String,System.String)">
            <summary>
            Sets the value to the specified attribute and updates the properties collection.
            </summary>
            <param name="key">The key of the attribute to set.</param>
            <param name="value">The value to set for the attribute.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomProviderDataHelper`1.HandleUnmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)">
            <summary>
            Concrete implementation of <see cref="M:System.Configuration.ConfigurationElement.Unmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)"/>.
            </summary>
            <param name="sourceElement">A <see cref="T:System.Configuration.ConfigurationElement"/> object at the current level containing a merged view of the properties.</param>
            <param name="parentElement">The parent <see cref="T:System.Configuration.ConfigurationElement"/> object, or a <see langword="null"/> reference if this is the top level.</param>
            <param name="saveMode">A <see cref="T:System.Configuration.ConfigurationSaveMode"/> object that determines which property values to include.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomProviderDataHelper`1.IsKnownPropertyName(System.String)">
            <summary>
            Returns whether the property name is known in advance, i.e. it is not a dynamic property.
            </summary>
            <param name="propertyName">The property name.</param>
            <returns><b>true</b> if the property is known in advance, <b>false</b> otherwise.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomProviderDataHelper`1.Attributes">
            <summary>
            Gets the collection of custom attributes.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomProviderDataHelper`1.Properties">
            <summary>
            Gets a <see cref="T:System.Configuration.ConfigurationPropertyCollection"/> of the properties that are defined for this configuration element when implemented in a derived class. 
            </summary>
            <value>
            A <see cref="T:System.Configuration.ConfigurationPropertyCollection"/> of the properties that are defined for this configuration element when implemented in a derived class. 
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource">
            <summary>
            Represents a configuration source that is backed by a dictionary of named objects.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource.sections">
            <summary>
            This field supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource.eventHandlers">
            <summary>
            This field supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource.GetSection(System.String)">
            <summary>
            Retrieves the specified <see cref="T:System.Configuration.ConfigurationSection"/>.
            </summary>
            <param name="sectionName">The name of the section to be retrieved.</param>
            <returns>The specified <see cref="T:System.Configuration.ConfigurationSection"/>, or <see langword="null"/> (<b>Nothing</b> in Visual Basic)
            if a section by that name is not found.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource.Add(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            Adds a <see cref="T:System.Configuration.ConfigurationSection"/> to the configuration source.
            </summary>
            <remarks>
            If a configuration section with the specified name already exists it will be replaced.
            </remarks>
            <param name="name">The name by which the <paramref name="section"/> should be added.</param>
            <param name="section">The configuration section to add.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource.Remove(System.String)">
            <summary>
            Removes a <see cref="T:System.Configuration.ConfigurationSection"/> from the configuration source.
            </summary>
            <param name="sectionName">The name of the section to remove.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource.Contains(System.String)">
            <summary>
            Determines if a section name exists in the source.
            </summary>
            <param name="name">The section name to find.</param>
            <returns><b>true</b> if the section exists; otherwise, <b>false</b>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource.AddSectionChangeHandler(System.String,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventHandler)">
            <summary>
            Adds a handler to be called when changes to the section named <paramref name="sectionName"/> are detected.
            </summary>
            <param name="sectionName">The name of the section to watch for.</param>
            <param name="handler">The handler for the change event to add.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource.RemoveSectionChangeHandler(System.String,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventHandler)">
            <summary>
            Removes a handler to be called when changes to section <code>sectionName</code> are detected.
            </summary>
            <param name="sectionName">The name of the watched section.</param>
            <param name="handler">The handler for the change event to remove.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource.OnSourceChangedEvent(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceChangedEventArgs)">
            <summary>
            Raises the <see cref="E:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource.SourceChanged"/> event.
            </summary>
            <param name="args">Event arguments</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="E:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource.SourceChanged">
            <summary>
            Raised when anything in the source changes.
            </summary>
            <remarks>
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DictionaryConfigurationSource"/> does not report any
            configuration change events.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSourceElement">
            <summary>
            Represents the configuration settings that describe a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSourceElement.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSourceElement"/> class with a default name and an empty path.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSourceElement.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSourceElement"/> class with a name and an path.
            </summary>
            <param name="name">The instance name.</param>
            <param name="filePath">The file path.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSourceElement.CreateSource">
            <summary>
            Returns a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSource"/> configured with the receiver's settings.
            </summary>
            <returns>A new configuration source.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSourceElement.CreateDesignSource(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.IDesignConfigurationSource)">
            <summary>
             Returns a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.IDesignConfigurationSource"/> configured based on this configuration element.
            </summary>
            <returns>Returns a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Design.IDesignConfigurationSource"/> or null if this source does not have design-time support.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.FileConfigurationSourceElement.FilePath">
            <summary>
            Gets or sets the file path. This is a required field.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.GenericEnumeratorWrapper`1">
            <devdoc>
            Represents a genereic enumerator for the NamedElementCollection.
            </devdoc>	
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ICustomProviderData">
            <summary>
            Represents the configuration settings for a custom provider.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ICustomProviderData.Name">
            <summary>
            Gets the name for the represented provider.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ICustomProviderData.Attributes">
            <summary>
            Gets the attributes for the represented provider.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IHelperAssistedCustomConfigurationData`1">
            <summary>
            This interface must be implemented by configuration objects for custom providers that rely
            on a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomProviderDataHelper`1"/> to perform the dynamic properties management.
            </summary>
            <remarks>
            This interface is generic so that the helper can be strongly-typed.
            </remarks>
            <typeparam name="T">The configuration object type. It must match the type implementing the interface.</typeparam>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IHelperAssistedCustomConfigurationData`1.BaseGetPropertyValue(System.Configuration.ConfigurationProperty)">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            Returns the value for the specified property using the inherited implementation.
            </summary>
            <param name="property">The property to get the value from.</param>
            <returns>The value for the property.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IHelperAssistedCustomConfigurationData`1.BaseIsModified">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            Returns the modification status using the inherited implementation.
            </summary>
            <returns><b>true</b> if the configuration element has been modified, <b>false</b> otherwise.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IHelperAssistedCustomConfigurationData`1.BaseReset(System.Configuration.ConfigurationElement)">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            Resets the internal state of the configuration object using the inherited implementation.
            </summary>
            <param name="parentElement">The parent node of the configuration element.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IHelperAssistedCustomConfigurationData`1.BaseSetPropertyValue(System.Configuration.ConfigurationProperty,System.Object)">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            Sets the value for the specified property using the inherited implementation.
            </summary>
            <param name="property">The property to set the value to.</param>
            <param name="value">The new value for the property.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IHelperAssistedCustomConfigurationData`1.BaseUnmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            Modifies the <b>ConfigurationElement</b> object to remove all values that should not be saved using the inherited implementation.
            </summary>
            <param name="sourceElement">A <see cref="T:System.Configuration.ConfigurationElement"/> object at the current level containing a merged view of the properties.</param>
            <param name="parentElement">The parent <b>ConfigurationElement</b> object, or a null reference (Nothing in Visual Basic) if this is the top level.</param>
            <param name="saveMode">A <see cref="T:System.Configuration.ConfigurationSaveMode"/> object that determines which property values to include.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IHelperAssistedCustomConfigurationData`1.Helper">
            <summary>
            Gets the helper that manages the configuration information.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NullConfigurationSource">
            <summary>
            Represents a null configuration source that always returns null for a section.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NullConfigurationSource.GetSection(System.String)">
            <summary>
            Returns null for the section.
            </summary>
            <param name="sectionName">The section name to retrieve.</param>
            <returns>Always <see langword="null"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NullConfigurationSource.Add(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            Null implementation of <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Add(System.String,System.Configuration.ConfigurationSection)"/> that 
            ignores the request.
            </summary>
            <param name="sectionName">The name by which the <paramref name="configurationSection"/> should be added.</param>
            <param name="configurationSection">The configuration section to add.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NullConfigurationSource.Remove(System.String)">
            <summary>
            Null implementation of <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource.Remove(System.String)"/> that 
            ignores the request.
            </summary>
            <param name="sectionName">The name of the section to remove.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NullConfigurationSource.AddSectionChangeHandler(System.String,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventHandler)">
            <summary>
            Adds a handler to be called when changes to section <code>sectionName</code> are detected.
            </summary>
            <param name="sectionName">The name of the section to watch for.</param>
            <param name="handler">The handler.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NullConfigurationSource.RemoveSectionChangeHandler(System.String,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationChangedEventHandler)">
            <summary>
            Remove a handler to be called when changes to section <code>sectionName</code> are detected.
            </summary>
            <param name="sectionName">The name of the section to watch for.</param>
            <param name="handler">The handler.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NullConfigurationSource.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="E:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.NullConfigurationSource.SourceChanged">
            <summary>
            Event raised when configuration source contents have changed.
            </summary>
            <remarks>This class never raises this event.</remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeFileWatcher">
            <summary>
            <para>Represents an <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.IConfigurationChangeWatcher"/> that watches a file.</para>
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher">
            <summary>
            <para>Represents an <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.IConfigurationChangeWatcher"/> that watches a file.</para>
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.IConfigurationChangeWatcher">
            <summary>
            <para>Provides a way to watch for changes to configuration in storage.</para>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.IConfigurationChangeWatcher.StartWatching">
            <summary>
            When implemented by a subclass, starts the object watching for configuration changes
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.IConfigurationChangeWatcher.StopWatching">
            <summary>
            When implemented by a subclass, stops the object from watching for configuration changes
            </summary>
        </member>
        <member name="E:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.IConfigurationChangeWatcher.ConfigurationChanged">
            <summary>
            Event raised when the underlying persistence mechanism for configuration notices that
            the persistent representation of configuration information has changed.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.IConfigurationChangeWatcher.SectionName">
            <summary>
            When implemented by a subclass, returns the section name that is being watched.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.SetDefaultPollDelayInMilliseconds(System.Int32)">
            <summary>
            Sets the default poll delay.
            </summary>
            <param name="newDefaultPollDelayInMilliseconds">The new default poll.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.ResetDefaultPollDelay">
            <summary>
            Reset the default to 15000 millisecond.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.SetPollDelayInMilliseconds(System.Int32)">
            <summary>
            Sets the poll delay in milliseconds.
            </summary>
            <param name="newDelayInMilliseconds">
            The poll delay in milliseconds.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.#ctor">
            <summary>
            <para>Initialize a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher"/> class</para>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.Finalize">
            <summary>
            <para>
            Allows an <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeFileWatcher"/> to attempt to free 
            resources and perform other cleanup operations before the 
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeFileWatcher"/> is reclaimed by garbage collection.
            </para>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.StartWatching">
            <summary>
            <para>Starts watching the configuration file.</para>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.StopWatching">
            <summary>
            <para>Stops watching the configuration file.</para>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.Dispose">
            <summary>
            <para>Releases the unmanaged resources used by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeFileWatcher"/> and optionally releases the managed resources.</para>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.Disposing(System.Boolean)">
            <summary>
            <para>Releases the unmanaged resources used by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeFileWatcher"/> and optionally releases the managed resources.</para>
            </summary>
            <param name="isDisposing">
            <para><see langword="true"/> to release both managed and unmanaged resources; <see langword="false"/> to release only unmanaged resources.</para>
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.Dispose(System.Boolean)">
            <summary>
            <para>Releases the unmanaged resources used by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeFileWatcher"/> and optionally releases the managed resources.</para>
            </summary>
            <param name="isDisposing">
            <para><see langword="true"/> to release both managed and unmanaged resources; <see langword="false"/> to release only unmanaged resources.</para>
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.OnConfigurationChanged">
            <summary>
            <para>Raises the <see cref="E:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.ConfigurationChanged"/> event.</para>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.GetCurrentLastWriteTime">
            <summary>
            <para>Returns the <see cref="T:System.DateTime"/> of the last change of the information watched</para>
            </summary>
            <returns>The <see cref="T:System.DateTime"/> of the last modificaiton, or <code>DateTime.MinValue</code> if the information can't be retrieved</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.BuildEventData">
            <summary>
            Builds the change event data, in a suitable way for the specific watcher implementation
            </summary>
            <returns>The change event information</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.GetEventSourceName">
            <summary>
            Returns the source name to use when logging events
            </summary>
            <returns>The event source name</returns>
        </member>
        <member name="E:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.ConfigurationChanged">
            <summary>
            Event raised when the underlying persistence mechanism for configuration notices that
            the persistent representation of configuration information has changed.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeWatcher.SectionName">
            <summary>
            <para>Gets the name of the configuration section being watched.</para>
            </summary>
            <value>
            <para>The name of the configuration section being watched.</para>
            </value>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeFileWatcher.#ctor(System.String,System.String)">
            <summary>
            <para>Initialize a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeFileWatcher"/> class with the path to the configuration file and the name of the section</para>
            </summary>
            <param name="configFilePath">
            <para>The full path to the configuration file.</para>
            </param>
            <param name="configurationSectionName">
            <para>The name of the configuration section to watch.</para>
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeFileWatcher.GetCurrentLastWriteTime">
            <summary>
            <para>Returns the <see cref="T:System.DateTime"/> of the last change of the information watched</para>
            <para>The information is retrieved using the watched file modification timestamp</para>
            </summary>
            <returns>The <see cref="T:System.DateTime"/> of the last modificaiton, or <code>DateTime.MinValue</code> if the information can't be retrieved</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeFileWatcher.BuildEventData">
            <summary>
            Builds the change event data, including the full path of the watched file
            </summary>
            <returns>The change event information</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeFileWatcher.GetEventSourceName">
            <summary>
            Returns the source name to use when logging events
            </summary>
            <returns>The event source name</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Storage.ConfigurationChangeFileWatcher.SectionName">
            <summary>
            <para>Gets the name of the configuration section being watched.</para>
            </summary>
            <value>
            <para>The name of the configuration section being watched.</para>
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource">
            <summary>
            Represents an <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> that retrieves the configuration information from the 
            application's default configuration file using the <see cref="T:System.Configuration.ConfigurationManager"/> API.
            </summary>
            <remarks>
            <para>
            The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource"/> is a wrapper over the static configuration access API provided by 
            <see cref="T:System.Configuration.ConfigurationManager"/> and watches for changes in the configuration files to refresh the 
            configuration when a change is detected.
            </para>
            </remarks>
            <seealso cref="T:System.Configuration.ConfigurationManager"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource"/> class that will refresh changes
            according to the value of the <paramref name="refresh"/> parameter.
            </summary>
            <param name="refresh"><see langword="true"/> if changes to the configuration file should be notified.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource.#ctor(System.Boolean,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource"/> class that will refresh changes
            according to the value of the <paramref name="refresh"/> parameter, polling every 
            <paramref name="refreshInterval"/> milliseconds.
            </summary>
            <param name="refresh"><see langword="true"/> if changes to the configuration file should be notified.</param>
            <param name="refreshInterval">The poll interval in milliseconds.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource.DoAdd(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            Adds a <see cref="T:System.Configuration.ConfigurationSection"/> to the configuration and saves the configuration source.
            </summary>
            <remarks>
            If a configuration section with the specified name already exists it will be replaced.
            </remarks>
            <param name="sectionName">The name by which the <paramref name="configurationSection"/> should be added.</param>
            <param name="configurationSection">The configuration section to add.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource.DoRemove(System.String)">
            <summary>
            Removes a <see cref="T:System.Configuration.ConfigurationSection"/> from the configuration and saves the configuration source.
            </summary>
            <param name="sectionName">The name of the section to remove.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource.Save(System.String,System.Configuration.ConfigurationSection)">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            Adds or replaces <paramref name="configurationSection"/> under name <paramref name="section"/> in the configuration 
            and saves the configuration file.
            </summary>
            <param name="section">The name for the section.</param>
            <param name="configurationSection">The configuration section to add or replace.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource.DoGetSection(System.String)">
            <summary>
            Retrieves the specified <see cref="T:System.Configuration.ConfigurationSection"/> from the configuration file.
            </summary>
            <param name="sectionName">The section name.</param>
            <returns>The section, or <see langword="null"/> if it doesn't exist.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource.RefreshAndValidateSections(System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.ICollection{System.String}@,System.Collections.Generic.IDictionary{System.String,System.String}@)">
            <summary>
            Refreshes the configuration sections from the main configuration file and determines which sections have 
            suffered notifications and should be notified to registered handlers.
            </summary>
            <param name="localSectionsToRefresh">A dictionary with the configuration sections residing in the main 
            configuration file that must be refreshed.</param>
            <param name="externalSectionsToRefresh">A dictionary with the configuration sections residing in external 
            files that must be refreshed.</param>
            <param name="sectionsToNotify">A new collection with the names of the sections that suffered changes and 
            should be notified.</param>
            <param name="sectionsWithChangedConfigSource">A new dictionary with the names and file names of the sections 
            that have changed their location.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource.RefreshExternalSections(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Refreshes the configuration sections from an external configuration file.
            </summary>
            <param name="sectionsToRefresh">A collection with the names of the sections that suffered changes and should 
            be refreshed.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSourceElement">
            <summary>
            Represents the configuration settings that describe an <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSourceElement.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSourceElement"/> class with default values.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSourceElement.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSourceElement"/> class with a name and an type.
            </summary>
            <param name="name">The instance name.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSourceElement.CreateSource">
            <summary>
            Returns a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.SystemConfigurationSource"/>.
            </summary>
            <returns>A new configuration source.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.AbstractInstallerBuilder">
            <summary>
            Base class for the reflection-based installers. These reflection-based installers 
            search through assemblies looking for the appropriate kinds of installable resources and
            arrange for them to be registered with the appropriate installer
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.AbstractInstallerBuilder.Fill(System.Configuration.Install.Installer)">
            <summary>
            Fills the given installer with other, more specific kinds of installers that have been
            filled with the appropriate kinds of installable resources
            </summary>
            <param name="installer">Outer installer to be filled with nested installers for specific resources</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.AbstractInstallerBuilder.#ctor(System.Type[],System.Type)">
            <summary>
            Initializes object by giving it access to an array of all available types and a specification of
            the more specific resource type that will be installed.
            </summary>
            <param name="availableTypes">Array of available types through which installer should look</param>
            <param name="instrumentationAttributeType">Attribute specifying the more specific resource type to search for</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.AbstractInstallerBuilder.ConfirmAttributeExists(System.Type,System.Type)">
            <summary>
            Helper method to determine if the given type is annotated with the required attribute.
            </summary>
            <param name="instrumentedType">Type in question</param>
            <param name="attributeType">More specific attribute used to match resource being installed</param>
            <returns>True if the attributes on the given <paramref name="instrumentedType"></paramref> matches <paramref name="attributeType"></paramref></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.AbstractInstallerBuilder.IsInstrumented(System.Type,System.Type)">
            <summary>
            Helper method to determine if the attributes for a given type match the attributes used to 
            specify a specific kind of installable resource. The type should be attributed with <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.HasInstallableResourcesAttribute"></see>
            and the attribute passed to this method call.
            </summary>
            <param name="instrumentedType">Type in question</param>
            <param name="instrumentedAttributeType">More specific attribute used to match resource being installed</param>
            <returns><b>true</b> if the type specifies intallable resources.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.AbstractInstallerBuilder.CreateInstallers(System.Collections.Generic.ICollection{System.Type})">
            <summary>
            Creates one or more installers after iterating over the <paramref name="instrumentedTypes"></paramref>.
            The number of iterators returned depends on the specific needs of the particular installable type.
            </summary>
            <returns>Collection of installers created through iterating over included types</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.AbstractInstallerBuilder.InstrumentedTypes">
            <summary>
            Gets or sets a list of all instrumentented types found in a given assembly. Types are instrumented if they are
            attributed with <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.HasInstallableResourcesAttribute"></see>	 and another attribute specifying 
            another, more specific resource type.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounterFactory">
            <summary>
            Factory for <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounter"></see>s. Individual <see cref="T:System.Diagnostics.PerformanceCounter"></see>
            instances are cached to prevent the same instance from being created multiple times.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounterFactory.CreateCounter(System.String,System.String,System.String[])">
            <summary>
            Creates an <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounter"></see> initialized with individual <see cref="T:System.Diagnostics.PerformanceCounter"></see>
            instances. Instances are named according to <paramref name="instanceNames"></paramref> passed to this method.
            </summary>
            <param name="categoryName">Performance counter category name, as defined during installation.</param>
            <param name="counterName">Performance counter name, as defined during installation.</param>
            <param name="instanceNames">Param array of instance names for which individual counters should be created.</param>
            <returns>The new counter instance.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EnterpriseLibraryPerformanceCounterFactory.ClearCachedCounters">
            <summary>
            This method supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EventLogDefinitionAttribute">
            <summary>
            Defines an event source to be installed by the reflection based installer system. Each field
            in this attribute is a placeholder for the same field as defined in the <see cref="T:System.Diagnostics.EventLogInstaller"></see>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EventLogDefinitionAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes this object with the event log name and source to be installed.
            </summary>
            <param name="logName">Event log name to which the source should be added.</param>
            <param name="sourceName">Event log source to be added.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EventLogDefinitionAttribute.LogName">
            <summary>
            Gets the event log name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EventLogDefinitionAttribute.SourceName">
            <summary>
            Gets the event source name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EventLogDefinitionAttribute.CategoryCount">
            <summary>
            Gets and sets the category count.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EventLogDefinitionAttribute.CategoryResourceFile">
            <summary>
            Gets the category resource file name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EventLogDefinitionAttribute.MessageResourceFile">
            <summary>
            Gets and sets the message resource file name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EventLogDefinitionAttribute.ParameterResourceFile">
            <summary>
            Gets and sets the parameter resource file name.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EventLogEntryFormatter">
            <summary>
            Formats an event log entry to the defined format.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.IEventLogEntryFormatter">
            <summary>
            Formats an event log entry for logging to event log.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.IEventLogEntryFormatter.GetEntryText(System.String,System.String[])">
            <overloads>
            Creates a formatted message, suitable for logging to the event log.
            </overloads>
            <summary>
            Creates a formatted message, suitable for logging to the event log.
            </summary>
            <param name="message">Message to be formatted, with format tags embedded.</param>
            <param name="extraInformation">Extra strings to be matched up with the format tags provided in <paramref name="message"></paramref>.</param>
            <returns>Formatted message, suitable for logging to the event log.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.IEventLogEntryFormatter.GetEntryText(System.String,System.Exception,System.String[])">
            <summary>
            Creates a formatted message, suitable for logging to the event log.
            </summary>
            <param name="message">Message to be formatted, with format tags embedded.</param>
            <param name="exception">Exception containing message text to be added to event log message produced by this method</param>
            <param name="extraInformation">Extra strings to be matched up with the format tags provided in <paramref name="message"></paramref>.</param>
            <returns>Formatted message, suitable for logging to the event log.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EventLogEntryFormatter.#ctor(System.String)">
            <overloads>
            Initializes this object with the specified information.
            </overloads>
            <summary>
            Initializes this object with the name of the specific block using this class.
            </summary>
            <param name="blockName">Name of block using this functionality.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EventLogEntryFormatter.#ctor(System.String,System.String)">
            <summary>
            Initializes this object	with the given application and block names.
            </summary>
            <param name="applicationName">Name of the application.</param>
            <param name="blockName">Name of the block using this functionality.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EventLogEntryFormatter.GetEntryText(System.String,System.String[])">
            <overloads>
            Creates a formatted message, suitable for logging to the event log.
            </overloads>
            <summary>
            Creates a formatted message, suitable for logging to the event log.
            </summary>
            <param name="message">Message to be formatted, with format tags embedded.</param>
            <param name="extraInformation">Extra strings to be matched up with the format tags provided in <paramref name="message"></paramref>.</param>
            <returns>Formatted message, suitable for logging to the event log.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EventLogEntryFormatter.GetEntryText(System.String,System.Exception,System.String[])">
            <summary>
            Creates a formatted message, suitable for logging to the event log.
            </summary>
            <param name="message">Message to be formatted, with format tags embedded.</param>
            <param name="exception">Exception containing message text to be added to event log message produced by this method</param>
            <param name="extraInformation">Extra strings to be matched up with the format tags provided in <paramref name="message"></paramref>.</param>
            <returns>Formatted message, suitable for logging to the event log.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EventLogInstallerBuilder">
            <summary>
            Add event log source definitions for classes that have been attributed
            with HasInstallableResourceAttribute and EventLogDefinition attributes to EventLogInstallers.
            One installer is created for each unique event log source that is found.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EventLogInstallerBuilder.#ctor(System.Type[])">
            <summary>
            Initializes this object with a list of types that may potentially be attributed appropriately.
            </summary>
            <param name="potentialTypes">Array of types to inspect check for event log sources needing installation</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.EventLogInstallerBuilder.CreateInstallers(System.Collections.Generic.ICollection{System.Type})">
            <summary>
            Creates <see cref="T:System.Diagnostics.EventLogInstaller"></see> instances for each separate event log source needing installation.
            </summary>
            <param name="instrumentedTypes">Collection of <see cref="T:System.Type"></see>s that represent types defining
            event log sources to be installed.</param>
            <returns>Collection of installers containing event log sources to be installed.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterInstallerBuilder">
            <summary>
            Add event log source definitions for classes that have been attributed
            with HasInstallableResourceAttribute and EventLogDefinition attributes to EventLogInstallers.
            One installer is created for each unique performance counter category that is found.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterInstallerBuilder.#ctor(System.Type[])">
            <summary>
            Initializes this object with a list of <see cref="T:System.Type"></see>s that may potentially be attributed appropriately.
            </summary>
            <param name="availableTypes">Array of types to inspect check for performance counter definitions needing installation</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterInstallerBuilder.CreateInstallers(System.Collections.Generic.ICollection{System.Type})">
            <summary>
            Creates <see cref="T:System.Diagnostics.PerformanceCounterInstaller"></see> instances for each separate performance counter definition needing installation.
            </summary>
            <param name="instrumentedTypes">Collection of <see cref="T:System.Type"></see>s that represent types defining
            performance counter definitions to be installed.</param>
            <returns>Collection of installers containing performance counter definitions to be installed.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterInstallerBuilder.GetCategoryHelp(Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCountersDefinitionAttribute,System.Reflection.Assembly)">
            <summary>
            Gets the category help for a performance counter.
            </summary>
            <param name="attribute">The performance counter attribute.</param>
            <param name="originalAssembly">The assembly where the help is defined.</param>
            <returns>The help for the performance counter.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterInstallerBuilder.GetCounterHelp(System.String,System.Reflection.Assembly)">
            <summary>
            Gets the counter help for a performance counter.
            </summary>
            <param name="resourceName">The resource name.</param>
            <param name="originalAssembly">The assembly where the help is defined.</param>
            <returns>The help for the performance counter.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterInstanceName">
            <summary>
            Constructs an instance name for a <see cref="T:System.Diagnostics.PerformanceCounter"></see> following embedded
            formatting rules.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterInstanceName.#ctor(System.String,System.String)">
            <overloads>
            Initializes this object with information needed to construct a <see cref="T:System.Diagnostics.PerformanceCounter"></see>\
            instance name.
            </overloads>
            <summary>
            Initializes this object with information needed to construct a <see cref="T:System.Diagnostics.PerformanceCounter"></see>\
            instance name.
            </summary>
            <param name="prefix">Counter name prefix.</param>
            <param name="suffix">Counter name suffix.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterInstanceName.#ctor(System.String,System.String,System.Int32,System.Int32)">
            <overloads>
            Initializes this object with information needed to construct a <see cref="T:System.Diagnostics.PerformanceCounter"></see>\
            instance name.
            </overloads>
            <summary>
            Initializes this object with information needed to construct a <see cref="T:System.Diagnostics.PerformanceCounter"></see>\
            instance name.
            </summary>
            <param name="prefix">Counter name prefix.</param>
            <param name="suffix">Counter name suffix.</param>
            <param name="maxPrefixLength">Max prefix length.</param>
            <param name="maxSuffixLength">Max suffix length.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCounterInstanceName.ToString">
            <summary>
            Returns properly formatted counter name as a string.
            </summary>
            <returns>Formatted counter name.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCountersDefinitionAttribute">
            <summary>
            Defines information needed to install a <see cref="T:System.Diagnostics.PerformanceCounterCategory"></see>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCountersDefinitionAttribute.#ctor(System.String,System.String)">
             <overloads>
             Initializes this attribute with information needed to install this performance counter category.
             </overloads>
             <summary>
             Initializes this attribute with information needed to install this performance counter category.
             </summary>
             <param name="categoryName">Performance counter category name</param>
             <param name="categoryHelp">Counter category help resource name. 
             This is not the help text itself, 
             but is the resource name used to look up the internationalized help text at install-time.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCountersDefinitionAttribute.#ctor(System.String,System.String,System.Diagnostics.PerformanceCounterCategoryType)">
             <summary>
             Initializes this attribute with information needed to install this performance counter category.
             </summary>
             <param name="categoryName">Performance counter category name</param>
             <param name="categoryHelp">Counter category help resource name. 
             This is not the help text itself, 
             but is the resource name used to look up the internationalized help text at install-time.
            </param>
             <param name="categoryType">Performance counter category type.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCountersDefinitionAttribute.CategoryType">
            <summary>
            Gets the <see cref="T:System.Diagnostics.PerformanceCounter"></see> category type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCountersDefinitionAttribute.CategoryName">
            <summary>
            Gets the <see cref="T:System.Diagnostics.PerformanceCounter"></see> category name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.PerformanceCountersDefinitionAttribute.CategoryHelp">
            <summary>
            Gets the <see cref="T:System.Diagnostics.PerformanceCounter"></see> category help resource name.
            This is not the help text itself, 
            but is the resource name used to look up the internationalized help text at install-time.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.ReflectionInstaller`1">
            <summary>
            Generic installer wrapper around installer builder. Used to find and install 
            given type of installable resource.
            </summary>
            <typeparam name="TInstallerBuilder">Specific type of installer builder to instantiate</typeparam>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.ReflectionInstaller`1.Install(System.Collections.IDictionary)">
            <summary>
            Installs the instrumentation resources
            </summary>
            <param name="stateSaver">An <see cref="T:System.Collections.IDictionary"/> used to save information needed to perform a commit, rollback, or uninstall operation.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.ReflectionInstaller`1.Uninstall(System.Collections.IDictionary)">
            <summary>
            Uninstalls the instrumentation resources
            </summary>
            <param name="stateSaver">An <see cref="T:System.Collections.IDictionary"/> that contains the state of the computer after the installation was complete.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementFormatter">
            <summary>
            This class provides an engine to process a string that contains
            replacement tokens of the form "{token}" and replace them with
            calculated value later.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementFormatter.#ctor">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementFormatter"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementFormatter.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.ReplacementToken[])">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementFormatter"/>.
            </summary>
            <param name="tokens">List of tokens to replace.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementFormatter.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Practices.EnterpriseLibrary.Common.ReplacementToken})">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementFormatter"/>.
            </summary>
            <param name="tokens">List of tokens to replace.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementFormatter.Add(Microsoft.Practices.EnterpriseLibrary.Common.ReplacementToken[])">
            <summary>
            Add a new set of replacement tokens.
            </summary>
            <param name="tokens">Tokens to add to the list.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementFormatter.AddRange(System.Collections.Generic.IEnumerable{Microsoft.Practices.EnterpriseLibrary.Common.ReplacementToken})">
            <summary>
            Add new tokens to the set of replacements.
            </summary>
            <param name="tokens">Tokens to add to the list.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementFormatter.Format(System.String)">
            <summary>
            Format the given template, replacing any tokens present.
            </summary>
            <param name="template">The string to format, containing the replacement tokens.</param>
            <returns>The formatted string, with tokens replaced.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementToken">
            <summary>
            A single replacement token used by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementFormatter"/>. A
            token consists of two things:
            <list type="bullet">
            <item><description>The actual text of the token (including the {})</description></item>
            <item><description>A delegate to retrieve the value to replace the token.</description></item>
            </list>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementToken.#ctor(System.String,Microsoft.Practices.EnterpriseLibrary.Common.ReplacementTextDelegate)">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementToken"/>.
            </summary>
            <param name="token">The string marking where the token should be replaced.</param>
            <param name="getReplacementText">Delegate to return the value that replaces the token.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementToken.ReplaceToken(System.Text.StringBuilder)">
            <summary>
            Replace this token in the given stringbuilder.
            </summary>
            <param name="sb"><see cref="T:System.Text.StringBuilder"/> holding the template to perform the token replacement on.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementToken.Token">
            <summary>
            The token string.
            </summary>
            <value>The token string.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementToken.ReplacementText">
            <summary>
            The text to replace this token with.
            </summary>
            <value>Replacement text.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.ReplacementTextDelegate">
            <summary>
            Delegate type giving a function that returns the replacement text for a token.
            </summary>
            <returns>The replacement text.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.ResourceStringLoader">
            <summary>
            Helper class to load resources strings.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.ResourceStringLoader.LoadString(System.String,System.String)">
            <summary>
            Load a resource string.
            </summary>
            <param name="baseName">The base name of the resource.</param>
            <param name="resourceName">The resource name.</param>
            <returns>The string from the resource.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.ResourceStringLoader.LoadString(System.String,System.String,System.Reflection.Assembly)">
            <summary>
            Load a resource string.
            </summary>
            <param name="baseName">The base name of the resource.</param>
            <param name="resourceName">The resource name.</param>
            <param name="asm">The assembly to load the resource from.</param>
            <returns>The string from the resource.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Utility.ConstantStringResolver">
            <summary>
            Resolves strings by returning a constant value.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Utility.IStringResolver">
            <summary>
            Resolves string objects. 
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.IStringResolver.GetString">
            <summary>
            Returns a string represented by the receiver.
            </summary>
            <returns>The string object.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.ConstantStringResolver.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Utility.ConstantStringResolver"/> with a constant value.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Utility.DelegateStringResolver">
            <summary>
            Resolves strings by invoking a delegate and returning the resulting value.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.DelegateStringResolver.#ctor(System.Func{System.String})">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Utility.ConstantStringResolver"/> with a delegate.
            </summary>
            <param name="resolverDelegate">The delegate to invoke when resolving a string.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Utility.EnumerableExtensions">
            <summary>
            Some utility extensions on <see cref="T:System.Collections.Generic.IEnumerable`1"/> to suppliment
            those available from Linq.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.EnumerableExtensions.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0})">
            <summary>
            Execute <paramref name="action"/> for each element of <paramref name="sequence"/>.
            </summary>
            <typeparam name="T">Type of items in <paramref name="sequence"/>.</typeparam>
            <param name="sequence">Sequence of items to act on.</param>
            <param name="action">Action to invoke for each item.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.EnumerableExtensions.Zip``3(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``1},System.Func{``0,``1,``2})">
            <summary>
            Given a sequence, combine it with another sequence, passing the corresponding
            elements of each sequence to the <paramref name="zipper"/> action to create
            a new single value from the two sequence elements. "Zip" here refers to a zipper,
            not the compression algorithm. The resulting sequence will have the same number
            of elements as the shorter of sequence1 and sequence2.
            </summary>
            <typeparam name="T1">Type of the elments in the first sequence.</typeparam>
            <typeparam name="T2">Type of the elements in the second sequence.</typeparam>
            <typeparam name="TResult">Type of the resulting sequence elements.</typeparam>
            <param name="sequence1">The first sequence to combine.</param>
            <param name="sequence2">The second sequence to combine.</param>
            <param name="zipper">Func used to calculate the resulting values.</param>
            <returns>The result sequence.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.EnumerableExtensions.Zip``2(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``1})">
            <summary>
            Take two sequences and return a new sequence of <see cref="T:System.Collections.Generic.KeyValuePair`2"/> objects.
            </summary>
            <typeparam name="T1">Type of objects in sequence1.</typeparam>
            <typeparam name="T2">Type of objects in sequence2.</typeparam>
            <param name="sequence1">First sequence.</param>
            <param name="sequence2">Second sequence.</param>
            <returns>The sequence of <see cref="T:System.Collections.Generic.KeyValuePair`2"/> objects.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.EnumerableExtensions.ToDictionary``2(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``1})">
            <summary>
            Take two sequences and return a <see cref="T:System.Collections.Generic.IDictionary`2"/> with the first sequence
            holding the keys and the corresponding elements of the second sequence containing the values.
            </summary>
            <typeparam name="TKey">Type of keys in the dictionary.</typeparam>
            <typeparam name="TValue">Type of values in the dictionary.</typeparam>
            <param name="keys">Sequence of dictionary keys.</param>
            <param name="values">Sequence of dictionary values.</param>
            <returns>The constructed dictionary.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Utility.Guard">
            <summary>
            A static helper class that includes various parameter checking routines.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.Guard.ArgumentNotNull(System.Object,System.String)">
            <summary>
            Throws <see cref="T:System.ArgumentNullException"/> if the given argument is null.
            </summary>
            <exception cref="T:System.ArgumentNullException">The value is null.</exception>
            <param name="argumentValue">The argument value to test.</param>
            <param name="argumentName">The name of the argument to test.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.Guard.ArgumentNotNullOrEmpty(System.String,System.String)">
            <summary>
            Throws an exception if the tested string argument is null or an empty string.
            </summary>
            <exception cref="T:System.ArgumentNullException">The string value is null.</exception>
            <exception cref="T:System.ArgumentException">The string is empty.</exception>
            <param name="argumentValue">The argument value to test.</param>
            <param name="argumentName">The name of the argument to test.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Utility.ResourceStringResolver">
            <summary>
            Resolves strings by retrieving them from assembly resources, falling back to a specified
            value.
            </summary>
            <remarks>
            If both the resource type and the resource name are available, a resource lookup will be 
            performed; otherwise, the default value will be returned.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.ResourceStringResolver.#ctor(System.Type,System.String,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Utility.ResourceStringResolver"/>
            for a resource type, a resource name and a fallback value.
            </summary>
            <param name="resourceType">The type that identifies the resources file.</param>
            <param name="resourceName">The name of the resource.</param>
            <param name="fallbackValue">The fallback value, to use when any of the resource
            identifiers is not available.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.ResourceStringResolver.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Utility.ResourceStringResolver"/>
            for a resource type name, a resource name and a fallback value.
            </summary>
            <param name="resourceTypeName">The name of the type that identifies the resources file.</param>
            <param name="resourceName">The name of the resource.</param>
            <param name="fallbackValue">The fallback value, to use when any of the resource
            identifiers is not available.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Utility.StaticReflection">
            <summary>
            Provides a set of helper methods that retrieve
            <see cref="T:System.Reflection.MethodInfo"/> objects for the methods described in lambda expressions.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.StaticReflection.GetMethodInfo(System.Linq.Expressions.Expression{System.Action})">
            <summary>
            Retrieves a <see cref="T:System.Reflection.MethodInfo"/> object from an expression in the form
            () =&gt; SomeClass.SomeMethod().
            </summary>
            <param name="expression">The expression that describes the method to call.</param>
            <returns>The <see cref="T:System.Reflection.MethodInfo"/> object for the specified method.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.StaticReflection.GetMethodInfo``1(System.Linq.Expressions.Expression{System.Action{``0}})">
            <summary>
            Retrieves a <see cref="T:System.Reflection.MethodInfo"/> object from an expression in the form
            x =&gt; x.SomeMethod().
            </summary>
            <typeparam name="T">The type where the method is defined.</typeparam>
            <param name="expression">The expression that describes the method to call.</param>
            <returns>The <see cref="T:System.Reflection.MethodInfo"/> object for the specified method.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.StaticReflection.GetPropertyGetMethodInfo``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Retrieves a <see cref="T:System.Reflection.MethodInfo"/> object for the get method from an expression in the form
            x =&gt; x.SomeProperty.
            </summary>
            <typeparam name="T">The type where the method is defined.</typeparam>
            <typeparam name="TProperty">The type of the property.</typeparam>
            <param name="expression">The expression that describes the property for which the get method is to be extracted.</param>
            <returns>The <see cref="T:System.Reflection.MethodInfo"/> object for the get method.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.StaticReflection.GetPropertySetMethodInfo``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Retrieves a <see cref="T:System.Reflection.MethodInfo"/> object for the set method from an expression in the form
            x =&gt; x.SomeProperty.
            </summary>
            <typeparam name="T">The type where the method is defined.</typeparam>
            <typeparam name="TProperty">The type of the property.</typeparam>
            <param name="expression">The expression that describes the property for which the set method is to be extracted.</param>
            <returns>The <see cref="T:System.Reflection.MethodInfo"/> object for the set method.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.StaticReflection.GetMemberInfo``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Retrieves a <see cref="T:System.Reflection.PropertyInfo"/> object for the set method from an expression in the form
            x =&gt; x.SomeProperty.
            </summary>
            <typeparam name="T">The type where the method is defined.</typeparam>
            <typeparam name="TProperty">The type of the property.</typeparam>
            <param name="expression">The expression that describes the property for which the property informationis to be extracted.</param>
            <returns>The <see cref="T:System.Reflection.PropertyInfo"/> object for the property.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.StaticReflection.GetConstructorInfo``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Retrieves a <see cref="T:System.Reflection.ConstructorInfo"/> object from an expression in the form () =&gt; new SomeType().
            </summary>
            <typeparam name="T">The type where the constructor is defined.</typeparam>
            <param name="expression">The expression that calls the desired constructor.</param>
            <returns>The <see cref="T:System.Reflection.ConstructorInfo"/> object for the constructor.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Utility.TypeExtensions">
            <summary>
            Extensios to <see cref="T:System.Type"/>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.TypeExtensions.FindGenericParent(System.Type,System.Type)">
            <summary>
             Locates the generic parent of the type
            </summary>
            <param name="rootType">Type to begin search from.</param>
            <param name="parentType">Open generic type to seek</param>
            <returns>The found parent that is a closed generic of the <paramref name="parentType"/> or null</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Utility.WrappedAsyncOperation">
            <summary>
            A helper class that provides the code needed to wrap an existing
            asynchronous operation and return a different implementation of
            <see cref="T:System.IAsyncResult"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.WrappedAsyncOperation.BeginAsyncOperation``1(System.AsyncCallback,System.Func{System.AsyncCallback,System.IAsyncResult},System.Func{System.IAsyncResult,``0})">
            <summary>
            Start an asyncronous operation that wraps a lower-level
            async operation.
            </summary>
            <typeparam name="TWrappingAsyncResult">Type that implements IAsyncResult
            that will be returned from this method.</typeparam>
            <param name="callback">The user's callback method to be called when
            the async operation completes.</param>
            <param name="beginOperation">A delegate that invokes the underlying
            async operation that we're wrapping.</param>
            <param name="wrappingResultCreator">A delegate that takes the inner
            async result and returns the wrapping instance of <typeparamref name="TWrappingAsyncResult"/>.
            </param>
            <returns>The <see cref="T:System.IAsyncResult"/>.</returns>
        </member>
    </members>
</doc>
