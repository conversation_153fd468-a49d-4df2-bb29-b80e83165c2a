﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.ApplicationServer.Caching.Core</name>
  </assembly>
  <members>
    <member name="T:Microsoft.ApplicationServer.Caching.AddUserState"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.AddUserState.#ctor"></member>
    <member name="P:Microsoft.ApplicationServer.Caching.AddUserState.NewLogOn">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.AddUserState.NewUser">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.BaseOperationNotification">
      <summary>The base class for an event used for notifications. Applies only to the server versions of AppFabric.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.BaseOperationNotification.#ctor(System.String,Microsoft.ApplicationServer.Caching.DataCacheOperations,Microsoft.ApplicationServer.Caching.DataCacheItemVersion)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.BaseOperationNotification" /> class. Applies only to the server versions of AppFabric.</summary>
      <param name="cacheName">Name of the cache in which the operation occurred.</param>
      <param name="opType">Type of the operation which caused the notification.</param>
      <param name="version">Version of the operation which caused the notification.</param>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.BaseOperationNotification.CacheName">
      <summary>Name of the cache in which the operation occurred. Applies only to the server versions of AppFabric.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.BaseOperationNotification.OperationType">
      <summary>The type of the operation which caused the notification. Applies only to the server versions of AppFabric.</summary>
      <returns>Returns <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheOperations" />.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.BaseOperationNotification.ToString">
      <summary>Returns the string representation of the BaseOperationNotification event. Applies only to the server versions of AppFabric.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.BaseOperationNotification.Version">
      <summary>The version of the operation which caused the notification. Applies only to the server versions of AppFabric.</summary>
      <returns>Returns <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" />.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.ConfigStoreEntry">
      <summary>Represents a key-value pair that can be used by a custom provider for the cache cluster configuration store. Applies only to the server versions of AppFabric.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ConfigStoreEntry.#ctor(System.String,System.Byte[],System.Int64)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.ConfigStoreEntry" /> class. Applies only to the server versions of AppFabric.</summary>
      <param name="key">The key of the entry.</param>
      <param name="value">The value of the entry.</param>
      <param name="version">The version of the entry.</param>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.ConfigStoreEntry.Key">
      <summary>The key of the entry. Applies only to the server versions of AppFabric.</summary>
      <returns>A <see cref="T:System.String" /> object.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.ConfigStoreEntry.Version">
      <summary>The version of the entry. Applies only to the server versions of AppFabric.</summary>
      <returns>A <see cref="T:System.Int64" /> value that is greater than 0.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.ConfigStoreException">
      <summary>An exception that should be thrown from an implementation of the <see cref="T:Microsoft.ApplicationServer.Caching.ICustomProvider" /> interface. Applies only to the server versions of AppFabric.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ConfigStoreException.#ctor">
      <summary>Creates a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.ConfigStoreException" /> class. Applies only to the server versions of AppFabric.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ConfigStoreException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.ConfigStoreException" /> class. Applies only to the server versions of AppFabric.</summary>
      <param name="info">The object that holds the serialized object data.</param>
      <param name="context">The contextual information about the source or destination.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ConfigStoreException.#ctor(System.String)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.ConfigStoreException" /> class. Applies only to the server versions of AppFabric.</summary>
      <param name="exceptionMessage">The message string that describes the error.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ConfigStoreException.#ctor(System.String,System.Exception)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.ConfigStoreException" /> class. Applies only to the server versions of AppFabric.</summary>
      <param name="exceptionMessage">The message string that describes the error.</param>
      <param name="inner">The inner exception that caused the current exception.</param>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheClientLogManager">
      <summary>Provides support for changing the logging level of Windows Azure Caching.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheClientLogManager.ChangeLogLevel(System.Diagnostics.TraceLevel)">
      <summary>Changes the logging level of the Event Tracing for Windows (ETW) log for Windows Azure Caching.</summary>
      <returns>Returns <see cref="T:System.Diagnostics.TraceLevel" />.</returns>
      <param name="level">The trace level for the log.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheClientLogManager.SetSink(Microsoft.ApplicationServer.Caching.DataCacheTraceSink,System.Diagnostics.TraceLevel)">
      <summary>Enables tracing for a cache client.</summary>
      <param name="traceSink">The type of tracing. This can be set to <see cref="F:Microsoft.ApplicationServer.Caching.DataCacheTraceSink.DiagnosticSink" /> or <see cref="F:Microsoft.ApplicationServer.Caching.DataCacheTraceSink.EtwSink" />.</param>
      <param name="traceLevel">The <see cref="T:System.Diagnostics.TraceLevel" /> trace level.</param>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheErrorCode">
      <summary>A static class used to store global error codes.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.AcsTokenRequestFailedAuthError">
      <summary>The ACS token provided failed the authentication process.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.AdminAlreadyConfigured">
      <summary>Cache Administration is already configured on the machine.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.AdminNotConfigured">
      <summary>Cache Administration is not configured on the machine.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.AuthorizationTokenNotValid">
      <summary>The authentication token provided is not in a valid format.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminCacheAlreadyPresent">
      <summary>The named cache already exists in the cache cluster.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminCacheConfigWriteError"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminCacheCreationInconsistencyFailure">
      <summary>The named cache could not be created due to a detected inconsistency in the configuration store.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminCacheNotPresent">
      <summary>The specified named cache does not exist in the cache cluster.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminCancelShutdownError"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminClusterDown">
      <summary>The cache cluster is not running.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminClusterNotReady">
      <summary>The cache cluster is not ready to perform the requested operation.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminClusterRefreshFailed">
      <summary>The cache cluster experienced an error while attempting to refresh the list of cache hosts on all of the cache hosts.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminClusterSettingsNotProvided"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminClusterSettingsReadError">
      <summary>An error occurred attempting to read the configuration store provider name and connection string from the registry.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminClusterTimeout">
      <summary>A Cache Administration timeout occurred.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminCommandNotAllowed"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminConfigAddHostError">
      <summary>An error occurred attempt to add a cache host to the cache cluster configuration store.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminConfigDeleteHostError">
      <summary>An error occurred attempting to delete a cache host from the cache cluster configuration store.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminDefaultCacheCreateFailure">
      <summary>An error occurred while creating the default cache.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminDeleteInProgress">
      <summary>The specified cache is in the process of being removed.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminGrantClientAccountErrorFormat">
      <summary>An error occurred attempting to add an account to the allowed client accounts list.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminHostConfigWriteError"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminHostNameResolveFailure">
      <summary>The name 'localhost' is unable to be resolved to the current host name.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminHostNotPresent">
      <summary>The specified cache host is not part of the cache cluster.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminHostNotReachable"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminHostNotRunning">
      <summary>The specified cache host is not currently running.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminHostOperationError"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminHostRefreshFailed">
      <summary>The cache cluster experienced an error while attempting to refresh the list of cache hosts on each of the specified cache host.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminHostRunning">
      <summary>The specified cache host is already in the running state.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminHostsNotReachable"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminHostsNotRunning">
      <summary>No cache hosts are running in the cache cluster.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminHostsRunning">
      <summary>One or more cache hosts are running in the cache cluster.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminIncompatibleExpirationParameters"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminIncompleteParameters"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminInstallPathReadError">
      <summary>An error occurred reading the installation path from the registry.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminInvalidCacheName"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminInvalidClusterSettings">
      <summary>The specified provider string or connection string is invalid. </summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminInvalidOperation">
      <summary>The specified operation is invalid.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminInvalidParameters"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminInvalidParametersForHost"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminInvalidSecuritySettings">
      <summary>The specified security settings are invalid.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminMaxCachesCreated">
      <summary>The specified named cache cannot be created, because the cache cluster has reached the maximum number of named caches.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminMinSecondariesOutOfBounds"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminNoHosts">
      <summary>There are no cache hosts in the cache cluster.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminNoQuorumIfHostStopped">
      <summary>The specified host cannot be stopped, because the number of hosts would drop below the required quorum.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminNoSeedNodes">
      <summary>There are no seed nodes present in the cache cluster.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminNoValuesProvided"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminNullArgs">
      <summary>A required argument is null.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminNullProviderSettingsKey"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminOperationError"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminOperationNotSupported"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminPortsDuplicated">
      <summary>One or more port numbers have duplicate values.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminProviderTypeUnspecified"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminQuorumNotUp">
      <summary>The required quorum of hosts is not running.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminRegionNotPresent">
      <summary>The specified region does not exist in the cache cluster.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminRemoteRegistryAccessFailed">
      <summary>An error occurred while attempting to access the registry on a remote cache host.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminRequestTimeoutResultUnknown">
      <summary>The request timed out, but the result of the request is unknown.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminRevokeClientAccountErrorFormat">
      <summary>An error occurred attempting to remove an account from the list of allowed client accounts.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminShutdownInProgress"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminShutdownNotInProgress"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminShutdownNotPossible"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminStatsIncorrect">
      <summary>The returned status information might be incorrect, because one or more cache hosts could not be reached.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminStoreAccessFailure">
      <summary>An error occurred attempting to access the cache cluster configuration store.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminTimeout">
      <summary>A timeout occurred.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminUnknownError">
      <summary>An unknown error occurred.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminWindowsAccountAlreadyPresent">
      <summary>The specified Windows account already exists in the list of allowed client accounts.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminWindowsAccountInvalid">
      <summary>The specified Windows account is not valid.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheAdminWindowsAccountNotPresent">
      <summary>The specified Windows account is not present in the list of allowed client accounts.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheDisabled">
      <summary>The cache has been disabled. One possible cause could be a failure to reconcile charges associated with the specified cache.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheItemVersionMismatch">
      <summary>The object in the cache does not match with the specified version.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CacheRedirected"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.CcrUnhandledException"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ChannelAuthenticationFailed">
      <summary>Unable to establish an SSL channel.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ClientServerVersionMismatch">
      <summary>The cache client assemblies are a different version than the cache host assemblies.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ClusterAlreadyInitialized">
      <summary>The cache cluster has already been initialized.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ClusterConfigConnectionError">
      <summary>An error occurred while attempting to connect to the cache cluster configuration store.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ClusterConfigReadError">
      <summary>An error occurred while reading the cache cluster configuration store.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ClusterNotInitialized">
      <summary>The cache cluster is not initialized.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ConfigurationStateSaveError">
      <summary>An error occurred while attempting to save the configuration state.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ConnectionSettingsRegistrySaveError">
      <summary>An error occurred while attempting to save the connection settings to the registry.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ConnectionTerminated">
      <summary>The connection was terminated. This could be caused by server or network problems.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ConvertSimpleClient"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.DomainBlockedAccount"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.DuplicateServersSpecified"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.GetComputerDomainError">
      <summary>An error occurred while attempting to get the computer domain.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.HostAdditionFailureError">
      <summary>An error occurred while attempting to add a cache host to the cache cluster.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.HostAlreadyPresent"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.HostDeletionFailureError">
      <summary>An error occurred while attempting to remove a host from the cache cluster.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.HostEntryNotFound">
      <summary>The specified cache host entry was not found in the cache cluster configuration.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.IncompatibleExpirationParameters"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.IncompleteConnectionParameters">
      <summary>The specified connection parameters are incomplete.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.InstallPathMissingError">
      <summary>An error occurred while attempting to read the install path from the registry.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.InvalidArgument">
      <summary>The argument is not valid.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.InvalidAutoDiscoverIdentifier">
      <summary>AutoDiscover property is not valid.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.InvalidCacheLockHandle">
      <summary>The specified lock handle is not valid.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.InvalidEnumerator">
      <summary>An invalid enumerator is specified.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.KeyAlreadyExists">
      <summary>The key is already present in the cache or region.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.KeyDoesNotExist">
      <summary>An object cached with the specified key does not exist in the cache or region.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.MaxNamedCacheCountExceeded">
      <summary>The named cache count exceeded the maximum value.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.MessageLargerThanConfiguredSize">
      <summary>The request failed because either the response or the request message was larger than configured message size in the transport properties.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.NamedCacheDoesNotExist">
      <summary>The named cache does not exist.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.NetworkShareAsLocalPathError">
      <summary>A network path was expected, but a local path was provided.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.NetworkShareFilePermissionsError">
      <summary>The specified network file share does not have appropriate permissions.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.NetworkShareFolderConnectionError">
      <summary>An error occurred while attempting to connect to the shared network folder.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.NewNetworkShareSetupError">
      <summary>An error occurred attempting to write the configuration store files at the specified shared network folder.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.NonDomainBlockedAccount">
      <summary>The domain account is blocked for the workgroup configuration.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.NonDomainNWService">
      <summary>The Network Service account is not permitted for a workgroup-based configuration of the Caching Service.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.NotificationInvalidationNotSupported">
      <summary>Cache notifications are not supported because the named cache has been created without notifications enabled.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ObjectLocked">
      <summary>The cached object has already been locked.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ObjectNotLocked">
      <summary>The object is not locked.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.OffloadingWithXml"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.OperationNotSupported"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.PermissionsError">
      <summary>An error occurred while setting permissions on the cache cluster configuration.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.PortAlreadyInUseError">
      <summary>The specified port is already in-use.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.PortDuplicationError">
      <summary>One or more ports have duplicate values.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ReadThroughProviderDidNotReturnResult"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ReadThroughProviderFailure"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ReadThroughProviderNotFound"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ReadThroughRegionDoesNotExist"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.RegionAlreadyExists">
      <summary>The region already exists.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.RegionDoesNotExist">
      <summary>The region does not exist.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.RegistryAccessFailed">
      <summary>An error occurred while attempting to access the registry.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.RegistryKeyOpenFailure">
      <summary>An error occurred while attempting to open a registry key for reading.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.RetryLater">
      <summary>Temporary failure, retry the operation later.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.SerializationException">
      <summary>An exception was encountered during object serialization.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ServerNull">
      <summary>The server passed to the <see cref="T:Microsoft.Data.Caching.DataCacheFactory" /> constructor is null.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ServiceAccessError">
      <summary>An error occurred while accessing the Caching Service.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ServiceAlreadyConfigured">
      <summary>The Caching Service is already configured on the specified cache host.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ServiceNotConfigured">
      <summary>The Caching Service is not configured on the specified cache host.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.ServiceNotStopped">
      <summary>The operation cannot be completed, because the Caching Service is still running.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.SqlAuthenticationNotSupported">
      <summary>SQL Server authentication is not permitted with the SQL Server provider for the cache cluster configuration store. Windows authentication must be used.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.StringTooLarge"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.TestConnectionFailed">
      <summary>The test connection failed.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.Timeout">
      <summary>Communications with the cache cluster has experienced a delay past the timeout value.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.UndefinedError">
      <summary>An unknown error has occurred.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.UnsupportedOperationAttemptedOnPort">
      <summary>The current operation is not supported on this port.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorCode.UsageResourceNotFound"></member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus">
      <summary>Used to retrieve substatus codes for errors returned to the client.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.CacheServerUnavailable">
      <summary>The primary cache server is unavailable.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.CacheUnderReconfiguration"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.CertificateRevocationServerOffline">
      <summary>Unable to validate the SSL certificate.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.InternalError">
      <summary>An internal error has occurred.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.KeyLatched">
      <summary>The client failed to update a key when adding or replacing an object in the cache.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.KeyTooLarge">
      <summary>The specified key name is too large. The key size is calculated after serialization and UTF-8 encoding of the string. It must be less than 65 KB.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.None">
      <summary>There is no specific substatus code.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.NotPrimary">
      <summary>The request did not find the primary server.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.NoWriteQuorum">
      <summary>Unable to satisfy the specified write quorum of cache hosts.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.QuotaExceeded">
      <summary>A resource quota for the Windows Azure Caching namespace has been exceeded.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.ReadThroughKeyContention">
      <summary>Internal.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.RegionTooLarge">
      <summary>The specified region name is too large. The name size is calculated after serialization and UTF-8 encoding of the string. It must be less than 65 KB.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.ReplicationFailed">
      <summary>Replication failed.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.ReplicationQueueFull">
      <summary>The replication queue is full.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.ServiceMemoryShortage">
      <summary>The memory available for the caching service is low.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.TagTooLarge">
      <summary>The specified tag name is too large. The name size is calculated after serialization and UTF-8 encoding of the string. It must be less than 65 KB.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheErrorSubStatus.Throttled">
      <summary>The requested operation failed, because the required cache host is in a throttled state.</summary>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheException">
      <summary>Used for cache-related exceptions.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheException" /> class.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheException" /> class; allows you to provide serialization information with the exception.</summary>
      <param name="info">Stores serialization information about an object.</param>
      <param name="context">Describes the source, destination, and context of a serialized stream.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheException" /> class; allows you to provide a message with the exception.</summary>
      <param name="message">The message describing the exception.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheException" /> class; allows you to provide a message and another exception with the exception.</summary>
      <param name="message">The message describing the exception.</param>
      <param name="innerException">Another exception related to this exception.</param>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheException.ErrorCode">
      <summary>The integer used to identify the type of exception encountered.</summary>
      <returns>An <see cref="T:System.Int32" /> specifying the type of exception encountered.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>The interface that is used for serialization.</summary>
      <param name="info">The SerializationInfo class used to store all the data that you need to serialize the exception.</param>
      <param name="context">The StreamingContext class used to describe the source and destination of the exception.</param>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheException.HelpLink">
      <summary>A link to help for the exception.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheException.Message">
      <summary>The description of the exception encountered.</summary>
      <returns>A <see cref="T:System.String" /> that describes the type of exception encountered.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheException.SubStatus">
      <summary>The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheErrorSubstatus" /> of the exception encountered.</summary>
      <returns>An <see cref="T:System.Int32" /> indicating the exception substatus.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheException.TrackingId">
      <summary>The tracking identifier related to the exception. Only valid if tracing is enabled.</summary>
      <returns>Returns <see cref="T:System.Guid" />.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheItem">
      <summary>Used to retrieve all information associated with the cached object in the cluster.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheItem.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItem" /> class.</summary>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheItem.CacheName">
      <summary>The name of the cache where the object is stored.</summary>
      <returns>A string value that represents the name of the cache where the object is stored.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheItem.ExtensionTimeout">
      <summary>The amount of time to extend the object lifetime on each access.</summary>
      <returns>Returns <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheItem.Key">
      <summary>The identifier that is used to distinguish the cached object in the cache or region.</summary>
      <returns>A string value that represents the identifier that is used to distinguish the cached object in the cache or region.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheItem.RegionName">
      <summary>If applicable, the name of the region where the object is stored.</summary>
      <returns>A string value that represents, if applicable, the name of the region where the object is stored.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheItem.Size">
      <summary>The approximate size of the cached item. This field is intended for internal use only.</summary>
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheItem.Tags">
      <summary>A generic list of tags associated with the cached object.</summary>
      <returns>An generic list of <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheTag" /> objects associated with the cached object.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheItem.Timeout">
      <summary>Specifies the length of time for which the object will remain in the cache.</summary>
      <returns>Returns <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheItem.ToString">
      <summary>Creates a string representation of the DataCacheItem object.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheItem.Value">
      <summary>The object stored in cache.</summary>
      <returns>An object representing the deserialized form of the cached object.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheItem.Version">
      <summary>The version of the cached object.</summary>
      <returns>The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" />.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheItemFactory">
      <summary>Used to create <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItem" /> objects as a part of implementing a read-through or write-behind provider.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheItemFactory.GetCacheItem(Microsoft.ApplicationServer.Caching.DataCacheItemKey,System.String,System.Object,System.Collections.Generic.IEnumerable{Microsoft.ApplicationServer.Caching.DataCacheTag})">
      <summary>Creates a DataCacheItem object using the specified parameters.</summary>
      <returns>Returns <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItem" />.</returns>
      <param name="key">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemKey" /> that is passed to the read-through or write-behind provider methods.</param>
      <param name="cacheName">The name of the cache that is associated with this item.</param>
      <param name="value">The value of the cache item.</param>
      <param name="tags">A collection of tags associated with this item.</param>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheItemKey">
      <summary>A cache item key used in the implementation of a read-through or write-behind provider.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheItemKey.#ctor(System.String,System.String)">
      <summary>Create a new <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemKey" /> object.</summary>
      <param name="region">The region name for the cache item.</param>
      <param name="key">The key name for the cache item.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheItemKey.Equals(System.Object)">
      <summary>Returns true if the DataCacheItemKey objects are equal.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
      <param name="obj">The DataCacheItemKey object to compare.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheItemKey.GetHashCode">
      <summary>Returns the hash code for the DataCacheItemKey.</summary>
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheItemKey.Key">
      <summary>The name of the data cache item key.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheItemKey.Region">
      <summary>The region of the cache item.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheItemKey.ToString">
      <summary>Returns a string representation of the DataCacheItemKey.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion">
      <summary>Used to represent the version of a cached object.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheItemVersion.CompareTo(Microsoft.ApplicationServer.Caching.DataCacheItemVersion)">
      <summary>Used to compare two versions of the same item, specified with the same key.</summary>
      <returns>An <see cref="T:System.Int32" />value: 0 if the versions are equal, greater than zero if the instance is greater than the version parameter value, or less than zero if the instance is less than the version parameter value.</returns>
      <param name="other">The <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> to be compared.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheItemVersion.Equals(System.Object)">
      <summary>Used to determine whether another object is the same.</summary>
      <returns>A <see cref="T:System.Boolean" />value: true, if the objects are the same; false, if they are different.</returns>
      <param name="obj">Another object for comparison.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheItemVersion.GetHashCode">
      <summary>Returns the hash code of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> object.</summary>
      <returns>An <see cref="T:System.Int32" />value that represents the hash code of the DataCacheItemVersion object.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheItemVersion.IsEmpty(Microsoft.ApplicationServer.Caching.DataCacheItemVersion)">
      <summary>Indicates whether the DataCacheItemVersion object points to a null version. This is intended for internal use only.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
      <param name="version">The DataCacheItemVersion object to examine.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheItemVersion.op_Equality(Microsoft.ApplicationServer.Caching.DataCacheItemVersion,Microsoft.ApplicationServer.Caching.DataCacheItemVersion)">
      <summary>Indicates whether the two <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> objects are equal.</summary>
      <returns>True if the two specified objects are equal; otherwise, false.</returns>
      <param name="left">The first object to compare.</param>
      <param name="right">The second object to compare.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheItemVersion.op_GreaterThan(Microsoft.ApplicationServer.Caching.DataCacheItemVersion,Microsoft.ApplicationServer.Caching.DataCacheItemVersion)">
      <summary>Indicates whether the first object is greater than the second object.</summary>
      <returns>True if the first object is greater than the second object; otherwise, false.</returns>
      <param name="left">The first object to compare.</param>
      <param name="right">The second object to compare.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheItemVersion.op_Inequality(Microsoft.ApplicationServer.Caching.DataCacheItemVersion,Microsoft.ApplicationServer.Caching.DataCacheItemVersion)">
      <summary>Indicates whether the two <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItemVersion" /> objects are unequal.</summary>
      <returns>True if the two objects are not equal; otherwise, false.</returns>
      <param name="left">The first object to compare.</param>
      <param name="right">The second object to compare.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheItemVersion.op_LessThan(Microsoft.ApplicationServer.Caching.DataCacheItemVersion,Microsoft.ApplicationServer.Caching.DataCacheItemVersion)">
      <summary>Indicates whether the value of the first object is less than the second object.</summary>
      <returns>True if the value of the first object is less than the value of the second object; otherwise, false.</returns>
      <param name="left">The first object to compare.</param>
      <param name="right">The second object to compare.</param>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheLocalCacheInvalidationPolicy">
      <summary>Specifies the way locally cached objects should be invalidated.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheLocalCacheInvalidationPolicy.NotificationBased">
      <summary>Specifies that objects will stay in local cache until they are automatically invalidated by a cache notification.  Not supported in Windows Azure Shared Caching.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheLocalCacheInvalidationPolicy.TimeoutBased">
      <summary>Specifies that objects will stay in local cache until their lifetime reaches the localCacheTimeout duration specified in the DataCacheFactory constructor. </summary>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheLockHandle">
      <summary>Represents the structure used as a key to lock and unlock cached objects in a pessimistic concurrency scenario.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheLockHandle.ToString">
      <summary>Returns the specified object to string.</summary>
      <returns>A string that represents the object.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheObjectSerializerType">
      <summary>An enumeration of the serialization types.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheObjectSerializerType.BinaryFormatter">
      <summary>Items are serialized with a binary formatter.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheObjectSerializerType.CustomSerializer">
      <summary>Items are serialized with a custom formatter.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheObjectSerializerType.NetDataContractSerializer">
      <summary>Items are serialized with a NetDataContractSerializer formatter. This is the default.</summary>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheOperationDescriptor">
      <summary>Represents a notification event for operations performed against the cache, such as Add or Remove.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheOperationDescriptor.#ctor(System.String,System.String,System.String,Microsoft.ApplicationServer.Caching.DataCacheOperations,Microsoft.ApplicationServer.Caching.DataCacheItemVersion)">
      <summary>Creates a new instance of the DataCacheOperationDescriptor class using the specified parameters.</summary>
      <param name="cacheName">Name of the cache in which the operation occurred.</param>
      <param name="regionName">The name of the region where the object is stored.</param>
      <param name="key">The key used to store the object in the cache.</param>
      <param name="opType">The type of operation that occurred.</param>
      <param name="version">The version of the cached object.</param>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheOperationDescriptor.Key">
      <summary>Returns the <see cref="P:Microsoft.ApplicationServer.Caching.DataCacheOperationDescriptor.Key" /> property.</summary>
      <returns>A string representing the current value of the <see cref="P:Microsoft.ApplicationServer.Caching.DataCacheOperationDescriptor.Key" /> property.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheOperationDescriptor.RegionName">
      <summary>Returns the <see cref="P:Microsoft.ApplicationServer.Caching.DataCacheOperationDescriptor.RegionName" /> property.</summary>
      <returns>A string representing the current value of the <see cref="P:Microsoft.ApplicationServer.Caching.DataCacheOperationDescriptor.RegionName" /> property.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheOperationDescriptor.ToString">
      <summary>Returns the string representation of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheOperationDescriptor" /> object.</summary>
      <returns>A string representing the current value of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheOperationDescriptor" /> object.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheOperations">
      <summary>An enumeration used to specify specific item or region events that can trigger a cache notification.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheOperations.AddItem">
      <summary>Indicates that an object was added to the cache.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheOperations.ClearRegion">
      <summary>Indicates that a region in the cache was cleared with the <see cref="M:Microsoft.ApplicationServer.Caching.DataCache.ClearRegion(System.String)" /> method.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheOperations.CreateRegion">
      <summary>Indicates that a region in the cache was created with the <see cref="M:Microsoft.ApplicationServer.Caching.DataCache.CreateRegion(System.String,System.Boolean)" /> method. </summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheOperations.RemoveItem">
      <summary>Indicates that an object was removed from the cache. </summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheOperations.RemoveRegion">
      <summary>Indicates that a region in the cache was removed with the <see cref="M:Microsoft.ApplicationServer.Caching.DataCache.RemoveRegion(System.String)" /> method. </summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheOperations.ReplaceItem">
      <summary>Indicates that an object was replaced in the cache. </summary>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheProtectionLevel">
      <summary>Enumeration indicating whether data sent between client and server is signed and/or encrypted.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheProtectionLevel.EncryptAndSign">
      <summary>Indicates both encryption and signing are used.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheProtectionLevel.None">
      <summary>Indicates that neither encryption nor signing is used.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheProtectionLevel.Sign">
      <summary>Indicates that signing without encryption is used.</summary>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheSecurity">
      <summary>Used to enable signing and/or encryption of data sent between client and server.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSecurity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheSecurity" /> class.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSecurity.#ctor(Microsoft.ApplicationServer.Caching.DataCacheSecurityMode,Microsoft.ApplicationServer.Caching.DataCacheProtectionLevel)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheSecurity" /> class, allows you to specify the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheSecurityMode" /> and <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheProtectionLevel" /> for the instance. Applies only to the server versions of AppFabric.</summary>
      <param name="securityMode">Enumeration indicating if channel security is disabled by setting to None; or if channel security is enabled by setting to Transport.</param>
      <param name="protectionLevel">Enumeration indicating whether data sent between client and server is signed and/or encrypted.This property is only valid when the <see cref="P:Microsoft.ApplicationServer.Caching.DataCacheSecurity.SecurityMode" /> property is set to Transport.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSecurity.#ctor(System.Security.SecureString)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheSecurity" /> class. Applies only to Windows Azure Shared Caching.</summary>
      <param name="authorizationToken">A SecureString object that contains the authorization token from the Windows Azure Management Portal.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSecurity.#ctor(System.Security.SecureString,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheSecurity" /> class. Applies only to Windows Azure Shared Caching.</summary>
      <param name="authorizationToken">A SecureString object that contains the authorization token from the Windows Azure Management Portal.</param>
      <param name="sslEnabled">Specifies whether SSL should be used.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSecurity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheSecurity" /> class. </summary>
      <param name="authorizationToken">The authorization token.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSecurity.#ctor(System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheSecurity" /> class. </summary>
      <param name="authorizationToken">The authorization token.</param>
      <param name="sslEnabled">Specifies whether SSL should be used.</param>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheSecurity.ProtectionLevel">
      <summary>The <see cref="P:Microsoft.ApplicationServer.Caching.DataCacheSecurity.ProtectionLevel" /> set for the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheSecurity" /> object.</summary>
      <returns>Returns the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheProtectionLevel" /> set for the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheSecurity" /> object.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheSecurity.SecurityMode">
      <summary>The <see cref="P:Microsoft.ApplicationServer.Caching.DataCacheSecurity.SecurityMode" /> set for the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheSecurity" /> object.</summary>
      <returns>Returns the <see cref="P:Microsoft.ApplicationServer.Caching.DataCacheSecurity.SecurityMode" /> set for the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheSecurity" /> object.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheSecurityMode">
      <summary>Enumeration indicating whether channel security is enabled for data sent between client and server. Applies only to the server versions of AppFabric.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheSecurityMode.Message">
      <summary>Specifies that message security is used. This is the security mode required for AppFabric caches, which use the Access Control Service (ACS). Applies only to the server versions of AppFabric.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheSecurityMode.None">
      <summary>Specifies that no security is enabled. Applies only to the server versions of AppFabric.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheSecurityMode.Transport">
      <summary>Specifies that transport security is enabled. Applies only to the server versions of AppFabric.</summary>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheSerializationProperties">
      <summary>Specifies the serialization properties for a cache client.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheSerializationProperties.#ctor(Microsoft.ApplicationServer.Caching.DataCacheObjectSerializerType,Microsoft.ApplicationServer.Caching.IDataCacheObjectSerializer)">
      <summary>Creates a new DataCacheSerializationProperties object.</summary>
      <param name="cacheObjectSerializerType">The serializer type to use for cached objects.</param>
      <param name="customCacheObjectSerializer">Specifies an object that implements the IDataCacheObjectSerializer interface for custom serialization.</param>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheSerializationProperties.CacheObjectSerializerType">
      <summary>The type of serialization to use for cached objects.</summary>
      <returns>Returns <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheObjectSerializerType" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheSerializationProperties.CustomCacheObjectSerializer">
      <summary>Returns the IDataCacheObjectSerializer for a custom serializer.</summary>
      <returns>Returns <see cref="T:Microsoft.ApplicationServer.Caching.IDataCacheObjectSerializer" />.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheServiceAccountType">
      <summary>Specifies the type of account that is used to run the AppFabric Caching service.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheServiceAccountType.DomainAccount">
      <summary>The AppFabric Caching service runs under a domain account on the cache cluster.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheServiceAccountType.SystemAccount">
      <summary>The AppFabric Caching service runs under a built-in system account on the cache cluster. For example, NETWORK SERVICE.</summary>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheStoreException">
      <summary>An exception object that should be used by read-through or write-behind providers.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheStoreException.#ctor">
      <summary>Creates a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheStoreException" /> class.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheStoreException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheStoreException" /> class.</summary>
      <param name="info">Internal.</param>
      <param name="context">Internal.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheStoreException.#ctor(System.String)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheStoreException" /> class.</summary>
      <param name="message">An error message to associate with this exception object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheStoreException.#ctor(System.String,System.Exception)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheStoreException" /> class.</summary>
      <param name="message">An error message to associate with this exception object.</param>
      <param name="innerException">An inner exception to associate with this exception object.</param>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheStoreProvider">
      <summary>An abstract class that must implemented by read-through or write-behind providers.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheStoreProvider.#ctor">
      <summary>Creates a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheStoreProvider" /> class.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheStoreProvider.Delete(Microsoft.ApplicationServer.Caching.DataCacheItemKey)">
      <summary>This Delete overload is called for a single item that is deleted from the cache associated with the read-through or write-behind provider.</summary>
      <param name="key">The DataCacheItemKey for the item that has been deleted.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheStoreProvider.Delete(System.Collections.ObjectModel.Collection{Microsoft.ApplicationServer.Caching.DataCacheItemKey})">
      <summary>This Delete overload is called for multiple items that are deleted from the cache associated with the read-through or write-behind provider.</summary>
      <param name="keys">A collection of DataCacheItemKeys that should be removed.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheStoreProvider.Dispose">
      <summary>Used to dispose the DataCacheStoreProvider class.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheStoreProvider.Dispose(System.Boolean)">
      <summary>Used to dispose the DataCacheStoreProvider class.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheStoreProvider.Read(Microsoft.ApplicationServer.Caching.DataCacheItemKey)">
      <summary>This Read overload is called to read a single item from the read-through provider.</summary>
      <returns>Returns <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheItem" />.</returns>
      <param name="key">The DataCacheItemKey for the item that should be returned.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheStoreProvider.Read(System.Collections.ObjectModel.ReadOnlyCollection{Microsoft.ApplicationServer.Caching.DataCacheItemKey},System.Collections.Generic.IDictionary{Microsoft.ApplicationServer.Caching.DataCacheItemKey,Microsoft.ApplicationServer.Caching.DataCacheItem})">
      <summary>This Read overload is called to read a multiple items from the read-through provider.</summary>
      <param name="keys">A collection of DataCacheItemKey objects for the objects that should be read.</param>
      <param name="items">A dictionary of DataCacheItemKey objects and DataCacheItem objects that is used to return the results of the read request.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheStoreProvider.Write(Microsoft.ApplicationServer.Caching.DataCacheItem)">
      <summary>This Write method is called to write a single item to the backend associated with the write-behind provider.</summary>
      <param name="item">The DataCacheItem object that should be written to the backend.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheStoreProvider.Write(System.Collections.Generic.IDictionary{Microsoft.ApplicationServer.Caching.DataCacheItemKey,Microsoft.ApplicationServer.Caching.DataCacheItem})">
      <summary>This Write method is called to write multiple items to the backend associated with the write-behind provider.</summary>
      <param name="items">A collection of DataCacheItem objects that should be written to the backend.</param>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheTag">
      <summary>Represents an optional string-based identifier that you can associate with a cached object.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheTag.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheTag" /> class. </summary>
      <param name="tag">The string-based identifier to associate with a cached object. Tags must be less than 65 KB.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheTag.Equals(System.Object)">
      <summary>Compares the current DataCacheTag object for equality with the specified DataCacheTag object.</summary>
      <returns>True if the object implements the ToString method and its value is the same as the string representation of the current DataCacheTag; otherwise, false.</returns>
      <param name="obj">The object whose string representation is compared for equality to the current <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheTag" /> object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheTag.GetHashCode">
      <summary>Returns the hash code for the current <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheTag" /> object.</summary>
      <returns>An integer representing the hash code for the current DataCacheTag object.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheTag.ToString">
      <summary>Returns the string representation of the current <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheTag" /> object.</summary>
      <returns>A string representing the current value of the DataCacheTag object</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheTraceSink">
      <summary>Provides fields that specify the type of cache client tracing to use with the <see cref="M:Microsoft.ApplicationServer.Caching.DataCacheClientLogManager.SetSink(Microsoft.ApplicationServer.Caching.DataCacheTraceSink,System.Diagnostics.TraceLevel)" /> method. Applies only to Windows Azure Caching.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheTraceSink.DiagnosticSink">
      <summary>A trace sink that uses standard Windows Azure Caching diagnostics. Applies only to Windows Azure Caching.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.DataCacheTraceSink.EtwSink">
      <summary>A trace sink that uses Event Tracing for Windows (ETW) to record the trace data. Not supported in Windows Azure Shared Caching.</summary>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.DataCacheTransportProperties">
      <summary>Specifies the transport settings for a cache client.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheTransportProperties.#ctor">
      <summary>Creates a new instance of the <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheTransportProperties" /> class.</summary>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheTransportProperties.ChannelInitializationTimeout">
      <summary>Gets or sets the length of time to wait for a WCF channel initialization before timing out.</summary>
      <returns>Returns <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.DataCacheTransportProperties.Clone">
      <summary>Creates a copy of the current <see cref="T:Microsoft.ApplicationServer.Caching.DataCacheTransportProperties" /> object.</summary>
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheTransportProperties.ConnectionBufferSize">
      <summary>Gets or sets the size of the receive buffers used by transport channels.</summary>
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheTransportProperties.MaxBufferPoolSize">
      <summary>Gets or sets the maximum buffer pool size used by the WCF buffer manager.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheTransportProperties.MaxBufferSize">
      <summary>Gets or sets the maximum buffer size.</summary>
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheTransportProperties.MaxOutputDelay">
      <summary>Gets or sets the maximum length of time to wait before requests are batched and sent to the client.</summary>
      <returns>Returns <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.DataCacheTransportProperties.ReceiveTimeout">
      <summary>Gets or sets the length of time to wait for a request before aborting the channel.</summary>
      <returns>Returns <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.EvictionType">
      <summary>Specifies the eviction used for a cache.</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.EvictionType.Lru">
      <summary>The cache uses an eviction policy of Least-Recently-Used (LRU).</summary>
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.EvictionType.None">
      <summary>The cache does not evict objects.</summary>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.ExpirationType"></member>
    <member name="F:Microsoft.ApplicationServer.Caching.ExpirationType.AbsoluteExpiration">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.ExpirationType.Default">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.ExpirationType.None">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.ExpirationType.NotProvided">
      <summary />
    </member>
    <member name="F:Microsoft.ApplicationServer.Caching.ExpirationType.SlidingExpiration">
      <summary />
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.HostCacheStats">
      <summary>Stores cache statistics used in conjunction with the Get-CacheStatistics Windows PowerShell command.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.HostCacheStats.#ctor">
      <summary>Creates a new <see cref="T:Microsoft.ApplicationServer.Caching.HostCacheStats" /> object.</summary>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.HostCacheStats.ItemCount">
      <summary>The number of items in the cache.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.HostCacheStats.MissCount">
      <summary>The number of cache misses for requested items that were not present in the cache.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.HostCacheStats.NamedCacheCount">
      <summary>The number of named caches.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.HostCacheStats.RegionCount">
      <summary>The number of regions.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.HostCacheStats.RequestCount">
      <summary>The total number of requests.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.HostCacheStats.Size">
      <summary>The size in bytes of cached items.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.HostCacheStats.ToString"></member>
    <member name="T:Microsoft.ApplicationServer.Caching.ICustomProvider">
      <summary>Represent an ICustomProvider interface. Applies only to the server versions of AppFabric.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.AddUser(System.String,System.String)">
      <summary>Adds a Windows account to the list of allowed users that have access to the configuration store..</summary>
      <returns>Returns a<see cref="T:System.Object" /> that can be passed to the <see cref="M:Microsoft.ApplicationServer.Caching.ICustomProvider.RemoveUser(System.String,System.String,System.Object)" /> method in the case of a rollback.</returns>
      <param name="machine">The machine name.</param>
      <param name="user">The user name.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.BeginTransaction">
      <summary>Begins a transaction to provide isolation from other configuration store access.</summary>
      <returns>Returns a<see cref="T:System.Object" /> that represents the transaction context..</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.Cleanup">
      <summary>Enables the provider to clean up any data in the cache cluster configuration store.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.Delete(System.Object,System.String)">
      <summary>Deletes an existing entry from the configuration store.</summary>
      <param name="transactionContext">The transaction context object associated with this entry.</param>
      <param name="type">The type of entry to be enumerated.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.Delete(System.Object,System.String,System.String,System.Int64)">
      <summary>Deletes an existing entry from the configuration store.</summary>
      <returns>Returns <see cref="T:System.Boolean" />. If the deletion succeeds, the return value is true. If the entry does not exist or if the version does not match, the return value is false.</returns>
      <param name="transactionContext">The transaction context object associated with this entry.</param>
      <param name="type">The type of entry to be enumerated.</param>
      <param name="key">The key of the entry.</param>
      <param name="oldVersion">The version of the entry. If this value is less than or equal to 0, the deletion occurs regardless of the version. If this value is greater than 0, then the deletion is only performed if the version of the entry matches the version provided.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.EndTransaction(System.Object,System.Boolean)">
      <summary>Commits or rollbacks a transaction previously started with the <see cref="M:Microsoft.ApplicationServer.Caching.ICustomProvider.BeginTransaction" /> method.</summary>
      <param name="transactionContext">An object that represents the transaction context. This object is returned from the <see cref="M:Microsoft.ApplicationServer.Caching.ICustomProvider.BeginTransaction" /> method.</param>
      <param name="rollback">If true, the transaction should be rolled back. If false, the transaction should be committed.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.GetEntries(System.Object,System.String)">
      <summary>Enumerates all the entries of the specified type.</summary>
      <returns>Returns a<see cref="T:System.Collections.Generic.ICollection`1" /> of entries.</returns>
      <param name="transactionContext">The transaction context object.</param>
      <param name="type">The type of entry to enumerate.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.GetEntry(System.Object,System.String,System.String)">
      <summary>Retrieves a <see cref="T:Microsoft.ApplicationServer.Caching.ConfigStoreEntry" /> object from the configuration store.</summary>
      <returns>Returns a<see cref="T:Microsoft.ApplicationServer.Caching.ConfigStoreEntry" /> object.</returns>
      <param name="transactionContext">The transaction context object.</param>
      <param name="type">The type of the entry.</param>
      <param name="key">The key of the entry.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.GetStoreUtcTime(System.Object)">
      <summary>Obtains the current UTC time based on the clock for the configuration store.</summary>
      <returns>Returns <see cref="T:System.DateTime" />.</returns>
      <param name="transactionContext">The transaction context object.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.GetValue(System.Object,System.String,System.String)">
      <summary>Retrieves the value of a specific entry in the configuration store.</summary>
      <returns>The value of a specified entry in the configuration store.</returns>
      <param name="transactionContext">The transaction context.</param>
      <param name="type">The type of the entry.</param>
      <param name="key">The key for the entry.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.Initialize">
      <summary>Enables the custom provider to initialize the cache cluster configuration store.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.Insert(System.Object,System.String,System.String,System.Byte[],System.Int64)">
      <summary>Inserts an entry into the configuration store.</summary>
      <returns>Return true if the entry is successfully inserted. Return false if the entry with the same key and type already exists. For all other failures, a <see cref="T:Microsoft.ApplicationServer.Caching.ConfigStoreException" /> must be thrown.</returns>
      <param name="transactionContext">The transaction context object.</param>
      <param name="type">The type of the entry.</param>
      <param name="key">The key of the entry.</param>
      <param name="data">The value of the entry.</param>
      <param name="version">The version of the entry.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.IsInitialized">
      <summary>Gets the initialization status of the configuration store.</summary>
      <returns>Returns true if the configuration store is initialized.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.Open(System.String)">
      <summary>Initializes the configuration store with a connection string that is used for subsequent calls.</summary>
      <param name="connectionString">The connection string to the configuration store.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.RemoveUser(System.String,System.String,System.Object)">
      <summary>Removes a Windows account from the list of accounts that have permission t the access the configuration store.</summary>
      <param name="machine">The machine name.</param>
      <param name="user">The user name.</param>
      <param name="state">An object that represents the state if this call is being used to rollback a previous call to <see cref="M:Microsoft.ApplicationServer.Caching.ICustomProvider.AddUser(System.String,System.String)" />.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.RetrieveStoreVersion">
      <summary>Retrieves the version of the configuration store.</summary>
      <returns>Returns <see cref="T:System.Version" />.</returns>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.TestConnection">
      <summary>Determines whether the configuration store is available.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.ICustomProvider.Update(System.Object,System.String,System.String,System.Byte[],System.Int64)">
      <summary>Updates an entry in the store.</summary>
      <returns>Returns <see cref="T:System.Boolean" />. If the update succeeds, the return value is true. If the entry does not exist or if the version does not match, the return value is false.</returns>
      <param name="transactionContext">The transaction context object.</param>
      <param name="type">The type of the entry.</param>
      <param name="key">The key of the entry.</param>
      <param name="data">The updated value of the entry.</param>
      <param name="oldVersion">The version of the entry. If this value is less than or equal to 0, the update occurs regardless of the version. If this value is greater than 0, then the update is only performed if the version of the entry matches the version provided.</param>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.IDataCacheObjectSerializer">
      <summary>Implement this interface to provide custom serialization for cached objects.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.IDataCacheObjectSerializer.Deserialize(System.IO.Stream)">
      <summary>Deserializes a memory stream to an object.</summary>
      <returns>Returns <see cref="T:System.Object" />.</returns>
      <param name="stream">The memory stream returned from the cache.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.IDataCacheObjectSerializer.Serialize(System.IO.Stream,System.Object)">
      <summary>Serializes an object to a memory stream.</summary>
      <param name="stream">A memory stream to use to store the serialized object.</param>
      <param name="value">The object to serialize.</param>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.NamedCacheStats">
      <summary>Stores cache statistics used in conjunction with the Get-CacheStatistics Windows PowerShell command.</summary>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.NamedCacheStats.#ctor">
      <summary>Creates a new <see cref="T:Microsoft.ApplicationServer.Caching.NamedCacheStats" /> object.</summary>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.NamedCacheStats.IncomingBandwidth">
      <summary>The total size in bytes of requests sent to the cache.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.NamedCacheStats.ItemCount">
      <summary>The number of items in the cache.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.NamedCacheStats.MissCount">
      <summary>The number of cache misses for requested items that were not present in the cache.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.NamedCacheStats.OutgoingBandwidth">
      <summary>The total size in bytes of responses sent from the cache.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.NamedCacheStats.ReadRequestCount">
      <summary>The number of read operations from  the cache.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.NamedCacheStats.RegionCount">
      <summary>The number of regions in the cache.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.NamedCacheStats.RequestCount">
      <summary>The number of requests on the cache.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.NamedCacheStats.RestRequestCount"></member>
    <member name="P:Microsoft.ApplicationServer.Caching.NamedCacheStats.Size">
      <summary>The size in bytes of the cache.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.ApplicationServer.Caching.NamedCacheStats.WriteRequestCount">
      <summary>The number of write operations to the cache.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.SafeStreamWriter"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.SafeStreamWriter.#ctor(System.IO.Stream)">
      <param name="stream">Internal.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.SafeStreamWriter.#ctor(System.String)">
      <param name="path">Internal.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.SafeStreamWriter.#ctor(System.String,System.Boolean)">
      <param name="path">Internal.</param>
      <param name="append">Internal.</param>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.VelocityAuthorizationException"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityAuthorizationException.#ctor"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityAuthorizationException.#ctor(System.String)"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityAuthorizationException.#ctor(System.String,System.Exception)"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityAuthorizationException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)"></member>
    <member name="T:Microsoft.ApplicationServer.Caching.VelocityPacketException"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityPacketException.#ctor"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityPacketException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityPacketException.#ctor(System.String)"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityPacketException.#ctor(System.String,System.Exception)"></member>
    <member name="T:Microsoft.ApplicationServer.Caching.VelocityPacketFormatException"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityPacketFormatException.#ctor"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityPacketFormatException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <param name="info">Internal.</param>
      <param name="context">Internal.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityPacketFormatException.#ctor(System.String)">
      <param name="message">Internal.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityPacketFormatException.#ctor(System.String,System.Exception)">
      <param name="message">Internal.</param>
      <param name="innerException">Internal.</param>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.VelocityPacketFormatFatalException"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityPacketFormatFatalException.#ctor"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityPacketFormatFatalException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <param name="info">Internal.</param>
      <param name="context">Internal.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityPacketFormatFatalException.#ctor(System.String)">
      <param name="message">Internal.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityPacketFormatFatalException.#ctor(System.String,System.Exception)">
      <param name="message">Internal.</param>
      <param name="innerException">Internal.</param>
    </member>
    <member name="T:Microsoft.ApplicationServer.Caching.VelocityPacketTooBigException"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityPacketTooBigException.#ctor"></member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityPacketTooBigException.#ctor(System.String)">
      <param name="message">Internal.</param>
    </member>
    <member name="M:Microsoft.ApplicationServer.Caching.VelocityPacketTooBigException.#ctor(System.String,System.Exception)">
      <param name="message">Internal.</param>
      <param name="innerException">Internal.</param>
    </member>
  </members>
</doc>