<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Practices.EnterpriseLibrary.PolicyInjection</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Unity.TransientPolicyBuildUpExtension">
            <summary>
            Container extension that supports supplying additional, transient policies when building up an object
            through a container.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Unity.TransientPolicyBuildUpExtension.Initialize">
            <summary>
            Initializes the container with this extension's functionality.
            </summary>
            <remarks>
            This extension does not permfom any initialization.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Unity.TransientPolicyBuildUpExtension.BuildUp(System.Type,System.Object,System.String,Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Runs an existing object through the container and performs injection on it.
            </summary>
            <param name="t">The <see cref="T:System.Type"/> of object to perform injection on.</param>
            <param name="existing">The instance to build up.</param>
            <param name="name">The name to use when looking up the type mappings and other configurations.</param>
            <param name="injectionMembers">The providers of transient policies.</param>
            <returns>The resulting object. By default, this will be the object supplied in the <paramref name="existing"/> 
            parameter. However, container extensions may add things such as automatic proxy creation, which would cause this method to 
            return a different object that is still type-compatible with the t parameter.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Utility.ServiceLocatorExtensions">
            <summary>
            Provides extension methods on <see cref="T:Microsoft.Practices.ServiceLocation.IServiceLocator"/> for convenience.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Utility.ServiceLocatorExtensions.Dispose(Microsoft.Practices.ServiceLocation.IServiceLocator)">
            <summary>
            Disposes <paramref name="locator"/> if it implements the <see cref="T:System.IDisposable"/> interface.
            </summary>
            <param name="locator">The service locator to dispose, if possible.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ArgumentValidationException">
            <summary>
            The exception that is thrown by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandler"/> if validation fails.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ArgumentValidationException.#ctor(Microsoft.Practices.EnterpriseLibrary.Validation.ValidationResults,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ArgumentValidationException"/> class, storing the validation
            results and the name of the parameter that failed.
            </summary>
            <param name="validationResults">The <see cref="P:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ArgumentValidationException.ValidationResults"/> returned from the Validation Application Block.</param>
            <param name="paramName">The parameter that failed validation.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ArgumentValidationException.ToString">
            <summary>
            Creates and returns a string representation of the current exception.
            </summary>
            <returns>A string representation of the current exception.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ArgumentValidationException.ValidationResults">
            <summary>
            Gets the validation results for the failure.
            </summary>
            <value>The validation results for the failure.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.CategoryFormatter">
            <summary>
            Represents a formatter object that allows for the replacement of tokens in
            a log handler category string.
            </summary>
            <remarks>This class supports the following replacements:
            <list>
            <item><term>{method}</term><description>The target method name.</description></item>
            <item><term>{type}</term><description>The target method's implementing type.</description></item>
            <item><term>{namespace}</term><description>The namespace that contains the target's type.</description></item>
            <item><term>{assembly}</term><description>The assembly that contains the target's type.</description></item>
            </list></remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.CategoryFormatter.#ctor(System.Reflection.MethodBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.CategoryFormatter"/> class by using information from the
            given method.
            </summary>
            <param name="method">The method used to generate the category replacements.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.CategoryFormatter.FormatCategory(System.String)">
            <summary>
            Performs the formatting operation by replacing tokens in the template.
            </summary>
            <param name="template">The template string to replace the tokens in.</param>
            <returns>The template, with tokens replaced.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandler">
            <summary>
            Represents an <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/> that runs any exceptions returned from the
            target through the Exception Handling Application Block.
            </summary>
            <remarks>If the exception policy is configured to swallow exceptions,
            do not configure this call handler on a method that has a non-void return value,
            because the handler doesn't know which value to return if the exception is swallowed.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandler.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandler"/> class that processses exceptions
            by using the exception policy with the given name.
            </summary>
            <param name="policyName">The name of the exception policy to use.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandler.#ctor(System.String,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandler"/> class that processses exceptions
            by using the exception policy with the given name.
            </summary>
            <param name="policyName">The name of the exception policy to use.</param>
            <param name="order">The order in which the handler will be executed.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandler.Invoke(Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation,Microsoft.Practices.Unity.InterceptionExtension.GetNextHandlerDelegate)">
            <summary>
            Processes the method call.
            </summary>
            <remarks>This handler does nothing before the call. If an exception is
            returned, it runs the exception through the Exception Handling Application Block.</remarks>
            <param name="input">The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation"/> that has information about the call.</param>
            <param name="getNext">The delegate to call to get the next handler in the pipeline.</param>
            <returns>Return value from the target, or the (possibly changed) exceptions.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandler.ExceptionPolicyName">
            <summary>
            Gets the name of the exception policy used by this handler.
            </summary>
            <value>The name of the exception policy.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandler.Order">
            <summary>
            Gets or sets the order in which the handler will be executed.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandlerAttribute">
            <summary>
            Applies the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandler"/> to the target.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandlerAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandlerAttribute"/> class using the given
            exception policy name.
            </summary>
            <remarks>When using this attribute, the exception policy is always read from
            the default configuration.</remarks>
            <param name="policyName">The name of the exception policy name from configuration.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandlerAttribute.CreateHandler(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Derived classes implement this method. When called, it
            creates a new call handler as specified in the attribute
            configuration.
            </summary>
            <returns>A new call handler object.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandlerAttribute.PolicyName">
            <summary>
            Get or sets the name of the exception policy that is used by the handler.
            </summary>
            <value>The name of the exception policy.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler">
            <summary>
            An <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/> that will log information using the
            Logging Application Block before and/or after the
            call to the target completes.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler"/> class with default settings.
            </summary>
            <remarks>See the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerDefaults"/> class for the default values.</remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler.#ctor(System.Int32,System.Boolean,System.Boolean,System.String,System.String,System.Boolean,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler"/> class that writes entries using the given logging settings.
            </summary>
            <param name="eventId">The event ID to include in log entries.</param>
            <param name="logBeforeCall"><see langword="true"/> to log information before calling the target; otherwise, <see langword="false"/>.</param>
            <param name="logAfterCall"><see langword="true"/> to log information after calling the target; otherwise, <see langword="false"/>.</param>
            <param name="beforeMessage">The message to include in an  entry logged before the call.</param>
            <param name="afterMessage">The message to include in an entry logged after the call.</param>
            <param name="includeParameters"><see langword="true"/> to include the parameter values in the log entry; otherwise, <see langword="false"/>.</param>
            <param name="includeCallStack"><see langword="true"/> to include the current call stack in the log entry; otherwise, <see langword="false"/>.</param>
            <param name="includeCallTime"><see langword="true"/> to include the time to execute the target in the log entry; otherwise, <see langword="false"/>.</param>
            <param name="priority">The priority of the log entry.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler.#ctor(System.Int32,System.Boolean,System.Boolean,System.String,System.String,System.Boolean,System.Boolean,System.Boolean,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler"/> class that writes entries using the given logging settings.
            </summary>
            <param name="eventId">The event ID to include in log entries.</param>
            <param name="logBeforeCall"><see langword="true"/> to log information before calling the target; otherwise, <see langword="false"/>.</param>
            <param name="logAfterCall"><see langword="true"/> to log information after calling the target; otherwise, <see langword="false"/>.</param>
            <param name="beforeMessage">The message to include in an  entry logged before the call.</param>
            <param name="afterMessage">The message to include in an entry logged after the call.</param>
            <param name="includeParameters"><see langword="true"/> to include the parameter values in the log entry; otherwise, <see langword="false"/>.</param>
            <param name="includeCallStack"><see langword="true"/> to include the current call stack in the log entry; otherwise, <see langword="false"/>.</param>
            <param name="includeCallTime"><see langword="true"/> to include the time to execute the target in the log entry; otherwise, <see langword="false"/>.</param>
            <param name="priority">The priority of the log entry.</param>
            <param name="order">The order in which the handler will be executed.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler.Invoke(Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation,Microsoft.Practices.Unity.InterceptionExtension.GetNextHandlerDelegate)">
            <summary>
            Executes the call handler.
            </summary>
            <param name="input"><see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation"/> containing the information about the current call.</param>
            <param name="getNext">delegate to get the next handler in the pipeline.</param>
            <returns>Return value from the target method.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler.EventId">
            <summary>
            Event ID to include in log entry
            </summary>
            <value>event id</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler.LogBeforeCall">
            <summary>
            Should there be a log entry before calling the target?
            </summary>
            <value>true = yes, false = no</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler.LogAfterCall">
            <summary>
            Should there be a log entry after calling the target?
            </summary>
            <value>true = yes, false = no</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler.BeforeMessage">
            <summary>
            Message to include in a pre-call log entry.
            </summary>
            <value>The message</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler.AfterMessage">
            <summary>
            Message to include in a post-call log entry.
            </summary>
            <value>the message.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler.Categories">
            <summary>
            Gets the collection of categories to place the log entries into.
            </summary>
            <remarks>The category strings can include replacement tokens. See
            the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.CategoryFormatter"/> class for the list of tokens.</remarks>
            <value>The list of category strings.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler.IncludeParameters">
            <summary>
            Should the log entry include the parameters to the call?
            </summary>
            <value>true = yes, false = no</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler.IncludeCallStack">
            <summary>
            Should the log entry include the call stack?
            </summary>
            <remarks>Logging the call stack requires full trust code access security permissions.</remarks>
            <value>true = yes, false = no</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler.IncludeCallTime">
            <summary>
            Should the log entry include the time to execute the target?
            </summary>
            <value>true = yes, false = no</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler.Priority">
            <summary>
            Priority for the log entry.
            </summary>
            <value>priority</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler.Severity">
            <summary>
            Severity to log at.
            </summary>
            <value><see cref="T:System.Diagnostics.TraceEventType"/> giving the severity.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler.Order">
            <summary>
            Gets or sets the order in which the handler will be executed
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerDefaults">
            <summary>
            Specifies the default values for the various <see cref="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler"/> settings.
            </summary>
            <remarks>The default values are:
            <list type="table">
            <item><term>EventId</term><description>0</description></item>
            <item><term>LogBeforeCall</term><description>true</description></item>
            <item><term>LogAfterCall</term><description>true</description></item>
            <item><term>BeforeMessage</term><description></description></item>
            <item><term>AfterMessage</term><description></description></item>
            <item><term>Title</term><description>Call Logging</description></item>
            <item><term>IncludeParameters</term><description>true</description></item>
            <item><term>IncludeCallStack</term><description>false</description></item>
            <item><term>IncludeCallTime</term><description>true</description></item>
            <item><term>Priority</term><description>-1</description></item>
            <item><term>Severity</term><description><see cref="T:System.Diagnostics.TraceEventType"/>.Information</description></item>
            </list></remarks>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerDefaults.Order">
            <summary>
            Default Order = 0
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerDefaults.EventId">
            <summary>
            Default EventId = 0
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerDefaults.LogBeforeCall">
            <summary>
            Default option to log before the call = true
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerDefaults.LogAfterCall">
            <summary>
            Default option to log after the call = true
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerDefaults.IncludeParameters">
            <summary>
            Default option to include parameter values = true
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerDefaults.IncludeCallStack">
            <summary>
            Default option to include the call stack = false
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerDefaults.IncludeCallTime">
            <summary>
            Default option to include total time to call target = true
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerDefaults.Priority">
            <summary>
            Default priority = -1
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerDefaults.Severity">
            <summary>
            Default severity = <see cref="T:System.Diagnostics.TraceEventType"/>.Information
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerDefaults.BeforeMessage">
            <summary>
            Default message in before-call logs = nothing
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerDefaults.AfterMessage">
            <summary>
            Default message in after-call logs = nothing
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerDefaults.Title">
            <summary>
            Default log entry title = "Call Logging" (localizable)
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerAttribute">
            <summary>
            Applies the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler"/> to the target type, property, or method.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerAttribute.CreateHandler(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Creates the log handler for the target using the configured values.
            </summary>
            <returns>the created <see cref="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler"/>.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerAttribute.Categories">
            <summary>
            Gets or sets the collection of categories to place the log entries into.
            </summary>
            <remarks>The category strings can include replacement tokens. See
            the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.CategoryFormatter"/> class for the list of tokens.</remarks>
            <value>The list of category strings.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerAttribute.EventId">
            <summary>
            Event ID to include in log entry.
            </summary>
            <value>event id</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerAttribute.LogBeforeCall">
            <summary>
            Should there be a log entry before calling the target?
            </summary>
            <value>true = yes, false = no</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerAttribute.LogAfterCall">
            <summary>
            Should there be a log entry after calling the target?
            </summary>
            <value>true = yes, false = no</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerAttribute.BeforeMessage">
            <summary>
            Message to include in a pre-call log entry.
            </summary>
            <value>The message</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerAttribute.AfterMessage">
            <summary>
            Message to include in a post-call log entry.
            </summary>
            <value>the message.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerAttribute.IncludeParameters">
            <summary>
            Should the log entry include the parameters to the call?
            </summary>
            <value>true = yes, false = no</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerAttribute.IncludeCallStack">
            <summary>
            Should the log entry include the call stack?
            </summary>
            <remarks>Logging the call stack requires full trust code access security permissions.</remarks>
            <value>true = yes, false = no</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerAttribute.IncludeCallTime">
            <summary>
            Should the log entry include the time to execute the target?
            </summary>
            <value>true = yes, false = no</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerAttribute.Priority">
            <summary>
            Priority for the log entry.
            </summary>
            <value>priority</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandlerAttribute.Severity">
            <summary>
            Severity to log at.
            </summary>
            <value><see cref="T:System.Diagnostics.TraceEventType"/> giving the severity.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler">
            <summary>
            An <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/> that updates performance counters when calling the target.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.TotalInstanceName">
            <summary>
            Instance name for the "total" instance
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.NumberOfCallsCounterName">
            <summary>
            Number of calls counter name
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.CallsPerSecondCounterName">
            <summary>
            Calls per second counter name
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.AverageCallDurationCounterName">
            <summary>
            Average call duration counter name
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.AverageCallDurationBaseCounterName">
            <summary>
            Average call duration base counter name
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.TotalExceptionsCounterName">
            <summary>
            Total exceptions counter name
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.ExceptionsPerSecondCounterName">
            <summary>
            Exceptions per second counter name
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.#ctor(System.String,System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler"/> using the given category
            and instance name.
            </summary>
            <remarks>See the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerDefaults"/> for a list
            of the default values for each property.</remarks>
            <param name="category">Performance counter category to update. This counter category
            must be installed separately or the handler will fail.</param>
            <param name="counterInstanceName">Counter instance name. This may include replacement
            tokens. See the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.MethodInvocationFormatter"/> class for a list of the tokens.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.#ctor(System.String,System.String,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler"/> using the given settings.
            </summary>
            <param name="category">Performance counter category to update. This counter category
            must be installed separately or the handler will fail.</param>
            <param name="instanceName">Counter instance name. This may include replacement
            tokens. See the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.MethodInvocationFormatter"/> class for a list of the tokens.</param>
            <param name="useTotalCounter">Should a "Total" instance be updated?</param>
            <param name="incrementNumberOfCalls">Should the number of calls counter be updated?</param>
            <param name="incrementCallsPerSecond">Should the "calls / second" counter be updated?</param>
            <param name="incrementAverageCallDuration">Should the "average seconds / call" counter be updated?</param>
            <param name="incrementTotalExceptions">Should the "# of exceptions" counter be updated?</param>
            <param name="incrementExceptionsPerSecond">Should the "# exceptions / second" counter be updated?</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.#ctor(System.String,System.String,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            Creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler"/> using the given settings.
            </summary>
            <param name="category">Performance counter category to update. This counter category
            must be installed separately or the handler will fail.</param>
            <param name="instanceName">Counter instance name. This may include replacement
            tokens. See the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.MethodInvocationFormatter"/> class for a list of the tokens.</param>
            <param name="useTotalCounter">Should a "Total" instance be updated?</param>
            <param name="incrementNumberOfCalls">Should the number of calls counter be updated?</param>
            <param name="incrementCallsPerSecond">Should the "calls / second" counter be updated?</param>
            <param name="incrementAverageCallDuration">Should the "average seconds / call" counter be updated?</param>
            <param name="incrementTotalExceptions">Should the "# of exceptions" counter be updated?</param>
            <param name="incrementExceptionsPerSecond">Should the "# exceptions / second" counter be updated?</param>
            <param name="handlerOrder">Order of the handler.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.Invoke(Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation,Microsoft.Practices.Unity.InterceptionExtension.GetNextHandlerDelegate)">
            <summary>
            Executes the handler. Increments the various counter according to configuration.
            </summary>
            <param name="input"><see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation"/> describing the current call.</param>
            <param name="getNext">delegate to call to get the next handler in the pipeline.</param>
            <returns>Return value from target method.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.UseTotalCounter">
            <summary>
            Should a "Total" instance be updated?
            </summary>
            <value>true or false</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.IncrementNumberOfCalls">
            <summary>
            Should the number of calls counter be updated?
            </summary>
            <value>true or false</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.Category">
            <summary>
            Performance counter category to update.
            </summary>
            <value>category name</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.InstanceName">
            <summary>
            Counter instance name. This may include replacement
            tokens. See the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.MethodInvocationFormatter"/> class for a list of the tokens.
            </summary>
            <value>instance name.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.IncrementCallsPerSecond">
            <summary>
            Should the "calls / second" counter be updated?
            </summary>
            <value>true or false</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.IncrementAverageCallDuration">
            <summary>
            Should the "average seconds / call" counter be updated?
            </summary>
            <value>true or false</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.IncrementTotalExceptions">
            <summary>
            Should the "# of exceptions" counter be updated?
            </summary>
            <value>true or false</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.IncrementExceptionsPerSecond">
            <summary>
            Should the "# exceptions / second" counter be updated?
            </summary>
            <value>true or false</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler.Order">
            <summary>
            Gets or sets the order in which the handler will be executed
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerDefaults">
            <summary>
            Defaults for the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerDefaults"/> class.
            </summary>
            <remarks>The default values are:
            <list>
            <item><term>UseTotalCounter</term><description>true</description></item>
            <item><term>IncrementNumberOfCalls</term><description>true</description>&gt;</item>
            <item><term>IncrementCallsPerSecond</term><description>true</description></item>
            <item><term>IncrementAverageCallDuration</term><description>true</description></item>
            <item><term>IncrementTotalExceptions</term><description>false</description></item>
            <item><term>IncrementExceptionsPerSecond</term><description>false</description></item>
            </list>
            </remarks>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerDefaults.UseTotalCounter">
            <summary>
            Use total counter = true
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerDefaults.IncrementNumberOfCalls">
            <summary>
            Increment number of calls counter = true
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerDefaults.IncrementCallsPerSecond">
            <summary>
            Increment calls per second counter = true
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerDefaults.IncrementAverageCallDuration">
            <summary>
            Increment seconds / call counter = true
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerDefaults.IncrementTotalExceptions">
            <summary>
            Increment total number of exceptions counter = false
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerDefaults.IncrementExceptionsPerSecond">
            <summary>
            Increment exceptions per second counter = false
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerAttribute">
            <summary>
            An attribute that applies the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler"/> to the target.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerAttribute.#ctor(System.String,System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerAttribute"/> with the given 
            category and instance names. All other properties start at the default values.
            </summary>
            <remarks>See the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerDefaults"/> class for
            the default values.</remarks>
            <param name="category">Performance counter category name.</param>
            <param name="instanceName">Performance counter instance name. This may contain substitution
            tokens; see <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.MethodInvocationFormatter"/> for the list of tokens.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerAttribute.CreateHandler(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Derived classes implement this method. When called, it
            creates a new call handler as specified in the attribute
            configuration.
            </summary>
            <returns>A new call handler object.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerAttribute.CategoryName">
            <summary>
            Performance counter category to update.
            </summary>
            <value>category name</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerAttribute.InstanceName">
            <summary>
            Counter instance name. This may include replacement
            tokens. See the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.MethodInvocationFormatter"/> class for a list of the tokens.
            </summary>
            <value>instance name.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerAttribute.IncrementAverageCallDuration">
            <summary>
            Should the "average seconds / call" counter be updated?
            </summary>
            <value>true or false</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerAttribute.IncrementCallsPerSecond">
            <summary>
            Should the "calls / second" counter be updated?
            </summary>
            <value>true or false</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerAttribute.IncrementExceptionsPerSecond">
            <summary>
            Should the "# exceptions / second" counter be updated?
            </summary>
            <value>true or false</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerAttribute.IncrementNumberOfCalls">
            <summary>
            Should the number of calls counter be updated?
            </summary>
            <value>true or false</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerAttribute.IncrementTotalExceptions">
            <summary>
            Should the "# of exceptions" counter be updated?
            </summary>
            <value>true or false</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandlerAttribute.UseTotalCounter">
            <summary>
            Should a "Total" instance be updated?
            </summary>
            <value>true or false</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.TraceLogEntry">
            <summary>
            A <see cref="T:Microsoft.Practices.EnterpriseLibrary.Logging.LogEntry"/> class that contains the extra information logged
            by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.TraceLogEntry.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.TraceLogEntry"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.TraceLogEntry.#ctor(System.Object,System.String,System.Int32,System.Int32,System.Diagnostics.TraceEventType,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.TraceLogEntry"/> class with the specified values.
            </summary>
            <param name="message">The log message.</param>
            <param name="category">The log category.</param>
            <param name="priority">The log priority.</param>
            <param name="eventId">The log event id.</param>
            <param name="severity">The log severity.</param>
            <param name="title">The log title.</param>
            <param name="properties">Extra properties. This contains the parameters to the call.</param>
            <param name="typeName">The name of type that implements the method being called.</param>
            <param name="methodName">The name of the method that is called.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.TraceLogEntry.#ctor(System.Object,System.Collections.Generic.ICollection{System.String},System.Int32,System.Int32,System.Diagnostics.TraceEventType,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.TraceLogEntry"/> class with the specified values.
            </summary>
            <param name="message">The log message.</param>
            <param name="categories">The categories of the log entry.</param>
            <param name="priority">The log priority.</param>
            <param name="eventId">The log event id.</param>
            <param name="severity">The log severity.</param>
            <param name="title">The log title.</param>
            <param name="properties">Extra properties. This contains the parameters to the call.</param>
            <param name="typeName">The name of type that implements the method being called.</param>
            <param name="methodName">The name of the method that is called.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.TraceLogEntry.TypeName">
            <summary>
            Type name
            </summary>
            <value>type name</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.TraceLogEntry.MethodName">
            <summary>
            Method name
            </summary>
            <value>method name</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.TraceLogEntry.ReturnValue">
            <summary>
            Return value from the call.
            </summary>
            <value>return value</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.TraceLogEntry.CallStack">
            <summary>
            The call stack from the current call.
            </summary>
            <value>call stack string.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.TraceLogEntry.Exception">
            <summary>
            Exception thrown from the target.
            </summary>
            <value>If exception was thrown, this is the exception object. Null if no exception thrown.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.TraceLogEntry.CallTime">
            <summary>
            Total time to call the target.
            </summary>
            <value>null if not logged, else the elapsed time.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.TraceLogEntry.ElapsedTime">
            <summary>
            This is to support WMI instrumentation by returning
            the actual <see cref="P:Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.TraceLogEntry.CallTime"/> 
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandler">
            <summary>
            An <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/> that runs validation of a call's parameters
            before calling the target.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandler.#ctor(System.String,Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.SpecificationSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandler"/> class that uses the given
            rule set and <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.SpecificationSource"/> to get the validation rules.
            </summary>
            <param name="ruleSet">The validation rule set specified in the configuration.</param>
            <param name="specificationSource">A value that indicates whether the validation should come from the configuration, from attributes, or from both sources.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandler.#ctor(System.String,Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.SpecificationSource,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandler"/> class that uses the given
            rule set, <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.SpecificationSource"/> to get the validation rules, and handler order.
            </summary>
            <param name="ruleSet">The validation rule set specified in the configuration.</param>
            <param name="specificationSource">A value that indicates whether the validation should come from the configuration, from attributes, or from both sources.</param>
            <param name="handlerOrder">The order of the handler.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandler.#ctor(System.String,Microsoft.Practices.EnterpriseLibrary.Validation.ValidatorFactory,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandler"/> class that uses the given
            rule set, <see cref="P:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandler.ValidatorFactory"/> to get the validation rules.
            </summary>
            <param name="ruleSet">The validation rule set specified in the configuration.</param>
            <param name="validatorFactory">The <see cref="P:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandler.ValidatorFactory"/> to use when building the validator for the 
            type of a parameter, or <see langword="null"/> if no such validator is desired.</param>
            <param name="handlerOrder">The order of the handler.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandler.Invoke(Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation,Microsoft.Practices.Unity.InterceptionExtension.GetNextHandlerDelegate)">
            <summary>
            Runs the call handler. This does validation on the parameters, and if validation
            passes it calls the handler. It throws <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ArgumentValidationException"/>
            if validation fails.
            </summary>
            <param name="input">The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation"/> that contains the details of the current call.</param>
            <param name="getNext">The delegate to call to get the next handler in the pipeline.</param>
            <returns>The eturn value from the target.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandler.Order">
            <summary>
            Gets or sets the order in which the handler will be executed.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandler.RuleSet">
            <summary>
            Gets the rule set for this call handler.
            </summary>
            <value>The rule set name.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandler.ValidatorFactory">
            <summary>
            Gets the factory used to build validators.
            </summary>
            <value>The validator factory.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.SpecificationSource">
            <summary>
            Specifies where the information for validation should come from.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.SpecificationSource.Both">
            <summary>
            Configuration and type attributes.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.SpecificationSource.Attributes">
            <summary>
            Type attributes only, ignoring configuration.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.SpecificationSource.Configuration">
            <summary>
            Configuration only, ignoring type attributes.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.SpecificationSource.ParameterAttributesOnly">
            <summary>
            Only use attributes on the parameters themselves; ignore types and configuration.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandlerAttribute">
            <summary>
            Applies the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandler"/> to its target.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandlerAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandlerAttribute"/> that uses the
            default rule set.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandlerAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandlerAttribute"/> that uses the
            given rule set.
            </summary>
            <param name="ruleSet">The name of the rule set to use.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandlerAttribute.CreateHandler(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Derived classes implement this method. When called, it
            creates a new call handler as specified in the attribute
            configuration.
            </summary>
            <returns>A new call handler object.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandlerAttribute.SpecificationSource">
            <summary>
            Gets the <see cref="P:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandlerAttribute.SpecificationSource"/> that determines where to get validation rules from.
            </summary>
            <value>The specification source.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.AssemblyMatchingRuleData">
            <summary>
            Configuration element for the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.AssemblyMatchingRule"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchingRuleData">
            <summary>
            A configuration element base class that stores configuration information about a matching rule.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchingRuleData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchingRuleData"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchingRuleData.#ctor(System.String,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchingRuleData"/> class.
            </summary>
            <param name="matchingRuleName">The name of the rule in config.</param>
            <param name="matchingRuleType">The underlying type of matching rule this object configures.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchingRuleData.ConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented matching rule.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="nameSuffix">The name suffix to use for the matching rule registration name.</param>
            <returns>The actual rule registration name.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchingRuleData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented matching rule by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.AssemblyMatchingRuleData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.AssemblyMatchingRuleData"/> class with default settings.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.AssemblyMatchingRuleData.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.AssemblyMatchingRuleData"/> class with the given
            rule name and assembly name pattern to match.
            </summary>
            <param name="matchingRuleName">The name of rule from the configuration file.</param>
            <param name="assemblyName">The assembly name to match.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.AssemblyMatchingRuleData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented matching rule by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.AssemblyMatchingRuleData.Match">
            <summary>
            The assembly name to match.
            </summary>
            <value>Assembly name to match.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CallHandlerData">
            <summary>
            Base class for configuration information stored about a call handler.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CallHandlerData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CallHandlerData"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CallHandlerData.#ctor(System.String,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CallHandlerData"/> class with the specified name and type.
            </summary>
            <param name="handlerName">The name of the handler entry.</param>
            <param name="handlerType">The type of handler to create.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CallHandlerData.#ctor(System.String,System.Type,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CallHandlerData"/> class with the specified name, type, and handler order.
            </summary>
            <param name="handlerName">The name of the handler entry.</param>
            <param name="handlerType">The type of handler to create.</param>
            <param name="order">The order in which the handler will be executed.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CallHandlerData.ConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures the specified container.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="nameSuffix">The name suffix to use for the handler registration name.</param>
            <returns>The actual handler registration name.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CallHandlerData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented call handler by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CallHandlerData.Order">
            <summary>
            Gets or sets the the order in which the handler will be executed.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomAttributeMatchingRuleData">
            <summary>
            Configuration element for the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.CustomAttributeMatchingRule"/> configuration.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomAttributeMatchingRuleData.#ctor">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomAttributeMatchingRuleData"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomAttributeMatchingRuleData.#ctor(System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomAttributeMatchingRuleData"/> instance.
            </summary>
            <param name="name">Name of the matching rule.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomAttributeMatchingRuleData.#ctor(System.String,System.Type,System.Boolean)">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomAttributeMatchingRuleData"/> instance.
            </summary>
            <param name="name">Name of the matching rule.</param>
            <param name="attributeType">Attribute to find on the target.</param>
            <param name="searchInheritanceChain">Should we search the inheritance chain to find the attribute?</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomAttributeMatchingRuleData.#ctor(System.String,System.String,System.Boolean)">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomAttributeMatchingRuleData"/> instance.
            </summary>
            <param name="name">Name of the matching rule.</param>
            <param name="attributeTypeName">Name of the attribute type to match on the target.</param>
            <param name="searchInheritanceChain">Should we search the inheritance chain to find the attribute?</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomAttributeMatchingRuleData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented matching rule by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomAttributeMatchingRuleData.SearchInheritanceChain">
            <summary>
            Should we search the inheritance chain to find the attribute?
            </summary>
            <value>The "searchInheritanceChain" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomAttributeMatchingRuleData.AttributeTypeName">
            <summary>
            Name of attribute type to match.
            </summary>
            <value>The "attributeType" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomAttributeMatchingRuleData.AttributeType">
            <summary>
            The underlying type object for the attribute we want to search for.
            </summary>
            <value>This wraps the AttributeTypeName property in a type converter.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData">
            <summary>
            A configuration element that allows you to configure arbitrary
            call handlers that don't otherwise have configuration support.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.#ctor(System.String,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData"/> class by using the specified handler name and type.
            </summary>
            <param name="name">The name of the handler instance.</param>
            <param name="type">The type of handler to configure.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData"/> class by using the specified handler name and type name.
            </summary>
            <param name="name">The name of the handler instance.</param>
            <param name="typeName">The name of the type of handler to configure.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.#ctor(System.String,System.String,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData"/> class by using the specified handler name, type name, and handler order.
            </summary>
            <param name="name">The name of the handler instance.</param>
            <param name="typeName">The name of the type of handler to configure.</param>
            <param name="handlerOrder">The order of the handler type to configure.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.SetAttributeValue(System.String,System.String)">
            <summary>
            Sets the attribute value for a key.
            </summary>
            <param name="key">The attribute name.</param>
            <param name="value">The attribute value.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.Unmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)">
            <summary>
            Modifies the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData"/> object to remove all values that should not be saved. 
            </summary>
            <param name="sourceElement">A <see cref="T:System.Configuration.ConfigurationElement"/> object at the current level that contains a merged view of the properties.</param>
            <param name="parentElement">A parent <see cref="T:System.Configuration.ConfigurationElement"/> object or <see langword="null"/> if this is the top level.</param>		
            <param name="saveMode">A <see cref="T:System.Configuration.ConfigurationSaveMode"/> that determines which property values to include.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.Reset(System.Configuration.ConfigurationElement)">
            <summary>
            Resets the internal state of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData"/> object, 
            including the locks and the properties collection.
            </summary>
            <param name="parentElement">The parent element.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.IsModified">
            <summary>
            Indicates whether this configuration element has been modified since it was last 
            saved or loaded when implemented in a derived class.
            </summary>
            <returns><see langword="true"/> if the element has been modified; otherwise, <see langword="false"/>. </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.OnDeserializeUnrecognizedAttribute(System.String,System.String)">
            <summary>
            Called when an unknown attribute is encountered while deserializing the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData"/> object.
            </summary>
            <param name="name">The name of the unrecognized attribute.</param>
            <param name="value">The value of the unrecognized attribute.</param>
            <returns><see langword="true"/> if the processing of the element should continue; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Common#Configuration#CustomCallHandlerData}#BaseGetPropertyValue(System.Configuration.ConfigurationProperty)">
            <summary>Invokes the inherited behavior.</summary>
            <param name="property">Gets the given property value.</param>
            <returns>the requested property's value</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Common#Configuration#CustomCallHandlerData}#BaseSetPropertyValue(System.Configuration.ConfigurationProperty,System.Object)">
            <summary>Invokes the inherited behavior.</summary>
            <param name="property">Sets the given property.</param>
            <param name="value">New value for the property.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Common#Configuration#CustomCallHandlerData}#BaseUnmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)">
            <summary>Invokes the inherited behavior.</summary>
            <param name="sourceElement">Source configuration element</param>
            <param name="parentElement">Parent configuration element</param>
            <param name="saveMode">ConfigurationSaveMode</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Common#Configuration#CustomCallHandlerData}#BaseReset(System.Configuration.ConfigurationElement)">
            <summary>Invokes the inherited behavior.</summary>
            <param name="parentElement">Parent element</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Common#Configuration#CustomCallHandlerData}#BaseIsModified">
            <summary>Invokes the inherited behavior.</summary>
            <returns>True if element has been modified, false if not.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented matching rule by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.TypeName">
            <summary>
            Overridden in order to apply the <see cref="T:System.ComponentModel.BrowsableAttribute"/> attribute.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.Attributes">
            <summary>
            Gets or sets custom configuration attributes.
            </summary>
            <value>A collection of attributes.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.Properties">
            <summary>
            Gets a <see cref="T:System.Configuration.ConfigurationPropertyCollection"/> of the properties that are defined for 
            this configuration element when implemented in a derived class. 
            </summary>
            <value>
            A <see cref="T:System.Configuration.ConfigurationPropertyCollection"/> of the properties that are defined for this
            configuration element when implemented in a derived class. 
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomCallHandlerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Common#Configuration#CustomCallHandlerData}#Helper">
            <summary>
            Gets the helper.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData">
            <summary>
            A configuration element that lets you configure matching rules
            that don't have any explicit configuration support.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData"/> class by using the specified matching rule name and type.
            </summary>
            <param name="name">The name of the matching rule instance.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.#ctor(System.String,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData"/> class by using the specified matching rule name and type.
            </summary>
            <param name="name">The name of the matching rule instance.</param>
            <param name="type">The type of matching rule to configure.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData"/> class by using the specified matching rule name and type name.
            </summary>
            <param name="name">The name of the matching rule instance.</param>
            <param name="typeName">The name of the type of matching rule to configure.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.SetAttributeValue(System.String,System.String)">
            <summary>
            Sets the attribute value for a key.
            </summary>
            <param name="key">The attribute name.</param>
            <param name="value">The attribute value.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.Unmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)">
            <summary>
            Modifies the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData"/> object to remove all values that should not be saved. 
            </summary>
            <param name="sourceElement">A <see cref="T:System.Configuration.ConfigurationElement"/> object at the current level containing a merged view of the properties.</param>
            <param name="parentElement">A parent <see cref="T:System.Configuration.ConfigurationElement"/> object or <see langword="null"/> if this is the top level.</param>		
            <param name="saveMode">One of the <see cref="T:System.Configuration.ConfigurationSaveMode"/> values.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.Reset(System.Configuration.ConfigurationElement)">
            <summary>
            Resets the internal state of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData"/> object, 
            including the locks and the properties collection.
            </summary>
            <param name="parentElement">The parent element.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.IsModified">
            <summary>
            Indicates whether this configuration element has been modified since it was last 
            saved or loaded when implemented in a derived class.
            </summary>
            <returns><see langword="true"/> if the element has been modified; otherwise, <see langword="false"/>. </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.OnDeserializeUnrecognizedAttribute(System.String,System.String)">
            <summary>
            Called when an unknown attribute is encountered while deserializing the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData"/> object.
            </summary>
            <param name="name">The name of the unrecognized attribute.</param>
            <param name="value">The value of the unrecognized attribute.</param>
            <returns><see langword="true"/> if the processing of the element should continue; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#PolicyInjection#Configuration#CustomMatchingRuleData}#BaseGetPropertyValue(System.Configuration.ConfigurationProperty)">
            <summary>Invokes the inherited behavior.</summary>
            <param name="property">The property to get.</param>
            <returns>The value of the requested property.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#PolicyInjection#Configuration#CustomMatchingRuleData}#BaseSetPropertyValue(System.Configuration.ConfigurationProperty,System.Object)">
            <summary>Invokes the inherited behavior.</summary>
            <param name="property">Property to set.</param>
            <param name="value">New value for property.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#PolicyInjection#Configuration#CustomMatchingRuleData}#BaseUnmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)">
            <summary>Invokes the inherited behavior.</summary>
            <param name="sourceElement">Source configuration element</param>
            <param name="parentElement">Parent configuration element</param>
            <param name="saveMode">ConfigurationSaveMode</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#PolicyInjection#Configuration#CustomMatchingRuleData}#BaseReset(System.Configuration.ConfigurationElement)">
            <summary>Invokes the inherited behavior.</summary>
            <param name="parentElement">Parent element</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#PolicyInjection#Configuration#CustomMatchingRuleData}#BaseIsModified">
            <summary>Invokes the inherited behavior.</summary>
            <returns>True if element has been modified, false if not.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented matching rule by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.TypeName">
            <summary>
            Overridden in order to apply <see cref="T:System.ComponentModel.BrowsableAttribute"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.Attributes">
            <summary>
            Gets or sets custom configuration attributes.
            </summary>
            <value>The attribute collection.</value>  
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.Properties">
            <summary>
            Gets a <see cref="T:System.Configuration.ConfigurationPropertyCollection"/> of the properties that are defined for 
            this configuration element when implemented in a derived class. 
            </summary>
            <value>
            A <see cref="T:System.Configuration.ConfigurationPropertyCollection"/> of the properties that are defined for this
            configuration element when implemented in a derived class. 
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.CustomMatchingRuleData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#PolicyInjection#Configuration#CustomMatchingRuleData}#Helper">
            <summary>
            Gets the helper.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.AddPolicyInjectionSettings">
            <summary>
              Looks up a localized string similar to Add Policy Injection Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.AssemblyMatchingRuleDataDescription">
            <summary>
              Looks up a localized string similar to A Matching Rule that selects target classes based on the assembly name or by specifying a reference to an assembly..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.AssemblyMatchingRuleDataDisplayName">
            <summary>
              Looks up a localized string similar to Assembly Matching Rule.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.AssemblyMatchingRuleDataMatchDescription">
             <summary>
               Looks up a localized string similar to The name of the assembly to match. 
            It can be the name and version; the name, version and culture; or the full assembly name of the assembly excluding the .dll file name extension. 
            It cannot include wildcard characters. .
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.AssemblyMatchingRuleDataMatchDisplayName">
            <summary>
              Looks up a localized string similar to Assembly Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.AssemblyMatchingRuleDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Assembly Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.AssemblyMatchingRuleDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.AssemblyMatchingRuleDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Assembly Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.AssemblyMatchingRuleDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CallHandlerDataOrderDescription">
             <summary>
               Looks up a localized string similar to The position of the handler within the policy handler chain, starting from 1. 
            The default value is zero, which means that there is no explicit order specified for 
            the handler in relation to other handlers in the same handler chain..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CallHandlerDataOrderDisplayName">
            <summary>
              Looks up a localized string similar to Order.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomAttributeMatchingRuleDataAttributeTypeNameDescription">
            <summary>
              Looks up a localized string similar to The type name of the custom attribute that is applied to members of the target object..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomAttributeMatchingRuleDataAttributeTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Attribute Type Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomAttributeMatchingRuleDataDescription">
            <summary>
              Looks up a localized string similar to A Matching Rule that selects target classes and class members based on a custom attribute type that is applied to class members..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomAttributeMatchingRuleDataDisplayName">
            <summary>
              Looks up a localized string similar to Custom Attribute Matching Rule.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomAttributeMatchingRuleDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Custom Attribute Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomAttributeMatchingRuleDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomAttributeMatchingRuleDataSearchInheritanceChainDescription">
            <summary>
              Looks up a localized string similar to Specifies whether the rule should also search base classes for members that carry the custom attribute..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomAttributeMatchingRuleDataSearchInheritanceChainDisplayName">
            <summary>
              Looks up a localized string similar to Search Inheritance Chain.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomAttributeMatchingRuleDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Custom Attribute Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomAttributeMatchingRuleDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomCallHandlerDataDescription">
            <summary>
              Looks up a localized string similar to A custom implementation of a Call Handler that is added to Enterprise Library..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomCallHandlerDataDisplayName">
            <summary>
              Looks up a localized string similar to Custom Call Handler.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomCallHandlerDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Custom Call Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomCallHandlerDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomMatchingRuleDataAddCommand">
            <summary>
              Looks up a localized string similar to Custom Matching Rule (using type picker).
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomMatchingRuleDataDescription">
            <summary>
              Looks up a localized string similar to A custom implementation of a Matching Rule that is added to Enterprise Library..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomMatchingRuleDataDisplayName">
            <summary>
              Looks up a localized string similar to Custom Matching Rule.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomMatchingRuleDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Custom Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomMatchingRuleDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomMatchingRuleDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Custom Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.CustomMatchingRuleDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ExceptionCallHandlerDataDescription">
             <summary>
               Looks up a localized string similar to A Call Handler that provides the capability to manage and process exceptions in a standard way. 
            This handler uses the Exception Handling Application Block to handle exceptions raised by the target object..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ExceptionCallHandlerDataDisplayName">
            <summary>
              Looks up a localized string similar to Exception Handling Call Handler.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ExceptionCallHandlerDataExceptionPolicyNameDescription">
            <summary>
              Looks up a localized string similar to The name of the exception handling policy to use, as configured in the Exception Handling Application Block..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ExceptionCallHandlerDataExceptionPolicyNameDisplayName">
            <summary>
              Looks up a localized string similar to Exception Policy Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ExceptionCallHandlerDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Exception Handling Call Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ExceptionCallHandlerDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ExceptionCallHandlerDataOrderDescription">
             <summary>
               Looks up a localized string similar to The position of the handler within the policy handler chain, starting from 1. 
            The default value is zero, which means that there is no explicit order specified for the handler in relation to other 
            handlers in the same handler chain..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ExceptionCallHandlerDataOrderDisplayName">
            <summary>
              Looks up a localized string similar to Order.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ExceptionCallHandlerDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Exception Handling Call Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ExceptionCallHandlerDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerCategoryEntryDescription">
            <summary>
              Looks up a localized string similar to A single Category in the list of Categories for the log message..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerCategoryEntryDisplayName">
            <summary>
              Looks up a localized string similar to Category Entry.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerCategoryEntryNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Category Entry..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerCategoryEntryNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataAfterMessageDescription">
            <summary>
              Looks up a localized string similar to The message that the Logging Handler will add to the log entry after the target method executes..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataAfterMessageDisplayName">
            <summary>
              Looks up a localized string similar to After Message.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataBeforeMessageDescription">
            <summary>
              Looks up a localized string similar to The message that the Logging Handler will add to the log entry before the target method executes..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataBeforeMessageDisplayName">
            <summary>
              Looks up a localized string similar to Before Message.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataCategoriesDescription">
             <summary>
               Looks up a localized string similar to The list of categories to which the Logging Handler will write events. 
            Each category can be a literal value, and/or include the tokens {method}, {type}, {namespace}, and {assembly}..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataCategoriesDisplayName">
            <summary>
              Looks up a localized string similar to Categories.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataDescription">
            <summary>
              Looks up a localized string similar to A Call Handler that provides the capability to write log messages and trace messages as the client code invokes the selected method or accesses the selected property of the target object. This handler uses the Logging Application Block to create and write log messages..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataDisplayName">
            <summary>
              Looks up a localized string similar to Logging Call Handler.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataEventIdDescription">
            <summary>
              Looks up a localized string similar to The ID of the event to include in the log message..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataEventIdDisplayName">
            <summary>
              Looks up a localized string similar to Event Id.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataIncludeCallStackDescription">
            <summary>
              Looks up a localized string similar to Determines whether the call stack will be included in the log message. .
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataIncludeCallStackDisplayName">
            <summary>
              Looks up a localized string similar to Include Call Stack.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataIncludeCallTimeDescription">
            <summary>
              Looks up a localized string similar to Determines whether the duration of the call will be included in After Message section of the log message. .
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataIncludeCallTimeDisplayName">
            <summary>
              Looks up a localized string similar to Include Call Time.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataIncludeParameterValuesDescription">
            <summary>
              Looks up a localized string similar to Determines whether the values of the parameters passed to the target method will be included in the log message. .
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataIncludeParameterValuesDisplayName">
            <summary>
              Looks up a localized string similar to Include Parameter Values.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataLogBehaviorDescription">
             <summary>
               Looks up a localized string similar to Determines if the handler will write log messages before, after, or both before and after the call to the target object. 
            A value from the HandlerLogBehavior enumeration. 
            Valid values are Before, After, and BeforeAndAfter..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataLogBehaviorDisplayName">
            <summary>
              Looks up a localized string similar to Log Behavior.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Logging Call Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataOrderDescription">
             <summary>
               Looks up a localized string similar to The position of the handler within the policy handler chain, starting from 1. 
            The default value is zero, which means that there is no explicit order specified for the handler in relation to other handlers in the same handler chain..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataOrderDisplayName">
            <summary>
              Looks up a localized string similar to Order.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataPriorityDescription">
            <summary>
              Looks up a localized string similar to The priority value of the exception to include in the log message..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataPriorityDisplayName">
            <summary>
              Looks up a localized string similar to Priority.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataSeverityDescription">
            <summary>
              Looks up a localized string similar to The severity value of the exception to include in the log message; using values from the TraceEventType enumeration such as Critical, Error, and Warning..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataSeverityDisplayName">
            <summary>
              Looks up a localized string similar to Severity.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Logging Call Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.LogCallHandlerDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MatchDataCollectionDescription">
            <summary>
              Looks up a localized string similar to A collection of matches for a Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MatchDataCollectionDisplayName">
            <summary>
              Looks up a localized string similar to Match Data Collection.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MatchDataDescription">
            <summary>
              Looks up a localized string similar to A class that holds information about a single match for a Matching Rule, such as the string to match and whether the match is case-sensitive..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MatchDataDisplayName">
            <summary>
              Looks up a localized string similar to Match.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MatchDataIgnoreCaseDescription">
             <summary>
               Looks up a localized string similar to Specifies whether the match should be carried out on a case-sensitive basis. 
            The default is false..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MatchDataIgnoreCaseDisplayName">
            <summary>
              Looks up a localized string similar to Ignore Case.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MatchDataMatchDescription">
            <summary>
              Looks up a localized string similar to The string value the Matching Rule will compare to class, member, or parameter names..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MatchDataMatchDisplayName">
            <summary>
              Looks up a localized string similar to Match.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MatchingRuleDataDescription">
            <summary>
              Looks up a localized string similar to A base class for all Matching Rules..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MatchingRuleDataDisplayName">
            <summary>
              Looks up a localized string similar to Matching Rule.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MatchingRuleDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MatchingRuleDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MatchingRuleDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MatchingRuleDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MemberNameMatchingRuleDataDescription">
             <summary>
               Looks up a localized string similar to A Matching Rule that selects target classes and class members based on the names of the class members (methods or properties). 
            Wildcard characters can be used for the member name..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MemberNameMatchingRuleDataDisplayName">
            <summary>
              Looks up a localized string similar to Member Name Matching Rule.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MemberNameMatchingRuleDataMatchesDescription">
             <summary>
               Looks up a localized string similar to The collection of method and property names to match. 
            Wildcard characters can be included..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MemberNameMatchingRuleDataMatchesDisplayName">
            <summary>
              Looks up a localized string similar to Member Names.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MemberNameMatchingRuleDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Member Name Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MemberNameMatchingRuleDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MemberNameMatchingRuleDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Member Name Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MemberNameMatchingRuleDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MethodSignatureMatchingRuleDataDescription">
             <summary>
               Looks up a localized string similar to A Matching Rule that selects target classes and class members based on the name and signature (the list of parameter types) of its members. 
            This rule allows the use of wildcard characters for the member names..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MethodSignatureMatchingRuleDataDisplayName">
            <summary>
              Looks up a localized string similar to Method Signature Matching Rule.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MethodSignatureMatchingRuleDataIgnoreCaseDescription">
            <summary>
              Looks up a localized string similar to Specifies whether the match should be carried out on a case-sensitive basis. The default is false..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MethodSignatureMatchingRuleDataIgnoreCaseDisplayName">
            <summary>
              Looks up a localized string similar to Ignore Case.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MethodSignatureMatchingRuleDataMatchDescription">
            <summary>
              Looks up a localized string similar to The name of the method to match. Wildcard characters can be included..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MethodSignatureMatchingRuleDataMatchDisplayName">
            <summary>
              Looks up a localized string similar to Method Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MethodSignatureMatchingRuleDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Method Signature Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MethodSignatureMatchingRuleDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MethodSignatureMatchingRuleDataParametersDescription">
            <summary>
              Looks up a localized string similar to The collection of parameter type names (not the parameter names) that make up the matching method signature..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MethodSignatureMatchingRuleDataParametersDisplayName">
            <summary>
              Looks up a localized string similar to Parameters.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MethodSignatureMatchingRuleDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Method Signature Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.MethodSignatureMatchingRuleDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.NamespaceMatchingRuleDataDescription">
            <summary>
              Looks up a localized string similar to A Matching Rule that selects target classes and class members based on their namespace name, using wildcard characters for the child namespace names but not for the root namespace name..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.NamespaceMatchingRuleDataDisplayName">
            <summary>
              Looks up a localized string similar to Namespace Matching Rule.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.NamespaceMatchingRuleDataMatchesDescription">
             <summary>
               Looks up a localized string similar to The collection of namespaces to match. 
            Wildcard characters can be used for the child namespace names but not for the root namespace name..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.NamespaceMatchingRuleDataMatchesDisplayName">
            <summary>
              Looks up a localized string similar to Namespaces.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.NamespaceMatchingRuleDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Namespace Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.NamespaceMatchingRuleDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.NamespaceMatchingRuleDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Namespace Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.NamespaceMatchingRuleDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeElementDataCollectionDescription">
             <summary>
               Looks up a localized string similar to The collection of parameter types to match. 
            The type name can be a fully qualified type name or just the class name. Each match also specifies whether the match
            is case-sensitive, and the parameter kind (which must be one of Input, Output, InputOrOutput, and ReturnValue)..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeElementDataCollectionDisplayName">
            <summary>
              Looks up a localized string similar to Parameter Type Element Data Collection.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeElementDescription">
             <summary>
               Looks up a localized string similar to The type name, case-sensitivity, and usage for an individual parameter type to match. 
            The type name can be a fully qualified type name or just the class name..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeElementDisplayName">
            <summary>
              Looks up a localized string similar to Parameter Type Element.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeElementNameDescription">
             <summary>
               Looks up a localized string similar to A unique ID for this parameter. 
            This name does not need to match the corresponding parameter in the target types; only the type is used..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeElementNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeElementParameterTypeNameDescription">
            <summary>
              Looks up a localized string similar to An individual parameter type to match. The type name can be a fully qualified type name or just the class name..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeElementParameterTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Parameter Type Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeMatchDataDescription">
             <summary>
               Looks up a localized string similar to The type name, case-sensitivity, and usage for an individual parameter type to match. 
            The type name can be a fully qualified type name or just the class name..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeMatchDataDisplayName">
            <summary>
              Looks up a localized string similar to Parameter Type Match.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeMatchDataIgnoreCaseDescription">
            <summary>
              Looks up a localized string similar to Specifies whether the match should be carried out on a case-sensitive basis. The default is false..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeMatchDataIgnoreCaseDisplayName">
            <summary>
              Looks up a localized string similar to Ignore Case.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeMatchDataMatchDescription">
            <summary>
              Looks up a localized string similar to An individual parameter type to match. The type name can be a fully qualified type name or just the class name..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeMatchDataMatchDisplayName">
            <summary>
              Looks up a localized string similar to Parameter Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeMatchDataParameterKindDescription">
             <summary>
               Looks up a localized string similar to The usage of the parameter as a value from the ParameterKind enumeration. 
            Valid values are Input, Output, InputOrOutput, and ReturnValue..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeMatchDataParameterKindDisplayName">
            <summary>
              Looks up a localized string similar to Parameter Kind.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeMatchingRuleDataDescription">
            <summary>
              Looks up a localized string similar to A Matching Rule that selects target classes and class members based on the type name of a parameter for a member of the target object..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeMatchingRuleDataDisplayName">
            <summary>
              Looks up a localized string similar to Parameter Type Matching Rule.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeMatchingRuleDataMatchesDescription">
             <summary>
               Looks up a localized string similar to The collection of parameter types to match. 
            The type name can be a fully qualified type name or just the class name. Each match also specifies whether the match
            is case-sensitive, and the parameter kind (which must be one of Input, Output, InputOrOutput, and ReturnValue)..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeMatchingRuleDataMatchesDisplayName">
            <summary>
              Looks up a localized string similar to Parameter Types.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeMatchingRuleDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Parameter Type Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeMatchingRuleDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeMatchingRuleDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Parameter Type Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ParameterTypeMatchingRuleDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataCategoryNameDescription">
            <summary>
              Looks up a localized string similar to The name of the category of the target performance counter..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataCategoryNameDisplayName">
            <summary>
              Looks up a localized string similar to Category Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataDescription">
             <summary>
               Looks up a localized string similar to A Call Handler that increments a specific counter each time it executes in response to invocation of the selected method or setting of the selected property. 
            This handler uses the instrumentation features that are part of the Enterprise Library Core. .
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataDisplayName">
            <summary>
              Looks up a localized string similar to Performance Counter Call Handler.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataIncrementAverageCallDurationDescription">
            <summary>
              Looks up a localized string similar to Whether to increment an &apos;Average duration of each call&apos; counter each time..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataIncrementAverageCallDurationDisplayName">
            <summary>
              Looks up a localized string similar to Increment Average Call Duration.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataIncrementCallsPerSecondDescription">
            <summary>
              Looks up a localized string similar to Whether to increment a &apos;Number of calls per second&apos; counter each time..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataIncrementCallsPerSecondDisplayName">
            <summary>
              Looks up a localized string similar to Increment Calls Per Second.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataIncrementExceptionsPerSecondDescription">
            <summary>
              Looks up a localized string similar to Whether to increment a &apos;Number of exceptions per second&apos; counter each time..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataIncrementExceptionsPerSecondDisplayName">
            <summary>
              Looks up a localized string similar to Increment Exceptions Per Second.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataIncrementNumberOfCallsDescription">
            <summary>
              Looks up a localized string similar to Whether to increment a &apos;Total number of calls&apos; counter each time..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataIncrementNumberOfCallsDisplayName">
            <summary>
              Looks up a localized string similar to Increment Number Of Calls.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataIncrementTotalExceptionsDescription">
            <summary>
              Looks up a localized string similar to Whether to increment a &apos;Total number of exceptions&apos; counter each time..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataIncrementTotalExceptionsDisplayName">
            <summary>
              Looks up a localized string similar to Increment Total Exceptions.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataInstanceNameDescription">
             <summary>
               Looks up a localized string similar to The name of the instance of the target performance counter. 
            Can include the tokens {method}, {type}, {namespace}, {assembly}, and {appdomain}..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataInstanceNameDisplayName">
            <summary>
              Looks up a localized string similar to Instance Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Performance Counter Call Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataOrderDescription">
             <summary>
               Looks up a localized string similar to The position of the handler within the policy handler chain, starting from 1. 
            The default value is zero, which means that there is no explicit order specified for the handler in relation to other handlers in the same handler chain..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataOrderDisplayName">
            <summary>
              Looks up a localized string similar to Order.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Performance Counter Call Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataUseTotalCounterDescription">
            <summary>
              Looks up a localized string similar to Whether to increment a &apos;Total&apos; counter each time..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PerformanceCounterCallHandlerDataUseTotalCounterDisplayName">
            <summary>
              Looks up a localized string similar to Use Total Counter.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PolicyDataDescription">
            <summary>
              Looks up a localized string similar to A set of Matching Rules that select types and their members, and a set of Call Handlers that will execute when calls are made to these types and their members..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PolicyDataDisplayName">
            <summary>
              Looks up a localized string similar to Policy.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PolicyDataHandlersDescription">
            <summary>
              Looks up a localized string similar to The set of Call Handlers for this Policy..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PolicyDataHandlersDisplayName">
            <summary>
              Looks up a localized string similar to Handlers.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PolicyDataMatchingRulesDescription">
            <summary>
              Looks up a localized string similar to The set of Matching Rules for this Policy..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PolicyDataMatchingRulesDisplayName">
            <summary>
              Looks up a localized string similar to Matching Rules.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PolicyDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Policy..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PolicyDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PolicyInjectionSettingsDescription">
            <summary>
              Looks up a localized string similar to Configuration settings for the Policy Injection Application Block..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PolicyInjectionSettingsDisplayName">
            <summary>
              Looks up a localized string similar to Policy Injection Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PolicyInjectionSettingsPoliciesDescription">
            <summary>
              Looks up a localized string similar to The set of Policies configured for the Policy Injection Application Block..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PolicyInjectionSettingsPoliciesDisplayName">
            <summary>
              Looks up a localized string similar to Policies.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PropertyMatchDataDescription">
             <summary>
               Looks up a localized string similar to A class that contains details of a property to match. 
            It includes the property name, whether the match is case-sensitive, and a value from the PropertyMatchingOption enumeration that indicates 
            if the rule should match on the Get, Set, or both the accessors for a selected parameter. 
            Valid values for this are Get, Set, and GetOrSet..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PropertyMatchDataDisplayName">
            <summary>
              Looks up a localized string similar to Property Match.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PropertyMatchDataIgnoreCaseDescription">
             <summary>
               Looks up a localized string similar to Specifies whether the match should be carried out on a case-sensitive basis. 
            The default is false..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PropertyMatchDataIgnoreCaseDisplayName">
            <summary>
              Looks up a localized string similar to Ignore Case.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PropertyMatchDataMatchDescription">
             <summary>
               Looks up a localized string similar to The name of a property to match. It can include or consist of the * or ? wildcard characters to select 
            multiple properties, and may use square brackets [ ] to specify a range of characters..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PropertyMatchDataMatchDisplayName">
            <summary>
              Looks up a localized string similar to Property Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PropertyMatchDataMatchOptionDescription">
             <summary>
               Looks up a localized string similar to A value from the PropertyMatchingOption enumeration that indicates if the rule should match on the 
            Get, Set, or both the accessors for a selected parameter. Valid values are Get, Set, and GetOrSet..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PropertyMatchDataMatchOptionDisplayName">
            <summary>
              Looks up a localized string similar to Match Option.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PropertyMatchingRuleDataDescription">
             <summary>
               Looks up a localized string similar to A Matching Rule that selects target classes and class members based on their name, 
            including using wildcard characters, and the combination of accessors they implement..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PropertyMatchingRuleDataDisplayName">
            <summary>
              Looks up a localized string similar to Property Matching Rule.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PropertyMatchingRuleDataMatchesDescription">
            <summary>
              Looks up a localized string similar to The collection of match details containing the property names, case-sensitivity, and accessor details to match..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PropertyMatchingRuleDataMatchesDisplayName">
            <summary>
              Looks up a localized string similar to Properties.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PropertyMatchingRuleDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Property Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PropertyMatchingRuleDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PropertyMatchingRuleDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Property Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.PropertyMatchingRuleDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ReturnTypeMatchingRuleDataDescription">
            <summary>
              Looks up a localized string similar to A Matching Rule that selects target classes and class members based on the type of the return value..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ReturnTypeMatchingRuleDataDisplayName">
            <summary>
              Looks up a localized string similar to Return Type Matching Rule.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ReturnTypeMatchingRuleDataIgnoreCaseDescription">
            <summary>
              Looks up a localized string similar to Specifies whether the match should be carried out on a case-sensitive basis. The default is false..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ReturnTypeMatchingRuleDataIgnoreCaseDisplayName">
            <summary>
              Looks up a localized string similar to Ignore Case.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ReturnTypeMatchingRuleDataMatchDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the type of the return value of the method to match, or just the type name. .
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ReturnTypeMatchingRuleDataMatchDisplayName">
            <summary>
              Looks up a localized string similar to Return Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ReturnTypeMatchingRuleDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Return Type Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ReturnTypeMatchingRuleDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ReturnTypeMatchingRuleDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Return Type Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ReturnTypeMatchingRuleDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.StringBasedMatchingRuleDataDescription">
            <summary>
              Looks up a localized string similar to A base class for Matching Rules that selects target classes and class members using string matches..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.StringBasedMatchingRuleDataDisplayName">
            <summary>
              Looks up a localized string similar to String Based Matching Rule.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.StringBasedMatchingRuleDataIgnoreCaseDescription">
            <summary>
              Looks up a localized string similar to Specifies whether the match should be carried out on a case-sensitive basis. The default is false..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.StringBasedMatchingRuleDataIgnoreCaseDisplayName">
            <summary>
              Looks up a localized string similar to Ignore Case.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.StringBasedMatchingRuleDataMatchDescription">
            <summary>
              Looks up a localized string similar to The string to match..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.StringBasedMatchingRuleDataMatchDisplayName">
            <summary>
              Looks up a localized string similar to Match.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.StringBasedMatchingRuleDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the String Based Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.StringBasedMatchingRuleDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.StringBasedMatchingRuleDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the String Based Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.StringBasedMatchingRuleDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TagAttributeMatchingRuleDataDescription">
             <summary>
               Looks up a localized string similar to A Matching Rule that selects target classes and class members based on the name of an attribute of 
            type Tag that is applied to a class, or to members (methods or properties) within a class..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TagAttributeMatchingRuleDataDisplayName">
            <summary>
              Looks up a localized string similar to Tag Attribute Matching Rule.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TagAttributeMatchingRuleDataIgnoreCaseDescription">
            <summary>
              Looks up a localized string similar to Specifies whether the match should be carried out on a case-sensitive basis. The default is false..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TagAttributeMatchingRuleDataIgnoreCaseDisplayName">
            <summary>
              Looks up a localized string similar to Ignore Case.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TagAttributeMatchingRuleDataMatchDescription">
            <summary>
              Looks up a localized string similar to The name of the Tag attribute applied to the target object. It cannot include wildcard characters..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TagAttributeMatchingRuleDataMatchDisplayName">
            <summary>
              Looks up a localized string similar to Tag Attribute Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TagAttributeMatchingRuleDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Tag Attribute Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TagAttributeMatchingRuleDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TagAttributeMatchingRuleDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Tag Attribute Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TagAttributeMatchingRuleDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TypeMatchingRuleDataDescription">
            <summary>
              Looks up a localized string similar to A Matching Rule that selects target classes and class members based on the namespace and class name of the target type..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TypeMatchingRuleDataDisplayName">
            <summary>
              Looks up a localized string similar to Type Matching Rule.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TypeMatchingRuleDataMatchesDescription">
             <summary>
               Looks up a localized string similar to The collection of matches for types, including the type name and the case sensitivity. 
            The type name may include the full namespace and class name of the target object, or just the class name..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TypeMatchingRuleDataMatchesDisplayName">
            <summary>
              Looks up a localized string similar to Type Matches.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TypeMatchingRuleDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Type Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TypeMatchingRuleDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TypeMatchingRuleDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Type Matching Rule..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.TypeMatchingRuleDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ValidationCallHandlerDataDescription">
             <summary>
               Looks up a localized string similar to A Call Handler that provides the capability to test whether the value provided for the selected property, or the values specified for the parameters of the selected method, are valid against specific rules. 
            This handler uses the Validation Application Block to perform the validation..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ValidationCallHandlerDataDisplayName">
            <summary>
              Looks up a localized string similar to Validation Call Handler.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ValidationCallHandlerDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Validation Call Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ValidationCallHandlerDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ValidationCallHandlerDataOrderDescription">
             <summary>
               Looks up a localized string similar to The position of the handler within the policy handler chain, starting from 1. 
            The default value is zero, which means that there is no explicit order specified for the handler in relation to other handlers in the same handler chain..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ValidationCallHandlerDataOrderDisplayName">
            <summary>
              Looks up a localized string similar to Order.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ValidationCallHandlerDataRuleSetDescription">
             <summary>
               Looks up a localized string similar to The name of the rule set to use for all target object types, as configured in the Validation Application Block. 
            An empty string causes the handler to use the default rule set..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ValidationCallHandlerDataRuleSetDisplayName">
            <summary>
              Looks up a localized string similar to Rule Set.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ValidationCallHandlerDataSpecificationSourceDescription">
             <summary>
               Looks up a localized string similar to A value from the SpecificationSource enumeration that defines the locations where the handler will look for validation rules. 
            Valid values are Attributes, Configuration, ParameterAttributesOnly, and Both..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ValidationCallHandlerDataSpecificationSourceDisplayName">
            <summary>
              Looks up a localized string similar to Rule Source.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ValidationCallHandlerDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Validation Call Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.DesignResources.ValidationCallHandlerDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionCallHandlerData">
            <summary>
            Configuration element storing configuration information for the
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandler"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionCallHandlerData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionCallHandlerData"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionCallHandlerData.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionCallHandlerData"/> class by using the specified handler name.
            </summary>
            <param name="handlerName">The name of the handler.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionCallHandlerData.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionCallHandlerData"/> class by using the specified handler and exception policy name.
            </summary>
            <param name="handlerName">The name of the handler.</param>
            <param name="exceptionPolicyName">Exception policy name to use in handler.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionCallHandlerData.#ctor(System.String,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionCallHandlerData"/> class by using the specified handler name and order.
            </summary>
            <param name="handlerName">The name of the handler.</param>
            <param name="handlerOrder">The order to use in handler.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionCallHandlerData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented call handler by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionCallHandlerData.ExceptionPolicyName">
            <summary>
            Gets the exception policy name as defined in the configuration for the Exception Handling Application Block.
            </summary>
            <value>The "exceptionPolicyName" attribute in the configuration.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerCategoryEntry">
            <summary>
            A configuration element that handles the entries for the &lt;categories&gt; element
            for the log call handler.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerCategoryEntry.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerCategoryEntry"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerCategoryEntry.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerCategoryEntry"/> class with the given
            category string.
            </summary>
            <param name="name">The category string.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerCategoryEntry.Name">
            <summary>
            Gets or sets the name of the element.
            </summary>
            <value>
            The name of the element.
            </value>
            <remarks>
            Overridden in order to annotate with design-time attribute.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData">
            <summary>
            A configuration element for the data for the LogCallHandler.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData"/> class with the specified name.
            </summary>
            <param name="handlerName">The handler name</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData.#ctor(System.String,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData"/> class with the specified handler name and order.
            </summary>
            <param name="handlerName">The handler name</param>
            <param name="handlerOrder">The handler order</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented call handler by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData.LogBehavior">
            <summary>
            Should the handler log before the call, after the call, or both?
            </summary>
            <value>The "logBehavior" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData.Categories">
            <summary>
            Collection of log categories to use in the log message.
            </summary>
            <value>The "categories" nested element.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData.BeforeMessage">
            <summary>
            Message for the log entry in before-call logging.
            </summary>
            <value>The "beforeMessage" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData.AfterMessage">
            <summary>
            Message for the log entry in after-call logging.
            </summary>
            <value>The "afterMessage" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData.EventId">
            <summary>
            Event Id to put into log entries.
            </summary>
            <value>The "eventId" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData.IncludeParameterValues">
            <summary>
            Include parameter values and return values in the log entry
            </summary>
            <value>The "includeParameterValues" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData.IncludeCallStack">
            <summary>
            Include the call stack in the log entries.
            </summary>
            <remarks>Setting this to true requires UnmanagedCode permissions.</remarks>
            <value>The "includeCallStack" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData.IncludeCallTime">
            <summary>
            Include the time to execute the call in the log entries.
            </summary>
            <value>The "includeCallTime" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData.Priority">
            <summary>
            The priority of the log entries.
            </summary>
            <value>the "priority" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.LogCallHandlerData.Severity">
            <summary>
            The severity of the log entry.
            </summary>
            <value>the "severity" config attribute.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.HandlerLogBehavior">
            <summary>
            Specifies when the logging call handler will add log entries.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.HandlerLogBehavior.BeforeAndAfter">
            <summary>
            Log both before and after the call.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.HandlerLogBehavior.Before">
            <summary>
            Log only before the call.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.HandlerLogBehavior.After">
            <summary>
            Log only after the call.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData">
            <summary>
            A <see cref="T:System.Configuration.ConfigurationElement"/> that stores information about a single
            matchable item. Specifically, the string to match, and whether it is case
            sensitive or not.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData.#ctor">
            <summary>
            Constructs an empty <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData.#ctor(System.String)">
            <summary>
            Constructs a <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData"/> with the given matching string.
            </summary>
            <param name="match">String to match.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData.#ctor(System.String,System.Boolean)">
            <summary>
            Constructs a <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData"/> with the given matching string and case-sensitivity flag.
            </summary>
            <param name="match">String to match.</param>
            <param name="ignoreCase">true to do case insensitive comparison, false to do case sensitive.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData.Match">
            <summary>
            Gets or sets the string to match against.
            </summary>
            <value>The "match" attribute value out of the configuration file.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData.IgnoreCase">
            <summary>
            Gets or sets the case sensitivity flag.
            </summary>
            <value>The "ignoreCase" attribute value out of the configuration file.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchDataCollection`1">
            <summary>
            A <see cref="T:System.Configuration.ConfigurationElementCollection"/> storing <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData"/> elements,
            or elements derived from <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData"/>.
            </summary>
            <typeparam name="T">Type of element contained in the collection. Must be <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData"/> or derived from <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData"/>.</typeparam>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchDataCollection`1.CreateNewElement">
            <summary>
            Creates a new empty item to store into the collection.
            </summary>
            <returns>The created object.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchDataCollection`1.GetElementKey(System.Configuration.ConfigurationElement)">
            <summary>
            Gets the key value from the stored element.
            </summary>
            <param name="element">Element to retrieve key from.</param>
            <returns>The value of the "match" property.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchDataCollection`1.Add(`0)">
            <summary>
            Adds the given element to the collection.
            </summary>
            <param name="match">Element to add.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchDataCollection`1.RemoveAt(System.Int32)">
            <summary>
            Removes the element at the given index.
            </summary>
            <param name="index">Index to remove from.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchDataCollection`1.Remove(System.String)">
            <summary>
            Removes the match from the element with the given name.
            </summary>
            <param name="match">Match string to remove.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchDataCollection`1.Clear">
            <summary>
            Removes all items from the collection.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchDataCollection`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
            <summary>
            Gets an <see cref="T:System.Collections.Generic.IEnumerator`1"/> to do a foreach over
            the contents of the collection.
            </summary>
            <returns>The enumerator object.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchDataCollection`1.Item(System.Int32)">
            <summary>
            Gets or sets the item at the given index.
            </summary>
            <param name="index">Index to get/set item from.</param>
            <returns>Item at index.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MemberNameMatchingRuleData">
            <summary>
            A configuration element that supports the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MemberNameMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MemberNameMatchingRuleData.#ctor">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MemberNameMatchingRuleData"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MemberNameMatchingRuleData.#ctor(System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MemberNameMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Matching rule name in configuration file.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MemberNameMatchingRuleData.#ctor(System.String,System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MemberNameMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Matching rule name in configuration file.</param>
            <param name="match">Member name pattern to match.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MemberNameMatchingRuleData.#ctor(System.String,System.Collections.Generic.IEnumerable{Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData})">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MemberNameMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Matching rule name in configuration file.</param>
            <param name="matches">Collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData"/> containing the patterns
            to match. If any pattern matches, the rule matches.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MemberNameMatchingRuleData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented matching rule by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MemberNameMatchingRuleData.Matches">
            <summary>
            The collection of patterns to match.
            </summary>
            <value>The "matches" child element in config.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MethodSignatureMatchingRuleData">
            <summary>
            Configuration element that stores the configuration information for an instance
            of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MethodSignatureMatchingRule"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.StringBasedMatchingRuleData">
            <summary>
            Base class for matching rule configuration data for those rules that take
            a single match instance consisting of a string to match and an
            ignore case flag.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.StringBasedMatchingRuleData.#ctor">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.StringBasedMatchingRuleData"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.StringBasedMatchingRuleData.#ctor(System.String,System.String,System.Type)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.StringBasedMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Name of the matching rule</param>
            <param name="matches">String to match.</param>
            <param name="matchingRuleType">Type of the underlying matching rule.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.StringBasedMatchingRuleData.Match">
            <summary>
            The string to match.
            </summary>
            <value>The "match" configuration attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.StringBasedMatchingRuleData.IgnoreCase">
            <summary>
            Should comparisons be case sensitive?
            </summary>
            <value>The "ignoreCase" configuration attribute. If false, comparison is
            case sensitive. If true, comparison is case insensitive.</value>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MethodSignatureMatchingRuleData.#ctor">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MethodSignatureMatchingRuleData"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MethodSignatureMatchingRuleData.#ctor(System.String,System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MethodSignatureMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Name of matching rule in config.</param>
            <param name="memberName">Method name pattern to match.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MethodSignatureMatchingRuleData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented matching rule by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MethodSignatureMatchingRuleData.Parameters">
            <summary>
            The collection of parameters that make up the matching method signature.
            </summary>
            <value>The "parameters" child element.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElementDataCollection">
            <summary>
            A configuration element that stores a collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElement"/> objects.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElementDataCollection.CreateNewElement">
            <summary>
            Creates a new element to store in the collection.
            </summary>
            <returns>The new element.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElementDataCollection.GetElementKey(System.Configuration.ConfigurationElement)">
            <summary>
            Gets the element key from the element.
            </summary>
            <param name="element">Element to retrieve key from.</param>
            <returns>The key.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElementDataCollection.Add(Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElement)">
            <summary>
            Adds a <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElement"/> to the collection.
            </summary>
            <param name="parameterTypeElement">The element to add.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElementDataCollection.Clear">
            <summary>
            Removes all items from the collection.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElementDataCollection.Get(System.Int32)">
            <summary>
            Gets the element at the given index.
            </summary>
            <param name="index">Index of desired element.</param>
            <returns>The element at that index.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElementDataCollection.RemoveAt(System.Int32)">
            <summary>
            Removes the specified element from the collection.
            </summary>
            <param name="index">Index of element to remove.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElement">
            <summary>
            A configuration element representing a single parameter in a method signature.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElement.#ctor(System.String,System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElement"/> instance.
            </summary>
            <param name="name">unique identifier for this parameter. The name does
            NOT need to match the target's parameter name.</param>
            <param name="parameterType">Expected type of parameter</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElement.#ctor">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElement"/> instance.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElement.Name">
            <summary>
            A unique ID for this parameter. This name does not need to match
            the corresponding parameter in the target types; only the type is
            used.
            </summary>
            <value>A name for this property that is unique in this rule's configuration.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeElement.ParameterTypeName">
            <summary>
            The parameter type required.
            </summary>
            <value>The "typeName" config attribute.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.NamespaceMatchingRuleData">
            <summary>
            Configuration element that stores the configuration information for an instance
            of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.NamespaceMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.NamespaceMatchingRuleData.#ctor">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.NamespaceMatchingRuleData"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.NamespaceMatchingRuleData.#ctor(System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.NamespaceMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Matching rule name in configuration file.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.NamespaceMatchingRuleData.#ctor(System.String,System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.NamespaceMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Matching rule name in configuration file.</param>
            <param name="namespaceName">Namespace pattern to match.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.NamespaceMatchingRuleData.#ctor(System.String,System.Collections.Generic.IEnumerable{Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData})">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.NamespaceMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Matching rule name in configuration file.</param>
            <param name="matches">Collection of namespace patterns to match. If any
            of the patterns match then the rule matches.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.NamespaceMatchingRuleData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented matching rule by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.NamespaceMatchingRuleData.Matches">
            <summary>
            The collection of match data containing patterns to match.
            </summary>
            <value>The "matches" child element.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchingRuleData">
            <summary>
            A configuration element storing configuration information for an instance of
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchingRuleData.#ctor">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchingRuleData"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchingRuleData.#ctor(System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Matching rule instance name in configuration.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchingRuleData.#ctor(System.String,System.Collections.Generic.IEnumerable{Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchData})">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Matching rule instance name in configuration.</param>
            <param name="matches">Collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchData"/> to match against.
            If any of them match, the rule matches.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchingRuleData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented matching rule by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchingRuleData.Matches">
            <summary>
            The collection of parameter types to match against.
            </summary>
            <value>The "matches" subelement.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchData">
            <summary>
            An extended <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData"/> class that also includes the
            <see cref="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchData.ParameterKind"/> of the parameter to match.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchData.#ctor">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchData"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchData.#ctor(System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchData"/> instance.
            </summary>
            <param name="match">Parameter type to match. Kind is InputOrOutput.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchData.#ctor(System.String,Microsoft.Practices.Unity.InterceptionExtension.ParameterKind)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchData"/> instance.
            </summary>
            <param name="match">Parameter type to match.</param>
            <param name="kind"><see cref="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchData.ParameterKind"/> to match.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchData.#ctor(System.String,Microsoft.Practices.Unity.InterceptionExtension.ParameterKind,System.Boolean)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchData"/> instance.
            </summary>
            <param name="match">Parameter type to match.</param>
            <param name="kind"><see cref="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchData.ParameterKind"/> to match.</param>
            <param name="ignoreCase">If false, type name comparisons are case sensitive. If true, 
            comparisons are case insensitive.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchData.ParameterKind">
            <summary>
            What kind of parameter is this? See <see cref="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ParameterTypeMatchData.ParameterKind"/> for available values.
            </summary>
            <value>The "parameterKind" config attribute.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PerformanceCounterCallHandlerData">
            <summary>
            A configuration element that stores information for the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PerformanceCounterCallHandlerData.#ctor">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PerformanceCounterCallHandlerData"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PerformanceCounterCallHandlerData.#ctor(System.String)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PerformanceCounterCallHandlerData"/>.
            </summary>
            <param name="instanceName">Name of the handler section.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PerformanceCounterCallHandlerData.#ctor(System.String,System.Int32)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PerformanceCounterCallHandlerData"/>.
            </summary>
            <param name="instanceName">Name of the handler section.</param>
            <param name="handlerOrder">Order of the handler.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PerformanceCounterCallHandlerData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented call handler by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PerformanceCounterCallHandlerData.CategoryName">
            <summary>
            Performance counter category name.
            </summary>
            <value>The "categoryName" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PerformanceCounterCallHandlerData.InstanceName">
            <summary>
            Performance counter instance name.
            </summary>
            <remarks>This string may include substitution tokens. See <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.MethodInvocationFormatter"/>
            for the list of tokens.</remarks>
            <value>The "instanceName" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PerformanceCounterCallHandlerData.UseTotalCounter">
            <summary>
            Increment "Total" counter instance.
            </summary>
            <value>The "useTotalCounter" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PerformanceCounterCallHandlerData.IncrementNumberOfCalls">
            <summary>
            Increment the "total # of calls" counter?
            </summary>
            <value>The "incrementNumberOfCalls" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PerformanceCounterCallHandlerData.IncrementCallsPerSecond">
            <summary>
            Increment the "calls / second" counter?
            </summary>
            <value>the "incrementCallsPerSecond" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PerformanceCounterCallHandlerData.IncrementAverageCallDuration">
            <summary>
            Increment "average seconds / call" counter?
            </summary>
            <value>The "incrementAverageCallDuration" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PerformanceCounterCallHandlerData.IncrementTotalExceptions">
            <summary>
            Increment "total # of exceptions" counter?
            </summary>
            <value>The "incrementTotalExceptions" config attribute.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PerformanceCounterCallHandlerData.IncrementExceptionsPerSecond">
            <summary>
            Increment the "exceptions / second" counter?
            </summary>
            <value>The "incrementExceptionsPerSecond" config attribute.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PolicyData">
            <summary>
            A <see cref="T:System.Configuration.ConfigurationElement"/> that maps the information about
            a policy from the configuration source.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PolicyData.#ctor(System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PolicyData"/> with the given name.
            </summary>
            <param name="policyName">Name of the policy.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PolicyData.#ctor">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PolicyData"/> with no name.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PolicyData.ConfigureContainer(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Adds the policy represented by the configuration object to a container.
            </summary>
            <param name="container">The container to add the policy to.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PolicyData.MatchingRules">
            <summary>
            Gets or sets the collection of matching rules from configuration.
            </summary>
            <value>The matching rule data collection.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PolicyData.Handlers">
            <summary>
            Get or sets the collection of handlers from configuration.
            </summary>
            <value>The handler data collection.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PolicyInjectionSettings">
            <summary>
            A <see cref="T:System.Configuration.ConfigurationSection"/> that stores the policy set in configuration.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PolicyInjectionSettings.SectionName">
            <summary>
            Section name as it appears in the configuration file.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PolicyInjectionSettings.OnDeserializeUnrecognizedElement(System.String,System.Xml.XmlReader)">
            <summary>
            Gets a value indicating whether an unknown element is encountered during deserialization.
            </summary>
            <param name="elementName">The name of the unknown subelement.</param>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> being used for deserialization.</param>
            <returns>true when an unknown element is encountered while deserializing; otherwise, false.</returns>
            <remarks>This class will ignore an element named "injectors".</remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PolicyInjectionSettings.ConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Adds to the <paramref name="container"/> the policy definitions available in the corresponding configuration section
            in <paramref name="configurationSource"/>, if such a section is available.
            </summary>
            <param name="container">The container on which the injection policies must be configured.</param>
            <param name="configurationSource">The configuration source from where the policy injection settings must be retrieved.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PolicyInjectionSettings.ConfigureContainer(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Adds to the <paramref name="container"/> the policy definitions represented in the configuration file.
            </summary>
            <param name="container">The container on which the injection policies must be configured.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PolicyInjectionSettings.Policies">
            <summary>
            Gets or sets the collection of Policies from configuration.
            </summary>
            <value>The <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PolicyData"/> collection.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchingRuleData">
            <summary>
            A configuration element class that stores configuration information for instances
            of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchingRuleData.#ctor">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchingRuleData"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchingRuleData.#ctor(System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Matching rule instance name in configuration.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchingRuleData.#ctor(System.String,System.Collections.Generic.IEnumerable{Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchData})">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Matching rule instance name in configuration.</param>
            <param name="matches">Collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchData"/> containing
            property patterns to match.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchingRuleData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented matching rule by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchingRuleData.Matches">
            <summary>
            The collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchData"/> containing property names to match.
            </summary>
            <value>The "matches" config subelement.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchData">
            <summary>
            A derived <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData"/> which adds storage for which methods
            on the property to match.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchData"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchData.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchData"/> class by using the specified pattern.
            </summary>
            <param name="match">The property name pattern to match. The rule will match both getter and setter methods of a property.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchData.#ctor(System.String,Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingOption)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchData"/> class by using the specified pattern and options.
            </summary>
            <param name="match">The property name pattern to match.</param>
            <param name="option">Which of the property methods to match. See <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingOption"/>
            for the valid options.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchData.#ctor(System.String,Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingOption,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchData"/> class by using the specified pattern and options.
            </summary>
            <param name="match">The property name pattern to match.</param>
            <param name="option">Which of the property methods to match. See <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingOption"/>
            for the valid options.</param>
            <param name="ignoreCase">If false, type name comparisons are case sensitive. If true, 
            comparisons are case insensitive.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PropertyMatchData.MatchOption">
            <summary>
            Which methods of the property to match. Default is to match both getters and setters.
            </summary>
            <value>The "matchOption" config attribute.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ReturnTypeMatchingRuleData">
            <summary>
            A configuration element that stores configuration information about an
            instance of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ReturnTypeMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ReturnTypeMatchingRuleData.#ctor">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ReturnTypeMatchingRuleData"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ReturnTypeMatchingRuleData.#ctor(System.String,System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ReturnTypeMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Matching rule instance name in configuration.</param>
            <param name="returnTypeName">Return type to match.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ReturnTypeMatchingRuleData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented matching rule by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.ReturnTypeMatchingRuleData.Match">
            <summary>
            
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TagAttributeMatchingRuleData">
            <summary>
            A configuration element that stores configuration information for
            an instance of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TagAttributeMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TagAttributeMatchingRuleData.#ctor">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TagAttributeMatchingRuleData"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TagAttributeMatchingRuleData.#ctor(System.String,System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TagAttributeMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Matching rule instance name in configuration.</param>
            <param name="tagToMatch">Tag string to match.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TagAttributeMatchingRuleData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented matching rule by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TagAttributeMatchingRuleData.Match">
            <summary>
            
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TypeMatchingRuleData">
            <summary>
            Configuration element that stores configuration information for
            an instance of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TypeMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TypeMatchingRuleData.#ctor">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TypeMatchingRuleData"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TypeMatchingRuleData.#ctor(System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TypeMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Matching rule instance name in configuration.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TypeMatchingRuleData.#ctor(System.String,System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TypeMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Matching rule instance name in configuration.</param>
            <param name="typeName">Type name to match.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TypeMatchingRuleData.#ctor(System.String,System.Collections.Generic.IEnumerable{Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData})">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TypeMatchingRuleData"/> instance.
            </summary>
            <param name="matchingRuleName">Matching rule instance name in configuration.</param>
            <param name="matches">Collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData"/> containing
            types to match. If any one matches, the rule matches.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TypeMatchingRuleData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented matching rule by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.TypeMatchingRuleData.Matches">
            <summary>
            The collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.MatchData"/> giving the types to match.
            </summary>
            <value>The "matches" configuration subelement.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Validation.Configuration.ValidationCallHandlerData">
            <summary>
            A configuration element class that stores the configuration data for
            the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.PolicyInjection.ValidationCallHandler"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Validation.Configuration.ValidationCallHandlerData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.Configuration.ValidationCallHandlerData"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Validation.Configuration.ValidationCallHandlerData.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.Configuration.ValidationCallHandlerData"/> class by using the specified handler name.
            </summary>
            <param name="handlerName">The name of the handler in the configuration.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Validation.Configuration.ValidationCallHandlerData.#ctor(System.String,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.Configuration.ValidationCallHandlerData"/> class by using the specified handler name and order.
            </summary>
            <param name="handlerName">The name of the handler in the configuration.</param>
            <param name="handlerOrder">The order of the handler in the configuration.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Validation.Configuration.ValidationCallHandlerData.DoConfigureContainer(Microsoft.Practices.Unity.IUnityContainer,System.String)">
            <summary>
            Configures an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to resolve the represented call handler by using the specified name.
            </summary>
            <param name="container">The container to configure.</param>
            <param name="registrationName">The name of the registration.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Validation.Configuration.ValidationCallHandlerData.RuleSet">
            <summary>
            Gets The rule set name to use for all types.
            </summary>
            <remarks>
            An empty string specifies the default rule set. 
            </remarks>
            <value>The "ruleSet" configuration property.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Validation.Configuration.ValidationCallHandlerData.SpecificationSource">
            <summary>
            SpecificationSource (Both | Attributes | Configuration) : Where to look for validation rules. Default is Both.
            </summary>
            <value>The "specificationSource" configuration attribute.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Installers.PerformanceCountersInstaller">
            <summary>
            An <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.CallHandlers.PerformanceCounterCallHandler"/> class that can be used by installutil.exe to install
            the performance counter categories updated by the <see cref="T:System.Configuration.Install.Installer"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Installers.PerformanceCountersInstaller.#ctor">
            <summary>
            Create the installer with an empty list of categories.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Installers.PerformanceCountersInstaller.#ctor(System.String[])">
            <summary>
            Create the installer class with the given list of categories.
            </summary>
            <param name="categoryNames">The set of categories.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Installers.PerformanceCountersInstaller.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Create the installer class, reading the categories from a policy set
            configured in the given <paramref name="configurationSource"/>.
            </summary>
            <param name="configurationSource">Configuration source containing the policy set.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Installers.PerformanceCountersInstaller.OnBeforeInstall(System.Collections.IDictionary)">
            <summary>
            Raises the <see cref="E:System.Configuration.Install.Installer.BeforeInstall"></see> event.
            </summary>
            
            <param name="savedState">An <see cref="T:System.Collections.IDictionary"></see> that contains the state of the computer before the installers in the <see cref="P:System.Configuration.Install.Installer.Installers"></see> property are installed. This <see cref="T:System.Collections.IDictionary"></see> object should be empty at this point. </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Installers.PerformanceCountersInstaller.OnBeforeUninstall(System.Collections.IDictionary)">
            <summary>
            Raises the <see cref="E:System.Configuration.Install.Installer.BeforeUninstall"></see> event.
            </summary>
            
            <param name="savedState">An <see cref="T:System.Collections.IDictionary"></see> that contains the state of the computer before the installers in the <see cref="P:System.Configuration.Install.Installer.Installers"></see> property uninstall their installations. </param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.InstanceInterceptionPolicySettingInjectionMember">
            <summary>
             Sets the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor"/> on the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptionPolicy"/> for
             a type in the <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.InstanceInterceptionPolicySettingInjectionMember.#ctor(Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor)">
            <summary>
             Initializes the InstanceInterceptionPolicySettingInjectionMember with the provided interceptor.
            </summary>
            <param name="interceptor">The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor"/> to set on the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptionPolicy"/></param>
            <exception cref="T:System.ArgumentNullException">A valid interceptor is required</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.InstanceInterceptionPolicySettingInjectionMember.AddPolicies(System.Type,System.Type,System.String,Microsoft.Practices.ObjectBuilder2.IPolicyList)">
            <summary>
            Add policies to the <paramref name="policies"/> to configure the container with 
            an appropriate <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptionPolicy"/>
            </summary>
            <param name="serviceType">Type of the interface being registered. This parameter is
            ignored by this class.</param>
            <param name="implementationType">Type to register.</param>
            <param name="name">Name used to resolve the type object.</param>
            <param name="policies">Policy list to add policies to.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.InstanceInterceptionPolicySettingInjectionMember.Interceptor">
            <summary>
             The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor"/> set on the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptionPolicy"/>
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.AssemblyMatchingRule">
            <summary>
            Placeholder for <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.AssemblyMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.AssemblyMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Tests to see if this rule applies to the given member.
            </summary>
            <remarks>
            This type is available to support the configuration subsystem. Use 
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.AssemblyMatchingRule"/> instead.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.CustomAttributeMatchingRule">
            <summary>
            Placeholder for <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.CustomAttributeMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.CustomAttributeMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Tests to see if this rule applies to the given member.
            </summary>
            <remarks>
            This type is available to support the configuration subsystem. Use 
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.CustomAttributeMatchingRule"/> instead.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.MemberNameMatchingRule">
            <summary>
            Placeholder for <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MemberNameMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.MemberNameMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Tests to see if this rule applies to the given member.
            </summary>
            <remarks>
            This type is available to support the configuration subsystem. Use 
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MemberNameMatchingRule"/> instead.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.MethodSignatureMatchingRule">
            <summary>
            Placeholder for <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MethodSignatureMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.MethodSignatureMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Tests to see if this rule applies to the given member.
            </summary>
            <remarks>
            This type is available to support the configuration subsystem. Use 
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MethodSignatureMatchingRule"/> instead.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.NamespaceMatchingRule">
            <summary>
            Placeholder for <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.NamespaceMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.NamespaceMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Tests to see if this rule applies to the given member.
            </summary>
            <remarks>
            This type is available to support the configuration subsystem. Use 
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.NamespaceMatchingRule"/> instead.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.ParameterTypeMatchingRule">
            <summary>
            Placeholder for <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.ParameterTypeMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Tests to see if this rule applies to the given member.
            </summary>
            <remarks>
            This type is available to support the configuration subsystem. Use 
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingRule"/> instead.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.PropertyMatchingRule">
            <summary>
            Placeholder for <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.PropertyMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Tests to see if this rule applies to the given member.
            </summary>
            <remarks>
            This type is available to support the configuration subsystem. Use 
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingRule"/> instead.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.ReturnTypeMatchingRule">
            <summary>
            Placeholder for <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ReturnTypeMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.ReturnTypeMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Tests to see if this rule applies to the given member.
            </summary>
            <remarks>
            This type is available to support the configuration subsystem. Use 
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ReturnTypeMatchingRule"/> instead.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.TagAttributeMatchingRule">
            <summary>
            Placeholder for <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TagAttributeMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.TagAttributeMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Tests to see if this rule applies to the given member.
            </summary>
            <remarks>
            This type is available to support the configuration subsystem. Use 
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TagAttributeMatchingRule"/> instead.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.TypeMatchingRule">
            <summary>
            Placeholder for <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TypeMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.MatchingRules.TypeMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Tests to see if this rule applies to the given member.
            </summary>
            <remarks>
            This type is available to support the configuration subsystem. Use 
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TypeMatchingRule"/> instead.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.MethodInvocationFormatter">
            <summary>
            Represents a formatter object that allows replacement tokens in a string.
            The supported tokens are:
            <list type="bullet">
            <item><term>{appdomain}</term><description>Includes the friendly name of the current application domain.</description></item>
            <item><term>{assembly}</term><description>Includes the assembly name.</description></item>
            <item><term>{namespace}</term><description>Includes the namespace of the target class.</description></item>
            <item><term>{type}</term><description>Includes the name of the type that contains the target method.</description></item>
            <item><term>{method}</term><description>Includes the name of the target method.</description></item>
            </list>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.MethodInvocationFormatter.#ctor(Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.MethodInvocationFormatter"/> class that replaces tokens
            using the information in the given method invocation.
            </summary>
            <param name="input"><see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation"/> object that contains information
            about the current method call.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjection">
            <summary>
            A static facade class that provides the main entry point into the
            Policy Injection Application Block. Methods on this class
            create intercepted objects, or wrap existing instances with
            interceptors.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjection.Create``1(System.Object[])">
            <summary>
            Creates a new object of type <typeparamref name="TObject"/> and
            adds interception as needed to match the policies specified in
            the default policy configuration.
            </summary>
            <typeparam name="TObject">Type of object to create.</typeparam>
            <param name="args">Arguments to pass to the <typeparamref name="TObject"/> constructor.</param>
            <returns>The intercepted object (or possibly a raw instance if no policies apply).</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjection.Create``2(System.Object[])">
            <summary>
            Creates a new object of type <typeparamref name="TObject"/> and
            adds interception as needed to match the policies specified in
            the default policy configuration.
            </summary>
            <typeparam name="TObject">Concrete object type to create.</typeparam>
            <typeparam name="TInterface">Type of reference to return. Must be an interface the object implements.</typeparam>
            <param name="args">Arguments to pass to the <typeparamref name="TObject"/> constructor.</param>
            <returns>The intercepted object (or possibly a raw instance if no policies apply).</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjection.Create(System.Type,System.Object[])">
            <summary>
            Creates a new object of type <paramref name="typeToCreate"/> and
            adds interception as needed to match the policies specified in
            the default policy configuration.
            </summary>
            <param name="typeToCreate">Type of object to create.</param>
            <param name="args">Arguments to pass to the <paramref name="typeToCreate"/> constructor.</param>
            <returns>The intercepted object (or possibly a raw instance if no policies apply).</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjection.Create(System.Type,System.Type,System.Object[])">
            <summary>
            Creates a new object of type <paramref name="typeToCreate"/> and
            adds interception as needed to match the policies specified in
            the default policy configuration.
            </summary>
            <param name="typeToCreate">Concrete object type to create.</param>
            <param name="typeToReturn">Type of reference to return. Must be an interface the object implements.</param>
            <param name="args">Arguments to pass to the <paramref name="typeToCreate"/> constructor.</param>
            <returns>The intercepted object (or possibly a raw instance if no policies apply).</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjection.Wrap``1(System.Object)">
            <summary>
            Creates a proxy for the given object that adds interception policies as
            defined in the default configuration source.
            </summary>
            <remarks>
            Despite the name of the <typeparamref name="TInterface"/> parameter, this
            may be any type that the instance is assignable to, including both interfaces
            that it implements and the concrete type of the object.
            </remarks>
            <typeparam name="TInterface">Type of the proxy to return.</typeparam>
            <param name="instance">Instance object to wrap.</param>
            <returns>The proxy for the instance, or the raw object if no policies apply.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjection.Wrap(System.Type,System.Object)">
            <summary>
            Creates a proxy for the given object that adds interception policies as
            defined in the default configuration source.
            </summary>
            <param name="typeToReturn">Type of the proxy to return.</param>
            <param name="instance">Instance object to wrap.</param>
            <returns>The proxy for the instance, or the raw object if no policies apply.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjection.SetPolicyInjector(Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjector,System.Boolean)">
            <summary>
            Sets the policy injector for the static facade.
            </summary>
            <param name="policyInjector">The policy injector.</param>
            <param name="throwIfSet"><see langword="true"/> to throw an exception if the policy injector is already set; otherwise, <see langword="false"/>. Defaults to <see langword="true"/>.</param>
            <exception cref="T:System.InvalidOperationException">The policy injector is already set and <paramref name="throwIfSet"/> is <see langword="true"/>.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjection.Reset">
            <summary>
            Resets the policy injector for the static facade.
            </summary>
            <remarks>
            Used for tests.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjector">
            <summary>
            A non-static facade class that provides the main entry point into the
            Policy Injection Application Block. Methods on this class
            create intercepted objects, or wrap existing instances with
            interceptors.
            </summary>
            <remarks>
            <para>
            This facade can be initialized with either an <see cref="T:Microsoft.Practices.ServiceLocation.IServiceLocator"/> or an 
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/>. In the latter case, a new container will be created and it will be disposed
            when the policy injector is disposed.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjector.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjector"/> class with the specified configuration source.
            </summary>
            <param name="configurationSource">The configuration source from which to retrieve configuration information.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="configurationSource"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjector.#ctor(Microsoft.Practices.ServiceLocation.IServiceLocator)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjector"/> class with the specified service locator.
            </summary>
            <param name="serviceLocator">The service locator from which an <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> can be resolved
            to perform interception.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="serviceLocator"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.InvalidOperationException">An <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> cannot be resolved from the 
            <paramref name="serviceLocator"/>, or the resolved container does not have the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Interception"/>
            extension.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjector.#ctor(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjector"/> class with the specified container.
            </summary>
            <param name="container">The container to perform interception.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="container"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.InvalidOperationException">The container does not have the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Interception"/>
            extension.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjector.Wrap``1(System.Object)">
            <summary>
            Creates a proxy for the given object that adds interception policies.
            </summary>
            <remarks>
            Despite the name of the <typeparamref name="TInterface"/> parameter, this
            may be any type that the instance is assignable to, including both interfaces
            that it implements and the concrete type of the object.
            </remarks>
            <typeparam name="TInterface">Type of the proxy to return.</typeparam>
            <param name="instance">Instance object to wrap.</param>
            <returns>The proxy for the instance, or the raw object if no policies apply.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjector.Wrap(System.Type,System.Object)">
            <summary>
            Creates a proxy for the given object that adds interception policies.
            </summary>
            <param name="typeToReturn">Type of the proxy to return.</param>
            <param name="instance">Instance object to wrap.</param>
            <returns>The proxy for the instance, or the raw object if no policies apply.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjector.Create``1(System.Object[])">
            <summary>
            Creates a new object of type <typeparamref name="TObject"/> and
            adds interception as needed to match the policies specified for the injector.
            </summary>
            <typeparam name="TObject">Type of object to create.</typeparam>
            <param name="args">Arguments to pass to the <typeparamref name="TObject"/> constructor.</param>
            <returns>The intercepted object (or possibly a raw instance if no policies apply).</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjector.Create``2(System.Object[])">
            <summary>
            Creates a new object of type <typeparamref name="TObject"/> and
            adds interception as needed to match the policies specified for the injector.
            </summary>
            <typeparam name="TObject">Concrete object type to create.</typeparam>
            <typeparam name="TInterface">Type of reference to return. Must be an interface the object implements.</typeparam>
            <param name="args">Arguments to pass to the <typeparamref name="TObject"/> constructor.</param>
            <returns>The intercepted object (or possibly a raw instance if no policies apply).</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjector.Create(System.Type,System.Object[])">
            <summary>
            Creates a new object of type <paramref name="typeToCreate"/> and
            adds interception as needed to match the policies specified for the injector.
            </summary>
            <param name="typeToCreate">Type of object to create.</param>
            <param name="args">Arguments to pass to the <paramref name="typeToCreate"/> constructor.</param>
            <returns>The intercepted object (or possibly a raw instance if no policies apply).</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjector.Create(System.Type,System.Type,System.Object[])">
            <summary>
            Creates a new object of type <paramref name="typeToCreate"/> and
            adds interception as needed to match the policies specified for the injector.
            </summary>
            <param name="typeToCreate">Concrete object type to create.</param>
            <param name="typeToReturn">Type of reference to return. Must be an interface the object implements.</param>
            <param name="args">Arguments to pass to the <paramref name="typeToCreate"/> constructor.</param>
            <returns>The intercepted object (or possibly a raw instance if no policies apply).</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjector.Dispose">
            <summary>
            Dispose this policy injector.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.PolicyInjector.Dispose(System.Boolean)">
            <summary>
            Dispose this policy injector.
            </summary>
            <param name="disposing"><see langword="true"/> if being called from the IDisposable.Dispose method, 
            <see langword="false"/> if being called from a finalizer.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.AverageCallDurationBaseCounterHelp">
            <summary>
              Looks up a localized string similar to Base counter used to calculate average call time..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.AverageCallDurationCounterHelp">
            <summary>
              Looks up a localized string similar to Tracks the average length of time for a monitored method to complete..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.CallsPerSecondCounterHelp">
            <summary>
              Looks up a localized string similar to Tracks calls / second to the monitored methods..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.CouldNotFindPolicyInConfiguration">
            <summary>
              Looks up a localized string similar to The configuration information for Policy &apos;{0}&apos; can not be found in the configuration source.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.ExceptionAttributeNoSubclassOfAttribute">
            <summary>
              Looks up a localized string similar to Type must be a subclass of System.Attribute..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.ExceptionPolicyInjectorAlreadySet">
            <summary>
              Looks up a localized string similar to The PolicyInjector is already set..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.ExceptionPolicyInjectorNotSet">
            <summary>
              Looks up a localized string similar to The PolicyInjector has not been set for the PolicyInjection static class. Set it invoking the PolicyInjection.SetPolicyInjector method...
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.ExceptionsPerSecondCounterHelp">
            <summary>
              Looks up a localized string similar to Tracks the number of exceptions / second thrown by the monitored methods..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.InterceptionNotSupported">
            <summary>
              Looks up a localized string similar to The type {0} is not interceptable..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.NoCategoryErrorMessage">
            <summary>
              Looks up a localized string similar to You must specify the /category=&lt;name&gt;(;&lt;name&gt;) switch on the command line..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.NoHandlersInPolicy">
            <summary>
              Looks up a localized string similar to The policy {0} does not have any handlers..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.NumberOfCallsCounterHelp">
            <summary>
              Looks up a localized string similar to Records the total number of calls to the monitored methods..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.NumberOfExceptionsCounterHelp">
            <summary>
              Looks up a localized string similar to Records the total number of exceptions thrown by the monitored methods..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.PerformanceCounterCategoryHelp">
            <summary>
              Looks up a localized string similar to Performance counters that are updated by the Policy Injection Application Block..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.PlaceholderRule">
            <summary>
              Looks up a localized string similar to This rule is not intended to be used directly. Use the rules from the Microsoft.Practices.Unity.Interception assembly instead..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.PolicyInjectionSectionNotFound">
            <summary>
              Looks up a localized string similar to The configuration section for Policy Injection cannot be found in the configuration source..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Properties.Resources.TracePoliciesApplied">
            <summary>
              Looks up a localized string similar to Injection Policies applied to object..
            </summary>
        </member>
    </members>
</doc>
