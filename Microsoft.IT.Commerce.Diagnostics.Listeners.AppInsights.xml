<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.IT.Commerce.Diagnostics.Listeners.AppInsights</name>
    </assembly>
    <members>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.Listeners.AppInsights.AppInsightsTraceListener">
            <summary>
            An ECIT trace listener that writes the logging information to the Application Insights repository.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.AppInsights.AppInsightsTraceListener.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.Listeners.AppInsights.AppInsightsTraceListener" /> class.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.AppInsights.AppInsightsTraceListener.#ctor(Microsoft.IT.Commerce.Diagnostics.Listeners.Common.Settings.DiagnosticsAppInsightsSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.Listeners.AppInsights.AppInsightsTraceListener" /> class.
            </summary>
            <param name="configEntity">The configuration entity.</param>
        </member>
        <member name="P:Microsoft.IT.Commerce.Diagnostics.Listeners.AppInsights.AppInsightsTraceListener.IsThreadSafe">
            <summary>
            Gets a value indicating whether the trace listener is thread safe.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.AppInsights.AppInsightsTraceListener.Write(System.String)">
            <summary>
            Writes the specified message to the listener.
            </summary>
            <param name="message">A message to write.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.AppInsights.AppInsightsTraceListener.WriteLine(System.String)">
            <summary>
            Writes a message to the listener followed by a line terminator.
            </summary>
            <param name="message">A message to write.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.AppInsights.AppInsightsTraceListener.TraceData(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.Object)">
            <summary>
            Writes trace information, a data object and event information to the listener specific output.
            </summary>
            <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
            <param name="source">A name used to identify the output, typically the name of the application that generated the trace event.</param>
            <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values specifying the type of event that has caused the trace.</param>
            <param name="id">A numeric identifier for the event.</param>
            <param name="data">The trace data to emit.</param>
            <PermissionSet>
              <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
              <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
            </PermissionSet>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.AppInsights.AppInsightsTraceListener.SendLogEntryToAppInsights(Microsoft.Practices.EnterpriseLibrary.Logging.LogEntry)">
            <summary>
            Sends the log entry to Application Insights
            </summary>
            <param name="logEntry">The log.</param>
            <exception cref="T:System.InvalidOperationException">An error was detected while attempting to send to Application Insights</exception>
        </member>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.Listeners.AppInsights.ClaimsAppInsightsTelemetryInitializer">
            <summary>
            Represents an object that implements supporting logic for user claims using <see cref="T:Microsoft.ApplicationInsights.DataContracts.TelemetryContext"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.AppInsights.ClaimsAppInsightsTelemetryInitializer.Initialize(Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Initializes properties of the specified <see cref="T:Microsoft.ApplicationInsights.Channel.ITelemetry" /> object.
            </summary>
            <param name="telemetry"></param>
        </member>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.Listeners.AppInsights.DiagnosticsAppInsightsTelemetryInitializer">
            <summary>
            Represents an object that implements supporting logic for <see cref="T:Microsoft.ApplicationInsights.DataContracts.TelemetryContext"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.AppInsights.DiagnosticsAppInsightsTelemetryInitializer.Initialize(Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Initializes the <see cref="T:Microsoft.ApplicationInsights.DataContracts.TelemetryContext" /> with diagnostics settings and common application properties.
            </summary>
            <param name="telemetry">The telemetry which holds the context.</param>
        </member>
    </members>
</doc>
