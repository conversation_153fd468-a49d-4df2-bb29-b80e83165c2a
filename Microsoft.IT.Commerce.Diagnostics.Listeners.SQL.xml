<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.IT.Commerce.Diagnostics.Listeners.SQL</name>
    </assembly>
    <members>
        <member name="T:Microsoft.IT.Commerce.Diagnostics.Listeners.SQL.BatchDatabaseTraceListener">
            <summary>
            Database trace listener which inserts asynchronously in batches.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.SQL.BatchDatabaseTraceListener.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.Listeners.SQL.BatchDatabaseTraceListener"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.SQL.BatchDatabaseTraceListener.#ctor(Microsoft.IT.Commerce.Diagnostics.Listeners.Common.Settings.DiagnosticsSqlSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IT.Commerce.Diagnostics.Listeners.SQL.BatchDatabaseTraceListener"/> class.
            </summary>
            <param name="configEntity">The configuration entity.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.SQL.BatchDatabaseTraceListener.Write(System.String)">
            <summary>
            When overridden in a derived class, writes the specified message to the listener you create in the derived class.
            </summary>
            <param name="message">A message to write.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.SQL.BatchDatabaseTraceListener.WriteLine(System.String)">
            <summary>
            When overridden in a derived class, writes a message to the listener you create in the derived class, followed by a line terminator.
            </summary>
            <param name="message">A message to write.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.SQL.BatchDatabaseTraceListener.TraceData(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.Object)">
            <summary>
            Writes trace information, a data object and event information to the listener specific output.
            </summary>
            <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache"/> object that contains the current process ID, thread ID, and stack trace information.</param>
            <param name="source">A name used to identify the output, typically the name of the application that generated the trace event.</param>
            <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType"/> values specifying the type of event that has caused the trace.</param>
            <param name="id">A numeric identifier for the event.</param>
            <param name="data">The trace data to emit.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.SQL.BatchDatabaseTraceListener.BeginSendingToDatabase">
            <summary>
            Begins sending the log entries in batches to the database.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.SQL.BatchDatabaseTraceListener.sendTimer_Elapsed(System.Object,System.Timers.ElapsedEventArgs)">
            <summary>
            Handles the Elapsed event of the sendTimer control.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="e">The <see cref="T:System.Timers.ElapsedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.SQL.BatchDatabaseTraceListener.ProcessBatch(System.Collections.Concurrent.ConcurrentQueue{Microsoft.IT.Commerce.Diagnostics.Listeners.Common.Entities.DiagnosticsEntityBase},System.Security.SecureString,System.Int32,Microsoft.Practices.EnterpriseLibrary.TransientFaultHandling.RetryPolicy)">
            <summary>
            Processes one batch of the log entry queue.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.SQL.BatchDatabaseTraceListener.FillDataTables(System.Collections.Concurrent.ConcurrentQueue{Microsoft.IT.Commerce.Diagnostics.Listeners.Common.Entities.DiagnosticsEntityBase},System.Int32,System.Data.DataTable,System.Data.DataTable,System.Data.DataTable,System.Data.DataTable)">
            <summary>
            Fills the data tables with a single batch size of log entries.
            </summary>
        </member>
        <member name="M:Microsoft.IT.Commerce.Diagnostics.Listeners.SQL.BatchDatabaseTraceListener.SendLogsToDatabase(System.Security.SecureString,Microsoft.Practices.EnterpriseLibrary.TransientFaultHandling.RetryPolicy,System.Data.DataTable,System.Data.DataTable,System.Data.DataTable,System.Data.DataTable)">
            <summary>
            Sends the log entries in the data tables to the database.
            </summary>
            <param name="connectionString">The connection string.</param>
            <param name="retryPolicy">The retry policy.</param>
            <param name="eventTable">The event table.</param>
            <param name="timingTable">The timing table.</param>
            <param name="milestoneTable">The milestone table.</param>
            <param name="auditTable">The audit table.</param>
        </member>
    </members>
</doc>
